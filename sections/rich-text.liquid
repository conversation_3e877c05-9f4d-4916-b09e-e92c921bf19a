{% comment %}
  bs-add
  - hide/show option added
  - added color scheme options with gradient option (section and content level)
  - added custom liquid block
  - added padding options mobile/desktop
  - max width for content mobile/desktop
  - ability to indent for text mobile/desktop
  - added button scheme options
  - ability to adjust width by 1/3 and 1/2
  - support for popup block
{% endcomment %}

{{ 'section-rich-text.css' | asset_url | stylesheet_tag }}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic', tag_name: 'content' %}

  {% if section.settings.content_width contains 'custom-width' %}
    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-content-width {
        max-width: {{ section.settings.content_max_width_tablet }}px;
      }
    }
    @media screen and (min-width: 990px) {
      .section-{{ section.id }}-content-width {
        max-width: {{ section.settings.content_max_width_desktop }}px;
      }
    }
  {% endif %}
{%- endstyle -%}

<div class="{{ section.settings.custom_css_class }}">
  <div class="color-{{ section.settings.color_scheme }} gradient">
    <div class="isolate {{ section.settings.content_width }}">
      <div class="rich-text content-container {{ section.settings.desktop_content_position }}">
        <div class="{% if section.settings.show_color_scheme_content %}color-{{ section.settings.color_scheme_content }} gradient{% endif %}">
          <div class="content-{{ section.id }}-padding">
            <div class="rich-text__wrapper">
              <div class="rich-text__blocks content--desktop-{{ section.settings.content_alignment }} content--mobile-{{ section.settings.mobile_content_alignment | default: section.settings.content_alignment }} section-{{ section.id }}-content-width">
                {%- for block in section.blocks -%}
                  {%- case block.type -%}
                    {%- when '@app' -%}
                      {% render block %}
                    {%- when 'custom_liquid' -%}
                      {{ block.settings.custom_liquid }}
                    {%- when 'heading' -%}
                      <h2
                        class="rich-text__heading rte inline-richtext {{ block.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %} {{ block.settings.text_indent }}"
                        {{ block.shopify_attributes }}
                        {% if settings.animations_reveal_on_scroll %}
                          data-cascade
                          style="--animation-order: {{ forloop.index }};"
                        {% endif %}
                      >
                        {{ block.settings.heading }}
                      </h2>
                    {%- when 'caption' -%}
                      <p
                        class="rich-text__caption {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                        {{ block.shopify_attributes }}
                        {% if settings.animations_reveal_on_scroll %}
                          data-cascade
                          style="--animation-order: {{ forloop.index }};"
                        {% endif %}
                      >
                        {{ block.settings.caption | escape }}
                      </p>
                    {%- when 'text' -%}
                      <div
                        class="rich-text__text rte{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %} {{ block.settings.text_indent }}"
                        {{ block.shopify_attributes }}
                        {% if settings.animations_reveal_on_scroll %}
                          data-cascade
                          style="--animation-order: {{ forloop.index }};"
                        {% endif %}
                      >
                        {{ block.settings.text }}
                      </div>
                    {%- when 'button' -%}
                      {%- liquid
                        assign button_label_content = block.settings.button_label_liquid | default: block.settings.button_label
                        assign button_label_2_content = block.settings.button_label_2_liquid | default: block.settings.button_label_2
                      -%}

                      <div
                        class="rich-text__buttons{% if button_label_content != blank and button_label_2_content != blank %} rich-text__buttons--multiple{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                        {{ block.shopify_attributes }}
                        {% if settings.animations_reveal_on_scroll %}
                          data-cascade
                          style="--animation-order: {{ forloop.index }};"
                        {% endif %}
                      >
                        {%- if button_label_content != blank -%}
                          <a
                            {% if block.settings.button_link == blank %}
                              role="link" aria-disabled="true"
                            {% else %}
                              href="{{ block.settings.button_link }}"
                            {% endif %}
                            class="{{ block.settings.button_style }}"
                          >
                            {{- button_label_content -}}
                          </a>
                        {%- endif -%}
                        {%- if button_label_2_content != blank -%}
                          <a
                            {% if block.settings.button_link_2 == blank %}
                              role="link" aria-disabled="true"
                            {% else %}
                              href="{{ block.settings.button_link_2 }}"
                            {% endif %}
                            class="{{ block.settings.button_style_2 }}"
                          >
                            {{- button_label_2_content -}}
                          </a>
                        {%- endif -%}
                      </div>
                      {%- comment -%}
                        bs-add
                          - support for popups
                          - outside div needed for alignment
                      {%- endcomment -%}
                    {%- when 'popup' -%}
                      <div>
                        {{ 'section-main-product.css' | asset_url | stylesheet_tag }}
                        <modal-opener
                          class="product-popup-modal__opener quick-add-hidden"
                          data-modal="#PopupModal-{{ block.id }}"
                          {{ block.shopify_attributes }}
                        >
                          <button
                            id="ProductPopup-{{ block.id }}"
                            class="{% if block.settings.button_style contains 'link' %}product-popup-modal__button{% endif %} {{ block.settings.button_style }}"
                            style="{% if block.settings.button_style contains 'link' %}min-height: auto;padding-right: 0;text-decoration: underline;{% endif %}"
                            type="button"
                            aria-haspopup="dialog"
                          >
                            {{ block.settings.text_liquid | default: block.settings.text }}
                          </button>
                        </modal-opener>
                      </div>
                  {%- endcase -%}
                {%- endfor -%}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{%- comment -%}
  bs-add
    - support for popups
{%- endcomment -%}
{% assign popups = section.blocks | where: 'type', 'popup' %}
{%- for block in popups -%}
  <modal-dialog
    id="PopupModal-{{ block.id }}"
    class="product-popup-modal color-{{ block.settings.color_scheme }} gradient {{ section.settings.custom_css_class }}"
    {{ block.shopify_attributes }}
  >
    <div
      role="dialog"
      aria-label="{{ block.settings.text_liquid | default: block.settings.text | strip_html | strip }}"
      aria-modal="true"
      class="product-popup-modal__content"
      tabindex="-1"
    >
      <button
        id="ModalClose-{{ block.id }}"
        type="button"
        class="product-popup-modal__toggle"
        aria-label="{{ 'accessibility.close' | t }}"
      >
        {{- 'icon-close.svg' | inline_asset_content -}}
      </button>
      <div class="product-popup-modal__content-info">
        <h3 class="h2">{{ block.settings.title | default: block.settings.page.title }}</h3>
        {{ block.settings.content_liquid | default: block.settings.page.content }}
      </div>
    </div>
  </modal-dialog>
{%- endfor -%}

{{ section.settings.custom_liquid }}

{% schema %}
{
  "name": "t:sections.rich-text.name",
  "tag": "section",
  "class": "section section--rich-text",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "Basic settings"
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "full-width",
          "label": "Full width"
        },
        {
          "value": "left-one-third",
          "label": "Left one third"
        },
        {
          "value": "left-one-half",
          "label": "Left one half"
        },
        {
          "value": "left-two-thirds",
          "label": "Left two thirds"
        },
        {
          "value": "right-one-third",
          "label": "Right one third"
        },
        {
          "value": "right-one-half",
          "label": "Right one half"
        },
        {
          "value": "right-two-thirds",
          "label": "Right two thirds"
        }
      ],
      "default": "full-width",
      "label": "t:sections.rich-text.settings.desktop_content_position.label",
      "info": "t:sections.rich-text.settings.desktop_content_position.info"
    },
    {
      "type": "select",
      "id": "content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.rich-text.settings.content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.rich-text.settings.content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.rich-text.settings.content_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.rich-text.settings.content_alignment.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "Padding top (mobile)",
      "default": 20
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 400,
      "step": 5,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "Padding bottom (mobile)",
      "default": 20
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 400,
      "step": 5,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 40
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "page",
      "id": "page",
      "label": "Linked Page",
      "info": "Allows page linking to access metafields in unique cases (e.g. home page)."
    },
    {
      "type": "header",
      "content": "Visibility"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Section Colors"
    },
    {
      "type": "header",
      "content": "Content Colors"
    },
    {
      "type": "checkbox",
      "id": "show_color_scheme_content",
      "default": false,
      "label": "Show content color scheme",
      "info": "If unchecked, it will default to section color scheme"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_content",
      "label": "Content color scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "content_width",
      "options": [
        {
          "value": "full-width",
          "label": "Full width on all devices"
        },
        {
          "value": "page-width",
          "label": "Page width (based on theme settings)"
        },
        {
          "value": "page-width page-width--narrow",
          "label": "Page width narrow (tablet and desktop only)"
        },
        {
          "value": "page-width custom-width",
          "label": "Custom tablet & desktop / Page width mobile"
        }
      ],
      "default": "page-width",
      "label": "Content width"
    },
    {
      "type": "range",
      "id": "content_max_width_tablet",
      "min": 100,
      "max": 900,
      "step": 50,
      "unit": "px",
      "label": "Content max width (tablet)",
      "info": "Only applicable if page-width is set to custom",
      "default": 900
    },
    {
      "type": "range",
      "id": "content_max_width_desktop",
      "min": 100,
      "max": 1600,
      "step": 50,
      "unit": "px",
      "label": "Content max width",
      "info": "Only applicable if page-width is set to custom",
      "default": 1600
    },
    {
      "type": "header",
      "content": "Alignment"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "",
          "label": "Inherit"
        },
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "",
      "label": "Content alignment (mobile)"
    },
    {
      "type": "header",
      "content": "Section Padding (horizontal)"
    },
    {
      "type": "range",
      "id": "padding_left_mobile",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding left (mobile)",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_left_desktop",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding left",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_right_mobile",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding right (mobile)",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_right_desktop",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding right",
      "default": 0
    },
    {
      "type": "header",
      "content": "Size guide"
    },
    {
      "type": "paragraph",
      "content": "Mobile (max-width: 749px)"
    },
    {
      "type": "paragraph",
      "content": "Tablet (min-width: 750px)"
    },
    {
      "type": "paragraph",
      "content": "Desktop (min-width: 990px)"
    },
    {
      "type": "header",
      "content": "Custom overrides"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "paragraph",
      "content": "Use this for section id: #shopify-section-{{ section.id }}"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "heading",
      "name": "t:sections.rich-text.blocks.heading.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "t:sections.rich-text.blocks.heading.settings.heading.default",
          "label": "t:sections.rich-text.blocks.heading.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__h6.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__h5.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__h4.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__h3.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "hxl",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "hxxl",
              "label": "t:sections.all.heading_size.options__5.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        },
        {
          "type": "select",
          "id": "text_indent",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "indent--mobile",
              "label": "Mobile only"
            },
            {
              "value": "indent--desktop",
              "label": "Desktop only"
            },
            {
              "value": "indent--mobile indent--desktop",
              "label": "Both"
            }
          ],
          "default": "",
          "label": "Text indent"
        }
      ]
    },
    {
      "type": "caption",
      "name": "t:sections.rich-text.blocks.caption.name",
      "settings": [
        {
          "type": "text",
          "id": "caption",
          "default": "t:sections.rich-text.blocks.caption.settings.text.default",
          "label": "t:sections.rich-text.blocks.caption.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "subtitle",
              "label": "t:sections.rich-text.blocks.caption.settings.text_style.options__1.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.rich-text.blocks.caption.settings.text_style.options__2.label"
            }
          ],
          "default": "caption-with-letter-spacing",
          "label": "t:sections.rich-text.blocks.caption.settings.text_style.label"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "small",
              "label": "t:sections.rich-text.blocks.caption.settings.caption_size.options__1.label"
            },
            {
              "value": "medium",
              "label": "t:sections.rich-text.blocks.caption.settings.caption_size.options__2.label"
            },
            {
              "value": "large",
              "label": "t:sections.rich-text.blocks.caption.settings.caption_size.options__3.label"
            }
          ],
          "default": "medium",
          "label": "t:sections.rich-text.blocks.caption.settings.caption_size.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.rich-text.blocks.text.name",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "t:sections.rich-text.blocks.text.settings.text.default",
          "label": "t:sections.rich-text.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_indent",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "indent--mobile",
              "label": "Mobile only"
            },
            {
              "value": "indent--desktop",
              "label": "Desktop only"
            },
            {
              "value": "indent--mobile indent--desktop",
              "label": "Both"
            }
          ],
          "default": "",
          "label": "Text indent"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.rich-text.blocks.buttons.name",
      "limit": 2,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "t:sections.rich-text.blocks.buttons.settings.button_label_1.default",
          "label": "t:sections.rich-text.blocks.buttons.settings.button_label_1.label",
          "info": "t:sections.rich-text.blocks.buttons.settings.button_label_1.info"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.rich-text.blocks.buttons.settings.button_link_1.label"
        },
        {
          "type": "select",
          "id": "button_style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "button button--primary",
          "label": "First button style"
        },
        {
          "type": "text",
          "id": "button_label_2",
          "label": "t:sections.rich-text.blocks.buttons.settings.button_label_2.label",
          "info": "t:sections.rich-text.blocks.buttons.settings.button_label_2.info"
        },
        {
          "type": "url",
          "id": "button_link_2",
          "label": "t:sections.rich-text.blocks.buttons.settings.button_link_2.label"
        },
        {
          "type": "select",
          "id": "button_style_2",
          "options": [
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "button button--primary",
          "label": "Second button style"
        },
        {
          "type": "header",
          "content": "Advanced settings",
          "info": "t:sections.all.advanced_options.label"
        },
        {
          "type": "liquid",
          "id": "button_label_liquid",
          "label": "First button label liquid",
          "info": "Overrides first button label and accepts HTML and limited Liquid markup [learn more](https://shopify.dev/docs/themes/architecture/settings/input-settings#liquid)"
        },
        {
          "type": "liquid",
          "id": "button_label_2_liquid",
          "label": "Second button label liquid",
          "info": "Overrides first button label and accepts HTML and limited Liquid markup [learn more](https://shopify.dev/docs/themes/architecture/settings/input-settings#liquid)"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.custom-liquid.name",
      "settings": [
        {
          "type": "header",
          "content": "Advanced settings",
          "info": "t:sections.all.advanced_options.label"
        },
        {
          "type": "text",
          "id": "title",
          "default": "My custom liquid",
          "label": "Title",
          "info": "Only used to organize custom liquid in side bar."
        },
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "popup",
      "name": "t:sections.main-product.blocks.popup.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "Pop-up link text",
          "label": "t:sections.main-product.blocks.popup.settings.link_label.label"
        },
        {
          "type": "liquid",
          "id": "text_liquid",
          "label": "Link label liquid",
          "info": "Overrides Link label above"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "Title",
          "info": "Overrides page title if set"
        },
        {
          "id": "page",
          "type": "page",
          "label": "t:sections.main-product.blocks.popup.settings.page.label"
        },
        {
          "type": "liquid",
          "id": "content_liquid",
          "label": "Custom content",
          "info": "Overrides page setting above"
        },
        {
          "type": "select",
          "id": "button_style",
          "options": [
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "link underlined-link",
          "label": "Button style"
        },
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme-1",
          "label": "t:sections.all.colors.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.rich-text.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        }
      ]
    }
  ]
}
{% endschema %}
