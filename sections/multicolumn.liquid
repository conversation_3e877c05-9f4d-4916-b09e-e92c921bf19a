{% comment %}
  bs-add
  - padding options for moible/desktop including side padding
  - liquid overrides for titles
  - hide/show for section based on device
  - removal of title section
  - ability to span columns per device type
{% endcomment %}

{{ 'section-multicolumn.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

{%- liquid
  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and section.blocks.size > columns_mobile_int
    assign show_mobile_slider = true
  endif

  # bs-add
  # - support for desktop slider
  assign columns_desktop_int = section.settings.columns_desktop | plus: 0
  assign show_desktop_slider = false
  if section.settings.swipe_on_desktop and section.blocks.size > columns_desktop_int
    assign show_desktop_slider = true
  endif

  assign title_content = section.settings.title_liquid | default: section.settings.title
  assign button_label_content = section.settings.button_label_liquid | default: section.settings.button_label
-%}

<div class="{{ section.settings.custom_css_class }}">
  <div class="multicolumn color-{{ section.settings.color_scheme_section }} gradient {% if section.settings.background_style == 'none' %}section-{{ section.id }}-padding{% endif %}">
    <div class="{% unless show_desktop_slider %}{{ section.settings.content_width }}{% endunless %}">
      <div class="{% unless show_desktop_slider %}{{ section.settings.content_position }}{% endunless %}">
        <div class="multicolumn {% if section.settings.background_style == 'none' %}color-{{ section.settings.color_scheme_content }} gradient{% endif %} {% unless section.settings.background_style == 'none' and settings.text_boxes_border_thickness > 0 or settings.text_boxes_shadow_opacity > 0 %} background-{{ section.settings.background_style }}{% endunless %}{% if title_content == blank %} no-heading{% endif %}">
          <div
            class="{% unless section.settings.background_style == 'none' %}section-{{ section.id }}-padding{% endunless %} isolate{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
            {% if settings.animations_reveal_on_scroll %}
              data-cascade
            {% endif %}
          >
            {% comment %}
              bs-add
              - support for desktop slider
            {% endcomment %}
            <slider-component
              class="slider-mobile-gutter{% if section.settings.content_width == 'full-width' %} slider-component-full-width{% endif %} {{ section.settings.content_width }} {% if show_desktop_slider %} slider-component-desktop{% endif %}"
              style="{% if section.settings.content_width contains 'page-width--narrow' %}--page-width: 72.6rem;{% endif %}"
            >
              <ul
                class="multicolumn-list contains-content-container grid grid--{{ section.settings.columns_mobile }}-col grid--{{ section.settings.columns_tablet }}-col-tablet grid--{{ section.settings.columns_desktop }}-col-desktop {% if show_mobile_slider or show_desktop_slider %} slider{% if show_desktop_slider %} slider--desktop{% endif %}{% if show_mobile_slider %} slider--tablet grid--peek{% endif %}{% endif %}"
                id="Slider-{{ section.id }}"
                role="list"
              >
                {%- liquid
                  assign highest_ratio = 0
                  for block in section.blocks
                    if block.settings.video != blank and block.settings.video.aspect_ratio > highest_ratio
                      assign highest_ratio = block.settings.video.aspect_ratio
                    elsif block.settings.image != blank and block.settings.image.aspect_ratio > highest_ratio
                      assign highest_ratio = block.settings.image.aspect_ratio
                    endif
                  endfor
                -%}
                {%- for block in section.blocks -%}
                  {%- assign block_title_content = block.settings.title_liquid | default: block.settings.title -%}
                  {%- assign block_text_content = block.settings.text_liquid | default: block.settings.text -%}
                  {%- assign block_link_label_content = block.settings.link_label_liquid
                    | default: block.settings.link_label
                  -%}

                  {%- assign empty_column_info_class = '' -%}
                  {%- if block_title_content == blank
                    and block_text_content == blank
                    and block_link_label_content == blank
                    and block.settings.show_popup == false
                  -%}
                    {%- assign empty_column_info_class = ' multicolumn-card__info--empty' -%}
                  {%- endif -%}
                  
                  {%- assign empty_column = '' -%}
                  {%- if block.settings.video == blank
                    and block.settings.image == blank
                    and empty_column_info_class != ''
                  -%}
                    {%- assign empty_column = ' multicolumn-list__item--empty' -%}
                  {%- endif -%}

                  {% comment %}
                    bs-add
                      - support for desktop slider
                  {% endcomment %}
                  <li
                    id="Slide-{{ section.id }}-{{ forloop.index }}"
                    class="multicolumn-list__item grid__item {{ block.settings.colomn_span_amount }} {{ block.settings.colomn_span_amount_tablet }} {{ block.settings.colomn_span_amount_desktop }}{% if section.settings.swipe_on_mobile or section.settings.swipe_on_desktop %} slider__slide{% endif %} content--mobile-{{ section.settings.column_alignment_mobile }} content--desktop-{{ section.settings.column_alignment_desktop }}{{ empty_column }} {% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                    {{ block.shopify_attributes }}
                    {% if settings.animations_reveal_on_scroll %}
                      data-cascade
                      style="--animation-order: {{ forloop.index }};"
                    {% endif %}
                  >
                    <div class="multicolumn-card content-container {% unless section.settings.background_style == 'none' %}color-{{ section.settings.color_scheme_content }} gradient{% endunless %}">
                      {%- if block.settings.image != blank or block.settings.video != blank -%}
                        {% if section.settings.image_ratio == 'adapt' or section.settings.image_ratio == 'circle' %}
                          {% assign spaced_image = true %}
                        {% endif %}
                        <div class="multicolumn-card__image-wrapper multicolumn-card__image-wrapper--{{ section.settings.image_width }}-width{% if section.settings.image_width != 'full' or spaced_image %} multicolumn-card-spacing{% endif %}{{ empty_column_info_class }}">
                          <div
                            class="media media--transparent media--{{ section.settings.image_ratio }}"
                            {% if section.settings.image_ratio == 'adapt' %}
                              style="padding-bottom: {{ 1 | divided_by: highest_ratio | times: 100 }}%;"
                            {% endif %}
                          >
                            {%- liquid
                              assign number_of_columns = section.settings.columns_desktop
                              assign number_of_columns_mobile = section.settings.columns_mobile
                              assign grid_space_desktop = number_of_columns | minus: 1 | times: settings.spacing_grid_horizontal | plus: 100 | append: 'px'
                              assign grid_space_tablet = number_of_columns_mobile | minus: 1 | times: settings.spacing_grid_horizontal | plus: 100 | append: 'px'
                              assign grid_space_mobile = number_of_columns_mobile | minus: 1 | times: settings.spacing_grid_horizontal | divided_by: 2 | plus: 30 | append: 'px'
                              if section.settings.image_width == 'half'
                                assign image_width = 0.5
                              elsif section.settings.image_width == 'third'
                                assign image_width = 0.33
                              elsif section.settings.image_width == 'two-third'
                                assign image_width = 0.66
                              else
                                assign image_width = 1
                              endif
                            -%}
                            {% capture sizes %}
                              (min-width: {{ settings.page_width }}px) calc(({{ settings.page_width }}px - {{ grid_space_desktop }}) * {{ image_width }} /  {{ number_of_columns }}),
                              (min-width: 990px) calc((100vw - {{ grid_space_desktop }}) * {{ image_width }} / {{ number_of_columns }}),
                              (min-width: 750px) calc((100vw - {{ grid_space_tablet }}) * {{ image_width }} / {{ number_of_columns_mobile }}),
                              calc((100vw - {{ grid_space_mobile }}) * {{ image_width }} / {{ number_of_columns_mobile }})
                            {% endcapture %}
                            {%- if block.settings.video != blank -%}
                              {{
                                block.settings.video
                                | video_tag:
                                  image_size: '1500x',
                                  loop: true,
                                  controls: false,
                                  muted: true,
                                  autoplay: true
                              }}
                            {%- elsif block.settings.image != blank -%}
                              {{
                                block.settings.image
                                | image_url: width: 3200
                                | image_tag:
                                  widths: '50, 75, 100, 150, 200, 300, 400, 500, 750, 1000, 1250, 1500, 1750, 2000, 2250, 2500, 2750, 3000, 3200',
                                  sizes: sizes,
                                  class: 'multicolumn-card__image'
                              }}
                            {% endif %}
                          </div>
                        </div>
                      {%- endif -%}
                      <div class="multicolumn-card__info{{ empty_column_info_class }}">
                        <div class="multicolumn-card__info__items">
                          {%- if block_title_content != blank -%}
                            <h3 class="inline-richtext {{ section.settings.heading_size }}">
                              {{ block_title_content }}
                            </h3>
                          {%- endif -%}
                          {%- if block_text_content != blank -%}
                            <div class="rte">{{ block_text_content }}</div>
                          {%- endif -%}

                          {%- comment -%}
                            bs-add
                              - support for popups
                          {%- endcomment -%}
                          {%- if block.settings.show_popup -%}
                            {{ 'section-main-product.css' | asset_url | stylesheet_tag }}
                            <modal-opener
                              class="product-popup-modal__opener quick-add-hidden"
                              data-modal="#PopupModal-{{ block.id }}"
                              {{ block.shopify_attributes }}
                            >
                              <button
                                id="ProductPopup-{{ block.id }}"
                                class="{% if block.settings.button_style_popup contains 'link' %}product-popup-modal__button{% endif %} {{ block.settings.button_style_popup }}{% if block_link_label_content == blank and section.settings.link_image == true %} multicolumn-card--cover-link{% endif %}"
                                style="{% if block.settings.button_style_popup contains 'link' %}min-height: auto;padding-right: 0;text-decoration: underline;{% endif %}"
                                type="button"
                                aria-haspopup="dialog"
                              >
                                {{ block.settings.text_liquid_popup | default: block.settings.text_popup }}
                              </button>
                            </modal-opener>
                          {%- endif -%}

                          {%- if block_link_label_content != blank -%}
                            <div class="multicolumn-card__info__link">
                              <a
                                class="{% if block.settings.button_style == 'link' %}link animate-arrow{% else %}{{ block.settings.button_style }}{% endif %}{% if block.settings.link != blank and section.settings.link_image == true and block.settings.show_popup == false %} multicolumn-card--cover-link{% endif %}"
                                {% if block.settings.link == blank %}
                                  role="link" aria-disabled="true"
                                {% else %}
                                  href="{{ block.settings.link }}"
                                {% endif %}
                              >
                                {{ block_link_label_content }}
                                {% if block.settings.button_style == 'link' %}
                                  <span class="svg-wrapper">
                                    <span class="icon-wrap">&nbsp;{{ 'icon-arrow.svg' | inline_asset_content }}</span>
                                  </span>
                                {% endif %}
                              </a>
                            </div>
                          {%- endif -%}
                        </div>
                      </div>
                    </div>
                  </li>
                {%- endfor -%}
              </ul>

              {% comment %}
                bs-add
                  - support for desktop slider
              {% endcomment %}
              {%- if show_mobile_slider or show_desktop_slider -%}
                <div class="slider-buttons">
                  <button
                    type="button"
                    class="slider-button slider-button--prev"
                    name="previous"
                    aria-label="{{ 'general.slider.previous_slide' | t }}"
                  >
                    <span class="svg-wrapper">{{ 'icon-caret.svg' | inline_asset_content }}</span>
                  </button>
                  <div class="slider-counter caption">
                    <span class="slider-counter--current">1</span>
                    <span aria-hidden="true"> / </span>
                    <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
                    <span class="slider-counter--total">{{ section.blocks.size }}</span>
                  </div>
                  <button
                    type="button"
                    class="slider-button slider-button--next"
                    name="next"
                    aria-label="{{ 'general.slider.next_slide' | t }}"
                  >
                    <span class="svg-wrapper">{{ 'icon-caret.svg' | inline_asset_content }}</span>
                  </button>
                </div>
              {%- endif -%}
            </slider-component>
            <div class="center{% if show_mobile_slider %} small-hide medium-hide{% endif %}">
              {%- if button_label_content != blank -%}
                <a
                  class="{{ section.settings.button_style }}"
                  {% if section.settings.button_link == blank %}
                    role="link" aria-disabled="true"
                  {% else %}
                    href="{{ section.settings.button_link }}"
                  {% endif %}
                >
                  {{ button_label_content }}
                </a>
              {%- endif -%}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{%- comment -%}
  bs-add
    - support for popups
{%- endcomment -%}
{%- for block in section.blocks -%}
  {%- if block.settings.show_popup -%}
    <modal-dialog
      id="PopupModal-{{ block.id }}"
      class="product-popup-modal color-{{ block.settings.color_scheme_popup }} gradient"
      {{ block.shopify_attributes }}
    >
      <div
        role="dialog"
        aria-label="{{ block.settings.title_popup | default: block.settings.page_popup.title }}"
        aria-modal="true"
        class="product-popup-modal__content"
        tabindex="-1"
      >
        <button
          id="ModalClose-{{ block.id }}"
          type="button"
          class="product-popup-modal__toggle"
          aria-label="{{ 'accessibility.close' | t }}"
        >
          {{- 'icon-close.svg' | inline_asset_content -}}
        </button>
        <div class="product-popup-modal__content-info">
          <h3 class="h2">{{ block.settings.title_popup | default: block.settings.page_popup.title }}</h3>
          {{ block.settings.content_liquid_popup | default: block.settings.page_popup.content }}
        </div>
      </div>
    </modal-dialog>
  {%- endif -%}
{%- endfor -%}

{{ section.settings.custom_liquid }}

{% schema %}
{
  "name": "t:sections.multicolumn.name",
  "class": "section section--multicolumn",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Color Scheme"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_section",
      "label": "Section color scheme",
      "default": "scheme-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_content",
      "label": "Content color scheme",
      "default": "scheme-1"
    },
    {
      "type": "select",
      "id": "background_style",
      "options": [
        {
          "value": "none",
          "label": "All content"
        },
        {
          "value": "primary",
          "label": "Individual blocks"
        }
      ],
      "default": "primary",
      "label": "Content background color"
    },
    {
      "type": "header",
      "content": "Width control"
    },
    {
      "type": "select",
      "id": "content_width",
      "options": [
        {
          "value": "full-width",
          "label": "Full width on all devices"
        },
        {
          "value": "page-width",
          "label": "Page width (based on theme settings)"
        },
        {
          "value": "page-width page-width--narrow",
          "label": "Page width narrow (tablet and desktop only)"
        }
      ],
      "default": "page-width",
      "label": "Content width",
      "info": "Multicolumn is always full width on mobile (max-width: 749px)"
    },
    {
      "type": "select",
      "id": "content_position",
      "options": [
        {
          "value": "full-width",
          "label": "Full width"
        },
        {
          "value": "left-one-third",
          "label": "Left one third"
        },
        {
          "value": "left-one-half",
          "label": "Left one half"
        },
        {
          "value": "left-two-thirds",
          "label": "Left two thirds"
        },
        {
          "value": "right-one-third",
          "label": "Right one third"
        },
        {
          "value": "right-one-half",
          "label": "Right one half"
        },
        {
          "value": "right-two-thirds",
          "label": "Right two thirds"
        }
      ],
      "default": "full-width",
      "label": "Content position within section",
      "info": "Only applies to desktop (min-width: 990px)"
    },
    {
      "type": "header",
      "content": "Columns"
    },
    {
      "type": "range",
      "id": "columns_tablet",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 2,
      "label": "Number of columns on tablet (min-width: 750px and max-width: 989px)"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 3,
      "label": "Number of columns on desktop (min-width: 990px)"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__2.label"
        }
      ],
      "default": "1",
      "label": "Number of columns on mobile (max-width: 749px)"
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.multicolumn.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "swipe_on_desktop",
      "default": false,
      "label": "Enable swipe on desktop"
    },
    {
      "type": "header",
      "content": "Block settings"
    },
    {
      "type": "select",
      "id": "image_width",
      "options": [
        {
          "value": "third",
          "label": "t:sections.multicolumn.settings.image_width.options__1.label"
        },
        {
          "value": "half",
          "label": "t:sections.multicolumn.settings.image_width.options__2.label"
        },
        {
          "value": "two-third",
          "label": "t:sections.multicolumn.settings.image_width.options__4.label"
        },
        {
          "value": "full",
          "label": "t:sections.multicolumn.settings.image_width.options__3.label"
        }
      ],
      "default": "full",
      "label": "t:sections.multicolumn.settings.image_width.label"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.multicolumn.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.multicolumn.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.multicolumn.settings.image_ratio.options__3.label"
        },
        {
          "value": "circle",
          "label": "t:sections.multicolumn.settings.image_ratio.options__4.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.multicolumn.settings.image_ratio.label"
    },
    {
      "type": "select",
      "id": "column_alignment_mobile",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left",
      "label": "Column alignment mobile (max-width: 749px)"
    },
    {
      "type": "select",
      "id": "column_alignment_desktop",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left",
      "label": "Column alignment desktop (min-width: 750px)"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__h6.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__h5.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__h4.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__h3.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h4",
      "label": "Heading font size"
    },
    {
      "type": "header",
      "content": "Button settings"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "t:sections.multicolumn.settings.button_label.default",
      "label": "t:sections.multicolumn.settings.button_label.label"
    },
    {
      "type": "liquid",
      "id": "button_label_liquid",
      "label": "Button label liquid",
      "info": "Overrides Button label above"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.multicolumn.settings.button_link.label"
    },
    {
      "type": "select",
      "id": "button_style",
      "options": [
        {
          "value": "button button--primary",
          "label": "Solid"
        },
        {
          "value": "button button--secondary",
          "label": "Outline"
        },
        {
          "value": "link underlined-link",
          "label": "Link"
        },
        {
          "value": "button button-1",
          "label": "Custom 1"
        },
        {
          "value": "button button-2",
          "label": "Custom 2"
        },
        {
          "value": "button button-3",
          "label": "Custom 3"
        },
        {
          "value": "button button-4",
          "label": "Custom 4"
        },
        {
          "value": "button button-5",
          "label": "Custom 5"
        }
      ],
      "default": "button button--primary",
      "label": "Button style"
    },
    {
      "type": "header",
      "content": "Content padding mobile (max-width: 749px)"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_left_mobile",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding left mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_right_mobile",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding right mobile",
      "default": 0
    },
    {
      "type": "header",
      "content": "Content padding desktop (min-width: 750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 500,
      "step": 5,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 500,
      "step": 5,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_left_desktop",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding left desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_right_desktop",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding right desktop",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "checkbox",
      "id": "link_image",
      "default": false,
      "label": "Enable column-wide linking",
      "info": "Prioritizes popup link over column link"
    },
    {
      "type": "page",
      "id": "page",
      "label": "Linked Page",
      "info": "Allows page linking to access metafields in unique cases (e.g. home page)."
    },
    {
      "type": "header",
      "content": "Custom overrides"
    },
    {
      "type": "paragraph",
      "content": "Use this for section id: #shopify-section-{{ section.id }}"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    }
  ],
  "blocks": [
    {
      "type": "column",
      "name": "t:sections.multicolumn.blocks.column.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.multicolumn.blocks.column.settings.image.label"
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video (overrides image)",
          "info": "(Not compatible with 'adapt' image dimensions)"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "default": "t:sections.multicolumn.blocks.column.settings.title.default",
          "label": "t:sections.multicolumn.blocks.column.settings.title.label"
        },
        {
          "type": "liquid",
          "id": "title_liquid",
          "label": "Heading liquid",
          "info": "Overrides Heading above"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "t:sections.multicolumn.blocks.column.settings.text.default",
          "label": "t:sections.multicolumn.blocks.column.settings.text.label"
        },
        {
          "type": "liquid",
          "id": "text_liquid",
          "label": "Description liquid",
          "info": "Overrides description above"
        },
        {
          "type": "inline_richtext",
          "id": "link_label",
          "label": "Link label"
        },
        {
          "type": "liquid",
          "id": "link_label_liquid",
          "label": "Link label liquid",
          "info": "Overrides Link label above"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.multicolumn.blocks.column.settings.link.label"
        },
        {
          "type": "select",
          "id": "button_style",
          "options": [
            {
              "value": "link",
              "label": "link"
            },
            {
              "value": "button button-1",
              "label": ".button-1"
            },
            {
              "value": "button button-2",
              "label": ".button-2"
            },
            {
              "value": "button button-3",
              "label": ".button-3"
            },
            {
              "value": "button button-4",
              "label": ".button-4"
            },
            {
              "value": "button button-5",
              "label": ".button-5"
            }
          ],
          "default": "link",
          "label": "Button style"
        },
        {
          "type": "header",
          "content": "Popup options"
        },
        {
          "type": "checkbox",
          "id": "show_popup",
          "default": false,
          "label": "Show popup"
        },
        {
          "type": "inline_richtext",
          "id": "text_popup",
          "default": "Link label",
          "label": "t:sections.main-product.blocks.popup.settings.link_label.label"
        },
        {
          "type": "liquid",
          "id": "text_liquid_popup",
          "label": "Link label liquid",
          "info": "Overrides link label above"
        },
        {
          "type": "select",
          "id": "button_style_popup",
          "options": [
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "link underlined-link",
          "label": "Button style"
        },
        {
          "id": "page_popup",
          "type": "page",
          "label": "t:sections.main-product.blocks.popup.settings.page.label"
        },
        {
          "type": "inline_richtext",
          "id": "title_popup",
          "label": "Heading",
          "info": "Overrides page title if set"
        },
        {
          "type": "liquid",
          "id": "content_liquid_popup",
          "label": "Custom content",
          "info": "Overrides page setting above"
        },
        {
          "type": "color_scheme",
          "id": "color_scheme_popup",
          "label": "t:sections.all.colors.label",
          "default": "scheme-1"
        },
        {
          "type": "header",
          "content": "Column span options"
        },
        {
          "type": "select",
          "id": "colomn_span_amount",
          "options": [
            {
              "value": "grid__item--span-1",
              "label": "1"
            },
            {
              "value": "grid__item--span-2",
              "label": "2"
            },
            {
              "value": "grid__item--span-3",
              "label": "3"
            }
          ],
          "default": "grid__item--span-1",
          "label": "Column span amount (mobile)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_tablet",
          "options": [
            {
              "value": "grid__item--span-1-tablet",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-tablet",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-tablet",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-tablet",
              "label": "4"
            }
          ],
          "default": "grid__item--span-1-tablet",
          "label": "Column span (tablet only)",
          "info": "Screen (min-width: 750px) and (max-width: 989px)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "grid__item--span-1-desktop",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-desktop",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-desktop",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-desktop",
              "label": "4"
            },
            {
              "value": "grid__item--span-5-desktop",
              "label": "5"
            },
            {
              "value": "grid__item--span-6-desktop",
              "label": "6"
            }
          ],
          "default": "grid__item--span-1-desktop",
          "label": "Column span (desktop only)",
          "info": "Screen (min-width: 990px)"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.multicolumn.presets.name",
      "blocks": [
        {
          "type": "column"
        },
        {
          "type": "column"
        },
        {
          "type": "column"
        }
      ]
    }
  ]
}
{% endschema %}
