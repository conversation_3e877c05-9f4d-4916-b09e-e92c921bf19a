{% comment %}
  bs-add
  - ability to hide/show section
  - ability to have separate padding/padding for desktop/mobile
  - new color options and also gradient flag for colors
  - new button styles
  - ability to override text with liquid
  - ability to have multiple collections via tabs/dropdown
  - card heading font size option
  - quick add button style option
  - option to show swatches on product card
  - option to show alternative title on product card
  - supports product list override for block collection
  - only shows section if product list or collection for first block is present 
{% endcomment %}

{%- liquid
  assign products_exist = false
  if section.blocks.size > 0
    if section.blocks.first.settings.product_list != blank
      assign products_exist = true
    elsif section.blocks.first.settings.collection != blank
      assign products_exist = true
    endif
  endif 
-%}

{% liquid
  assign media_aspect_ratio = section.settings.image_ratio | default: settings.card_image_ratio
  assign image_shape = section.settings.image_shape | default: settings.card_image_shape
  assign quick_add_behavior = section.settings.quick_add_behavior | default: settings.card_quick_add_behavior
  assign card_heading_font_size = section.settings.card_heading_font_size | default: settings.card_heading_font_size  
  assign show_secondary_image = settings.card_show_secondary_image
  if section.settings.show_secondary_image == "true"
    assign show_secondary_image = true
  elsif section.settings.show_secondary_image == "false"
    assign show_secondary_image = false
  endif
  assign show_vendor = settings.card_show_vendor
  if section.settings.show_vendor == "true"
    assign show_vendor = true
  elsif section.settings.show_vendor == "false"
    assign show_vendor = false
  endif
  assign show_rating = settings.card_show_rating
  if section.settings.show_rating == "true"
    assign show_rating = true
  elsif section.settings.show_rating == "false"
    assign show_rating = false
  endif
  assign show_alternative_title = settings.card_show_alternative_title
  if section.settings.show_alternative_title == "true"
    assign show_alternative_title = true
  elsif section.settings.show_alternative_title == "false"
    assign show_alternative_title = false
  endif
  assign show_card_product_custom_field = settings.card_show_card_product_custom_field
  if section.settings.show_card_product_custom_field == "true"
    assign show_card_product_custom_field = true
  elsif section.settings.show_card_product_custom_field == "false"
    assign show_card_product_custom_field = false
  endif
%}

{%- if request.design_mode or products_exist -%}

{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}

{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'template-collection.css' | asset_url | stylesheet_tag }}

{%- if section.blocks.size > 1 -%}
  {{ 'component-tab.css' | asset_url | stylesheet_tag }}
{%- endif -%}

{% if section.settings.image_shape == 'blob' %}
  {{ 'mask-blobs.css' | asset_url | stylesheet_tag }}
{%- endif -%}

{%- unless section.settings.quick_add == 'none' -%}
  {{ 'quick-add.css' | asset_url | stylesheet_tag }}
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
{%- endunless -%}

{%- if section.settings.quick_add == 'standard' -%}
  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- if section.settings.quick_add == 'bulk' -%}
  <script src="{{ 'quick-add-bulk.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'quantity-popover.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'price-per-item.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'quick-order-list.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}

  /* bs-add
    - option text alignment
  */
  .section-{{ section.id }}-padding .collection__title {
    text-align: {{ section.settings.text_alignment_mobile }};
  }
  .section-{{ section.id }}-padding div[role="tablist"] {
    text-align: {{ section.settings.text_alignment_mobile }};
  }
  .section-{{ section.id }}-padding div[role="tablist"] .button--tertiary {
    min-width: auto;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding .collection__title {
      text-align: {{ section.settings.text_alignment_desktop }};
    }
    .section-{{ section.id }}-padding div[role="tablist"] {
      text-align: {{ section.settings.text_alignment_desktop }};
    }
  }
{%- endstyle -%}

<div class="{{ section.settings.custom_css_class }}">
  <div class="color-{{ section.settings.color_scheme }} isolate gradient section-{{ section.id }}-padding">
  
    <div class="collection__title{% if section.blocks.size > 1 %} collection__title__tabs{% endif %} title-wrapper title-wrapper--no-top-margin page-width">
      {%- assign title_content = section.settings.title_liquid | default: section.settings.title -%}
      {%- if title_content != blank -%}
        <h2 class="title inline-richtext {{ section.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
          {{ title_content }}
        </h2>
      {%- endif -%}
      {%- if section.settings.description != blank -%}
        <div class="collection__description {{ section.settings.description_style }} rte{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
          {{ section.settings.description }}
        </div>
      {%- endif -%}
    </div>
  
    {%- comment -%} Needed if more than one collection {%- endcomment -%}
    {%- if section.blocks.size > 1 -%}
      <tab-component>
        <div role="tablist" aria-label="{{ section.settings.title_liquid | default: section.settings.title | escape }}" class="page-width">
          {%- for block in section.blocks -%}
          <button
            role="tab"
            aria-selected="{% if forloop.first %}true{% else %}false{% endif %}"
            aria-controls="TabsPanel-{{ block.id }}-{{ section.id }}"
            id="Tabs-{{ block.id }}-{{ section.id }}"
            class="button button--tertiary"
            tabindex="{% if forloop.first %}0{% else %}-1{% endif %}">
            <span class="{{ section.settings.tab_link_size }}">{{ block.settings.title | default: block.settings.collection.title }}</span>
          </button>
          {%- endfor -%}
        </div>
    {%- endif -%}
  
    {%- for block in section.blocks -%}
      {%- liquid
        assign product_list = block.settings.collection.products
        assign products_to_display = block.settings.collection.all_products_count

        if block.settings.product_list.count > 0
          assign product_list = block.settings.product_list
          assign products_to_display = product_list.count
        endif
  
        if product_list.count > section.settings.products_to_show
          assign products_to_display = section.settings.products_to_show
          assign more_in_collection = true
        endif

        # bs-add
        #   - provides functionality to hide products placed in cart
        assign cart_variant_id_list = ''
        if section.settings.hide_cart_products
          assign cart_variant_id_list = cart.items | map: 'variant_id' | join: ','
  
          for product in product_list
            if cart_variant_id_list contains product.selected_or_first_available_variant.id
              assign products_to_display = products_to_display | minus: 1
            endif
          endfor
        endif

        assign columns_mobile_int = section.settings.columns_mobile | plus: 0
        assign show_mobile_slider = false
        if section.settings.swipe_on_mobile and products_to_display > columns_mobile_int
          assign show_mobile_slider = true
        endif

        assign show_desktop_slider = false
        if section.settings.enable_desktop_slider and products_to_display > section.settings.columns_desktop
          assign show_desktop_slider = true
        endif
      -%}
  
      {%- comment -%} Needed if more than one collection {%- endcomment -%}
      {%- if section.blocks.size > 1 -%}
        <div id="TabsPanel-{{ block.id }}-{{ section.id }}" role="tabpanel" tabindex="0" aria-labelledby="Tabs-{{ block.id }}-{{ section.id }}" {% if forloop.first %}{% else %}hidden{% endif %}>
      {%- endif -%}
  
        <div class="collection{% if section.settings.quick_add == 'bulk' %} collection-quick-add-bulk{% endif %}{% if section.settings.full_width %} collection--full-width{% endif %}"
          id="collection-{{ section.id }}-{{ block.id }}"
          data-id="{{ section.id }}"
        >
          <div class="collection__title title-wrapper page-width{% if show_mobile_slider %} title-wrapper--self-padded-tablet-down{% endif %}{% if show_desktop_slider %} collection__title--desktop-slider{% endif %}">
            <div class="collection__description rte">
              {%- if block.settings.show_description -%}
                {{ block.settings.collection.description }}
              {%- else -%}
                {{ block.settings.description }}
              {%- endif -%}
            </div>
          </div>
  
          <slider-component class="slider-mobile-gutter{% if section.settings.full_width %} slider-component-full-width{% endif %}{% if show_mobile_slider == false %} page-width{% endif %}{% if show_desktop_slider == false and section.settings.full_width == false %} page-width-desktop{% endif %}{% if show_desktop_slider %} slider-component-desktop{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
            <ul
              id="Slider-{{ section.id }}-{{ block.id }}"
              data-id="{{ section.id }}"
              class="grid product-grid contains-card contains-card--product{% if settings.card_style == 'standard' %} contains-card--standard{% endif %} grid--{{ section.settings.columns_desktop }}-col-desktop grid--{{ section.settings.columns_mobile }}-col-tablet-down{% if show_mobile_slider or show_desktop_slider %} slider{% if show_desktop_slider %} slider--desktop{% endif %}{% if show_mobile_slider %} slider--tablet grid--peek{% endif %}{% endif %}"
              role="list"
              aria-label="{{ 'general.slider.name' | t }}"
            >
              {% assign skip_card_product_styles = false %}
              {% if block.settings.product_list.count > 0 %}
                {%- for product in block.settings.product_list -%}
                  {% unless cart_variant_id_list != '' and cart_variant_id_list contains product.selected_or_first_available_variant.id %}
                    <li
                      id="Slide-{{ section.id }}-{{ block.id }}-{{ forloop.index }}"
                      class="grid__item{% if show_mobile_slider or show_desktop_slider %} slider__slide{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                      {% if settings.animations_reveal_on_scroll %}
                        data-cascade
                        style="--animation-order: {{ forloop.index }};"
                      {% endif %}
                    >
                      {% render 'card-product',
                        card_product: product,
                        skip_styles: skip_card_product_styles,
                        section_id: block.id,
                        quick_add: section.settings.quick_add,
                        quick_add_button_style: section.settings.quick_add_button_style,
                        media_aspect_ratio: media_aspect_ratio,
                        image_shape: image_shape,
                        show_secondary_image: show_secondary_image,
                        show_vendor: show_vendor,
                        show_rating: show_rating,
                        quick_add_behavior: quick_add_behavior,
                        card_heading_font_size: card_heading_font_size,
                        show_alternative_title: show_alternative_title,
                        show_card_product_custom_field: show_card_product_custom_field
                      %}
                    </li>
                    {%- assign skip_card_product_styles = true -%}
                  {%- endunless -%}
                {% endfor %}
              {%- elsif block.settings.collection.products.size > 0 -%}
                {% paginate block.settings.collection.products by section.settings.products_to_show %}
                  {%- for product in block.settings.collection.products limit: section.settings.products_to_show -%}
                    {% unless cart_variant_id_list != '' and cart_variant_id_list contains product.selected_or_first_available_variant.id %}
                      {%- if block.settings.product_tags == blank or product.tags contains block.settings.product_tags -%}
                        <li
                          id="Slide-{{ section.id }}-{{ block.id }}-{{ forloop.index }}"
                          class="grid__item{% if show_mobile_slider or show_desktop_slider %} slider__slide{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                          {% if settings.animations_reveal_on_scroll %}
                            data-cascade
                            style="--animation-order: {{ forloop.index }};"
                          {% endif %}
                        >
                          {% render 'card-product',
                            card_product: product,
                            skip_styles: skip_card_product_styles,
                            section_id: block.id,
                            quick_add: section.settings.quick_add,
                            quick_add_button_style: section.settings.quick_add_button_style,
                            media_aspect_ratio: media_aspect_ratio,
                            image_shape: image_shape,
                            show_secondary_image: show_secondary_image,
                            show_vendor: show_vendor,
                            show_rating: show_rating,
                            quick_add_behavior: quick_add_behavior,
                            card_heading_font_size: card_heading_font_size,
                            show_alternative_title: show_alternative_title,
                            show_card_product_custom_field: show_card_product_custom_field
                          %}
                        </li>
                        {%- assign skip_card_product_styles = true -%}
                      {%- endif -%}
                    {%- endunless -%}
                  {% endfor %}
                {% endpaginate %}
              {%- else -%}
                {%- for i in (1..section.settings.columns_desktop) -%}
                  <li
                    class="grid__item{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                    {% if settings.animations_reveal_on_scroll %}
                      data-cascade
                      style="--animation-order: {{ forloop.index }};"
                    {% endif %}
                  >
                    {% liquid
                      assign ridx = forloop.rindex
                      case ridx
                        when 5
                          assign ridx = 1
                        when 6
                          assign ridx = 2
                      endcase
                    %}
                    {%- assign placeholder_image = 'product-apparel-' | append: ridx -%}
                    {% render 'card-product',
                      show_vendor: section.settings.show_vendor,
                      media_aspect_ratio: section.settings.image_ratio,
                      image_shape: section.settings.image_shape,
                      placeholder_image: placeholder_image
                    %}
                  </li>
                {%- endfor -%}
              {%- endif -%}
            </ul>
            {%- if show_mobile_slider or show_desktop_slider -%}
              <div class="slider-buttons">
                <button
                  type="button"
                  class="slider-button slider-button--prev"
                  name="previous"
                  aria-label="{{ 'general.slider.previous_slide' | t }}"
                  aria-controls="Slider-{{ section.id }}"
                >
                  <span class="svg-wrapper">
                    {{- 'icon-caret.svg' | inline_asset_content -}}
                  </span>
                </button>
                <div class="slider-counter caption">
                  <span class="slider-counter--current">1</span>
                  <span aria-hidden="true"> / </span>
                  <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
                  <span class="slider-counter--total">{{ products_to_display }}</span>
                </div>
                <button
                  type="button"
                  class="slider-button slider-button--next"
                  name="next"
                  aria-label="{{ 'general.slider.next_slide' | t }}"
                  aria-controls="Slider-{{ section.id }}"
                >
                  <span class="svg-wrapper">
                    {{- 'icon-caret.svg' | inline_asset_content -}}
                  </span>
                </button>
              </div>
            {%- endif -%}
          </slider-component>

          {%- if section.settings.view_all_style != "no" and more_in_collection and block.settings.product_list == blank -%}
            <div class="center collection__view-all{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
              <a
                href="{{ block.settings.collection.url }}"
                class="{{ section.settings.view_all_style }}"
                aria-label="{{ 'sections.featured_collection.view_all_label' | t: collection_name: block.settings.collection.title | escape }}"
              >
                {{ 'sections.featured_collection.view_all' | t }}
              </a>
            </div>
          {%- endif -%}
          {% if section.settings.image_shape == 'arch' %}
            {{ 'mask-arch.svg' | inline_asset_content }}
          {%- endif -%}
        </div>
  
      {%- comment -%} Needed if more than one collection {%- endcomment -%}
      {%- if section.blocks.size > 1 -%}
        </div>
      {%- endif -%}
    {%- endfor -%}
  
    {%- comment -%} Needed if more than one collection {%- endcomment -%}
    {%- if section.blocks.size > 1 -%}
    </tab-component>
    {%- endif -%}
  
  </div>
</div>

{{ section.settings.custom_liquid }}

{%- endif -%}

{% schema %}
{
  "name": "t:sections.featured-collection.name",
  "tag": "section",
  "class": "section section--featured-collection",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Color Scheme"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Mobile layout",
      "info": "Devices with screens < 750px"
    },
    {
      "type": "select",
      "id": "text_alignment_mobile",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left",
      "label": "Header alignment (mobile)"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "2",
      "label": "t:sections.featured-collection.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.featured-collection.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.featured-collection.settings.columns_mobile.options__2.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.featured-collection.settings.swipe_on_mobile.label"
    },
    {
      "type": "number",
      "id": "padding_top_mobile",
      "label": "Padding top",
      "info": "unit: px"
    },
    {
      "type": "number",
      "id": "padding_bottom_mobile",
      "label": "Padding bottom",
      "info": "unit: px"
    },
    {
      "type": "header",
      "content": "Desktop layout",
      "info": "Devices with screens >= 750px"
    },
    {
      "type": "select",
      "id": "text_alignment_desktop",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left",
      "label": "Header alignment (desktop)"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 4,
      "label": "t:sections.featured-collection.settings.columns_desktop.label"
    },
    {
      "type": "checkbox",
      "id": "enable_desktop_slider",
      "label": "t:sections.featured-collection.settings.enable_desktop_slider.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.featured-collection.settings.full_width.label",
      "default": false
    },
    {
      "type": "number",
      "id": "padding_top_desktop",
      "label": "Padding top",
      "info": "unit: px"
    },
    {
      "type": "number",
      "id": "padding_bottom_desktop",
      "label": "Padding bottom",
      "info": "unit: px"
    },
    {
      "type": "header",
      "content": "Other section settings"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "t:sections.featured-collection.settings.title.default",
      "label": "Heading"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__h6.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__h5.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__h4.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__h3.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h1",
      "label": "Heading size"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.featured-collection.settings.description.label"
    },
    {
      "type": "select",
      "id": "description_style",
      "label": "t:sections.featured-collection.settings.description_style.label",
      "options": [
        {
          "value": "body",
          "label": "t:sections.featured-collection.settings.description_style.options__1.label"
        },
        {
          "value": "subtitle",
          "label": "t:sections.featured-collection.settings.description_style.options__2.label"
        },
        {
          "value": "uppercase",
          "label": "t:sections.featured-collection.settings.description_style.options__3.label"
        }
      ],
      "default": "body"
    },
    {
      "type": "select",
      "id": "tab_link_size",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__h6.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__h5.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__h4.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__h3.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        }
      ],
      "default": "",
      "label": "Tab link size"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 25,
      "step": 1,
      "default": 8,
      "label": "t:sections.featured-collection.settings.products_to_show.label"
    },
    {
      "type": "select",
      "id": "view_all_style",
      "options": [
        {
          "value": "no",
          "label": "No"
        },
        {
          "value": "link underlined-link",
          "label": "Link"
        },
        {
          "value": "button button--primary",
          "label": "Solid"
        },
        {
          "value": "button button--secondary",
          "label": "Outline"
        },
        {
          "value": "button button-1",
          "label": "Custom 1"
        },
        {
          "value": "button button-2",
          "label": "Custom 2"
        },
        {
          "value": "button button-3",
          "label": "Custom 3"
        },
        {
          "value": "button button-4",
          "label": "Custom 4"
        },
        {
          "value": "button button-5",
          "label": "Custom 5"
        }
      ],
      "default": "no",
      "label": "View all button",
      "info": "Only shows if collection has more products than shown"
    },
    {
      "type": "checkbox",
      "id": "hide_cart_products",
      "label": "Hide products placed in cart",
      "default": false
    },  
    {
      "type": "select",
      "id": "quick_add",
      "default": "none",
      "label": "t:sections.main-collection-product-grid.settings.quick_add.label",
      "info": "t:sections.main-collection-product-grid.settings.quick_add.info",
      "options": [
        {
          "value": "none",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_1"
        },
        {
          "value": "standard",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_2"
        },
        {
          "value": "bulk",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_3"
        }
      ]
    },
    {
      "type": "select",
      "id": "quick_add_button_style",
      "options": [
        {
          "value": "button button--primary",
          "label": "Solid"
        },
        {
          "value": "button button--secondary",
          "label": "Outline"
        },
        {
          "value": "link underlined-link",
          "label": "Link"
        },
        {
          "value": "button button-1",
          "label": "Custom 1"
        },
        {
          "value": "button button-2",
          "label": "Custom 2"
        },
        {
          "value": "button button-3",
          "label": "Custom 3"
        },
        {
          "value": "button button-4",
          "label": "Custom 4"
        },
        {
          "value": "button button-5",
          "label": "Custom 5"
        }
      ],
      "default": "button button--primary",
      "label": "Quick add button style"
    },
    {
      "type": "header",
      "content": "Optional: Product card overrides"
    },
    {
      "type": "paragraph",
      "content": "Product cards are configured in theme settings. To override values for this section only, use the inputs below."
    },
    {
      "type": "text",
      "id": "image_ratio",
      "label": "t:sections.featured-collection.settings.image_ratio.label",
      "info": "Options: square, portrait, adapt"
    },
    {
      "type": "text",
      "id": "image_shape",
      "label": "Image shape",
      "info": "Options: default, arch, blob, chevronleft, chevronright, diamond, parallelogram, round"
    },
    {
      "type": "text",
      "id": "show_secondary_image",
      "label": "t:sections.featured-collection.settings.show_secondary_image.label",
      "info": "Options: true, false"
    },
    {
      "type": "text",
      "id": "card_heading_font_size",
      "label": "Card heading font size",
      "info": "Options: h1, h2, h3, h4, h5, h6 etc."
    },
    {
      "type": "text",
      "id": "show_vendor",
      "label": "t:sections.featured-collection.settings.show_vendor.label",
      "info": "Options: true, false"
    },
    {
      "type": "text",
      "id": "show_rating",
      "label": "t:sections.featured-collection.settings.show_rating.label",
      "info": "Options: true, false"
    },
    {
      "type": "text",
      "id": "quick_add_behavior",
      "label": "Quick add behavior"
    },
    {
      "type": "text",
      "id": "show_alternative_title",
      "label": "Show alternative title",
      "info": "Options: true, false"
    },
    {
      "type": "text",
      "id": "show_card_product_custom_field",
      "label": "Show custom field",
      "info": "Options: true, false"
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    },
  ],
  "blocks": [
    {
      "type": "featured_collection",
      "name": "Featured Collection",
      "limit": 5,
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
        },
        {
          "type": "product_list",
          "id": "product_list",
          "label": "Products",
          "info": "Overrides collection setting above"
        },
        {
          "type": "text",
          "id": "product_tags",
          "label": "Filter collection with tags (optional)"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "default": "t:sections.featured-collection.settings.title.default",
          "label": "Tab heading",
          "info": "Replaces name of collection"
        },
        {
          "type": "liquid",
          "id": "title_liquid",
          "label": "Tab heading liquid",
          "info": "Overrides tab heading above"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "t:sections.featured-collection.settings.description.label",
          "info": "Overrides collection description if checkbox below is activated"
        },
        {
          "type": "checkbox",
          "id": "show_description",
          "label": "t:sections.featured-collection.settings.show_description.label",
          "default": false
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.featured-collection.presets.name",
      "blocks": [
        {
          "type": "featured_collection",
          "settings": {
            "collection": "",
            "title": "Featured collection"
          }
        }
      ]
    }
  ]
}
{% endschema %}
