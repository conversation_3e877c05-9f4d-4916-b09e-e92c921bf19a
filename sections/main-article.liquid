{% comment %}
  bs-add
  - new liquid block
  - ability to override text with liquid
  - support for new block types
{% endcomment %}

{{ 'section-blog-post.css' | asset_url | stylesheet_tag }}

{% comment %}
  bs-add
  - support for new block types
{% endcomment %}
{{ 'video-section.css' | asset_url | stylesheet_tag }}
{{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}
{{ 'section-rich-text.css' | asset_url | stylesheet_tag }}
{{ 'section-image-banner.css' | asset_url | stylesheet_tag }}

<article class="article-template color-{{ section.settings.color_scheme }}">
  {%- for block in section.blocks -%}
    {%- case block.type -%}
      {%- when 'custom_liquid' -%}
        {{ block.settings.custom_liquid }}
      {%- when '@app' -%}
        <div class="page-width page-width--narrow">
          {% render block %}
        </div>
      {%- when 'featured_image' -%}
        {%- if article.image -%}
          <div
            class="article-template__hero-container{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}"
            {{ block.shopify_attributes }}
          >
            <div
              class="article-template__hero-{{ block.settings.image_height }} media"
              {% if block.settings.image_height == 'adapt' and article.image %}
                style="padding-bottom: {{ 1 | divided_by: article.image.aspect_ratio | times: 100 }}%;"
              {% endif %}
            >
              <img
                srcset="
                  {% if article.image.width >= 350 %}{{ article.image | image_url: width: 350 }} 350w,{% endif %}
                  {% if article.image.width >= 750 %}{{ article.image | image_url: width: 750 }} 750w,{% endif %}
                  {% if article.image.width >= 1100 %}{{ article.image | image_url: width: 1100 }} 1100w,{% endif %}
                  {% if article.image.width >= 1500 %}{{ article.image | image_url: width: 1500 }} 1500w,{% endif %}
                  {% if article.image.width >= 2200 %}{{ article.image | image_url: width: 2200 }} 2200w,{% endif %}
                  {% if article.image.width >= 3000 %}{{ article.image | image_url: width: 3000 }} 3000w,{% endif %}
                  {{ article.image | image_url }} {{ article.image.width }}w
                "
                sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 }}px, (min-width: 750px) calc(100vw - 10rem), 100vw"
                src="{{ article.image | image_url: width: 1100 }}"
                loading="eager"
                fetchpriority="high"
                width="{{ article.image.width }}"
                height="{{ article.image.height }}"
                alt="{{ article.image.alt | escape }}"
              >
            </div>
          </div>
        {%- endif -%}

      {%- when 'title' -%}
        <header
          class="page-width page-width--narrow{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}"
          {{ block.shopify_attributes }}
        >
          <h1
            class="article-template__title {{ block.settings.title_font_size }}"
          >
            {{ article.title | escape }}
          </h1>
          {%- if block.settings.blog_show_date -%}
            <span class="circle-divider caption-with-letter-spacing">
              {{- article.published_at | time_tag: format: 'date' -}}
            </span>
          {%- endif -%}
          {%- if block.settings.blog_show_author -%}
            <span class="caption-with-letter-spacing">
              <span>{{ article.author }}</span>
            </span>
          {%- endif -%}
        </header>

      {%- when 'content' -%}
        <div
          class="article-template__content page-width page-width--narrow rte{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
          {{ block.shopify_attributes }}
        >
          {{ article.content }}
        </div>

      {%- when 'share' -%}
        <div
          class="article-template__social-sharing page-width page-width--narrow{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
          {{ block.shopify_attributes }}
        >
          {% assign share_url = request.origin | append: article.url %}
          {% render 'share-button', block: block, share_link: share_url %}
        </div>

      {%- when 'video' -%}
        {%- liquid
          assign video_id = block.settings.video.id | append: block.id
          assign video_alt = block.settings.video.alt
          assign alt = 'sections.video.load_video' | t: description: video_alt | escape
          assign poster = block.settings.video.preview_image | default: block.settings.cover_image
        
          if block.settings.video != null
            assign ratio_diff = block.settings.video.aspect_ratio | minus: poster.aspect_ratio | abs
            if ratio_diff < 0.01 and ratio_diff > 0
              assign fix_ratio = true
            endif
          endif
        -%}

        {%- capture sizes -%}
          (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 }}px, (min-width: 750px)
          calc(100vw - 10rem), 100vw
        {%- endcapture -%}
        
        <div 
          class="article-template__content--block article-template__content--block--{{ block.type | handleize }} page-width page-width--narrow"
          {{ block.shopify_attributes }}
        >
          <deferred-media
            class="video-section__media deferred-media gradient global-media-settings{% if fix_ratio %} media-fit-cover{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
            data-media-id="{{ video_id }}"
            {% if poster != null %}
              style="--ratio-percent: {{ 1 | divided_by: poster.aspect_ratio | times: 100 }}%;"
            {% endif %}
          >
            <button
              id="Deferred-Poster-Modal-{{ video_id }}"
              class="video-section__poster media deferred-media__poster media--landscape"
              type="button"
              aria-label="{{ alt }}"
            >
              {%- if poster != null -%}
                {{
                  poster
                  | image_url: width: 3840
                  | image_tag: sizes: sizes, widths: '375, 750, 1100, 1500, 1780, 2000, 3000, 3840', alt: alt
                }}
              {%- else -%}
                {{ 'hero-apparel-3' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
              {%- endif -%}
              <span class="deferred-media__poster-button motion-reduce">
                <span class="svg-wrapper">
                  {{- 'icon-play.svg' | inline_asset_content -}}
                </span>
              </span>
            </button>
            <template>
              {{
                block.settings.video
                | video_tag:
                  image_size: '1100x',
                  autoplay: true,
                  loop: false,
                  controls: true,
                  muted: false
              }}
            </template>
          </deferred-media>
          {%- if block.settings.video_subtitle != blank -%}
            <p class="caption">
              {{ block.settings.video_subtitle }}
            </p>
          {%- endif -%}
        </div>
      {%- when 'heading' -%}
        <div 
          class="article-template__content--block article-template__content--block--{{ block.type | handleize }} page-width page-width--narrow"
          {{ block.shopify_attributes }}
        >
          <h2
            class="rich-text__heading rte inline-richtext {{ block.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %} {{ block.settings.text_indent }}"
            {{ block.shopify_attributes }}
            {% if settings.animations_reveal_on_scroll %}
              data-cascade
              style="--animation-order: {{ forloop.index }};"
            {% endif %}
          >
            {{ block.settings.heading }}
          </h2>
        </div>
      {%- when 'caption' -%}
        <div 
          class="article-template__content--block article-template__content--block--{{ block.type | handleize }} page-width page-width--narrow"
          {{ block.shopify_attributes }}
        >
          <p
            class="rich-text__caption {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
            {% if settings.animations_reveal_on_scroll %}
              data-cascade
              style="--animation-order: {{ forloop.index }};"
            {% endif %}
          >
            {{ block.settings.caption | escape }}
          </p>
        </div>
      {%- when 'text' -%}
        <div 
          class="article-template__content--block article-template__content--block--{{ block.type | handleize }} page-width page-width--narrow"
          {{ block.shopify_attributes }}
        >
          <div
            class="rich-text__text rte{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %} {{ block.settings.text_indent }}"
            {% if settings.animations_reveal_on_scroll %}
              data-cascade
              style="--animation-order: {{ forloop.index }};"
            {% endif %}
          >
            {{ block.settings.text }}
          </div>
        </div>
      {%- when 'image' -%}
        <div 
          class="article-template__content--block article-template__content--block--{{ block.type | handleize }} page-width page-width--narrow"
          {{ block.shopify_attributes }}
        >
          {%- style -%}
            @media screen and (max-width: 749px) {
              #Banner-{{ section.id }}{{ block.id }}.banner {
                gap: {{ block.settings.stack_images_spacing_on_mobile }}rem;
              }
              #Banner-{{ section.id }}{{ block.id }}.banner:not(.banner--stacked) {
                justify-content: space-between;
              }
              #Banner-{{ section.id }}{{ block.id }}.banner:not(.banner--stacked) .banner__media-half {
                max-width: calc(50% - calc({{ block.settings.stack_images_spacing_on_mobile }}rem / 2));
              }
            }
        
            @media screen and (min-width: 750px) {
              #Banner-{{ section.id }}{{ block.id }}.banner {
                justify-content: space-between;
              }
              #Banner-{{ section.id }}{{ block.id }}.banner .banner__media-half {
                max-width: calc(50% - calc({{ block.settings.stack_images_spacing_on_desktop }}rem / 2));
              }
            }
          {%- endstyle -%}

          {%- if block.settings.image_height == 'adapt' and block.settings.image != blank -%}
            {%- style -%}
              @media screen and (max-width: 749px) {
                #Banner-{{ section.id }}{{ block.id }}::before,
                #Banner-{{ section.id }}{{ block.id }} .banner__media::before,
                #Banner-{{ section.id }}{{ block.id }}:not(.banner--mobile-bottom) .banner__content::before {
                  padding-bottom: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%;
                  content: '';
                  display: block;
                }
              }
          
              @media screen and (min-width: 750px) {
                #Banner-{{ section.id }}{{ block.id }}::before,
                #Banner-{{ section.id }}{{ block.id }} .banner__media::before {
                  padding-bottom: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%;
                  content: '';
                  display: block;
                }
              }
            {%- endstyle -%}
          {%- endif -%}
          
          {%- liquid
            assign full_width = '100vw'
            assign widths = '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
          
            if block.settings.image_2 == blank
              assign half_width = '100vw'
              assign stacked_sizes = '100vw'
            else
              assign half_width = '50vw'
              assign stacked_sizes = '(min-width: 750px) 50vw, 100vw'
            endif
            assign fetch_priority = 'auto'
            if section.index == 1
              assign fetch_priority = 'high'
            endif
          -%}
          
          <div
            id="Banner-{{ section.id }}{{ block.id }}"
            class="banner banner--mobile-bottom banner--{{ block.settings.image_height }}{% if block.settings.stack_images_on_mobile and block.settings.image != blank and block.settings.image_2 != blank %} banner--stacked{% endif %}{% if block.settings.image_height == 'adapt' and block.settings.image != blank %} banner--adapt{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}"
          >
            {%- if block.settings.image != blank -%}
              <div class="banner__media media{% if block.settings.image == blank and block.settings.image_2 == blank %} placeholder{% endif %}{% if block.settings.image_2 != blank %} banner__media-half{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}">
                {%- liquid
                  assign image_height = block.settings.image.width | divided_by: block.settings.image.aspect_ratio
                  if block.settings.image_2 != blank
                    assign image_class = 'banner__media-image-half'
                  endif
                  if block.settings.image_2 != blank and block.settings.stack_images_on_mobile
                    assign sizes = stacked_sizes
                  elsif block.settings.image_2 != blank
                    assign sizes = half_width
                  else
                    assign sizes = full_width
                  endif
                -%}
                {{
                  block.settings.image
                  | image_url: width: 3840
                  | image_tag:
                    width: block.settings.image.width,
                    height: image_height,
                    class: image_class,
                    sizes: sizes,
                    widths: widths,
                    fetchpriority: fetch_priority
                }}
              </div>
            {%- elsif block.settings.image_2 == blank -%}
              <div class="banner__media media{% if block.settings.image == blank and block.settings.image_2 == blank %} placeholder{% endif %}{% if block.settings.image_2 != blank %} banner__media-half{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}">
                {{ 'hero-apparel-1' | placeholder_svg_tag: 'placeholder-svg' }}
              </div>
            {%- endif -%}
            {%- if block.settings.image_2 != blank -%}
              <div class="banner__media media{% if block.settings.image != blank %} banner__media-half{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}">
                {%- liquid
                  assign image_height_2 = block.settings.image_2.width | divided_by: block.settings.image_2.aspect_ratio
                  if block.settings.image != blank
                    assign image_class_2 = 'banner__media-image-half'
                  endif
                  if block.settings.image != blank and block.settings.stack_images_on_mobile
                    assign sizes = stacked_sizes
                  elsif block.settings.image_2 != blank
                    assign sizes = half_width
                  else
                    assign sizes = full_width
                  endif
                -%}
                {{
                  block.settings.image_2
                  | image_url: width: 3840
                  | image_tag:
                    width: block.settings.image_2.width,
                    height: image_height_2,
                    class: image_class_2,
                    sizes: sizes,
                    widths: widths,
                    fetchpriority: fetch_priority
                }}
              </div>
            {%- endif -%}
          </div>
          {%- if block.settings.image_subtitle != blank -%}
            <p class="caption">
              {{ block.settings.image_subtitle }}
            </p>
          {%- endif -%}
        </div>
    {%- endcase -%}
  {%- endfor -%}

  <div class="article-template__back element-margin-top center{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
    <a href="{{ blog.url }}" class="article-template__link link animate-arrow">
      <span class="icon-wrap">
        <span class="svg-wrapper">
          {{- 'icon-arrow.svg' | inline_asset_content -}}
        </span>
      </span>
      {{ 'blogs.article.back_to_blog' | t: title: blog.title | escape }}
    </a>
  </div>
  {%- if blog.comments_enabled? -%}
    <div class="article-template__comment-wrapper background-secondary">
      <div
        id="comments"
        class="page-width page-width--narrow{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
      >
        {%- if article.comments_count > 0 -%}
          {%- assign anchorId = '#Comments-' | append: article.id -%}

          <h2 id="Comments-{{ article.id }}" tabindex="-1">
            {{ 'blogs.article.comments' | t: count: article.comments_count }}
          </h2>
          {% paginate article.comments by 5 %}
            <div class="article-template__comments">
              {%- if comment.status == 'pending' and comment.content -%}
                <article id="{{ comment.id }}" class="article-template__comments-comment">
                  {{ comment.content }}
                  <footer class="right">
                    <span class="circle-divider caption-with-letter-spacing">{{ comment.author }}</span>
                  </footer>
                </article>
              {%- endif -%}

              {%- for comment in article.comments -%}
                <article id="{{ comment.id }}" class="article-template__comments-comment">
                  {{ comment.content }}
                  <footer class="right">
                    <span class="circle-divider caption-with-letter-spacing">{{ comment.author }}</span
                    ><span class="caption-with-letter-spacing">
                      {{- comment.created_at | time_tag: format: 'date' -}}
                    </span>
                  </footer>
                </article>
              {%- endfor -%}
              {% render 'pagination', paginate: paginate, anchor: anchorId %}
            </div>
          {% endpaginate %}
        {%- endif -%}
        {% form 'new_comment', article %}
          {%- liquid
            assign post_message = 'blogs.article.success'
            if blog.moderated? and comment.status == 'unapproved'
              assign post_message = 'blogs.article.success_moderated'
            endif
          -%}
          <h2>{{ 'blogs.article.comment_form_title' | t }}</h2>
          {%- if form.errors -%}
            <div class="form__message" role="alert">
              <h3 class="form-status caption-large text-body" tabindex="-1" autofocus>
                <span class="svg-wrapper">
                  {{- 'icon-error.svg' | inline_asset_content -}}
                </span>
                {{ 'templates.contact.form.error_heading' | t }}
              </h3>
            </div>
            <ul class="form-status-list caption-large">
              {%- for field in form.errors -%}
                <li>
                  <a href="#CommentForm-{{ field }}" class="link">
                    {%- if form.errors.translated_fields[field] contains 'author' -%}
                      {{ 'blogs.article.name' | t }}
                    {%- elsif form.errors.translated_fields[field] contains 'body' -%}
                      {{ 'blogs.article.message' | t }}
                    {%- else -%}
                      {{ form.errors.translated_fields[field] }}
                    {%- endif -%}
                    {{ form.errors.messages[field] }}
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          {%- elsif form.posted_successfully? -%}
            <div class="form-status-list form__message" role="status">
              <h3 class="form-status" tabindex="-1" autofocus>
                <span class="svg-wrapper">
                  {{- 'icon-success.svg' | inline_asset_content -}}
                </span>
                {{ post_message | t }}
              </h3>
            </div>
          {%- endif -%}

          <div
            {% if blog.moderated? == false %}
              class="article-template__comments-fields"
            {% endif %}
          >
            <div class="article-template__comment-fields">
              <div class="field field--with-error">
                <input
                  type="text"
                  name="comment[author]"
                  id="CommentForm-author"
                  class="field__input"
                  autocomplete="name"
                  value="{{ form.author }}"
                  aria-required="true"
                  required
                  {% if form.errors contains 'author' %}
                    aria-invalid="true"
                    aria-describedby="CommentForm-author-error"
                  {% endif %}
                  placeholder="{{ 'blogs.article.name' | t }}"
                >
                <label class="field__label" for="CommentForm-author">
                  {{- 'blogs.article.name' | t }}
                  <span aria-hidden="true">*</span></label
                >
                {%- if form.errors contains 'author' -%}
                  <small id="CommentForm-author-error">
                    <span class="form__message">
                      <span class="svg-wrapper">
                        {{- 'icon-error.svg' | inline_asset_content -}}
                      </span>
                      {{- 'blogs.article.name' | t }}
                      {{ form.errors.messages.author }}.</span
                    >
                  </small>
                {%- endif -%}
              </div>
              <div class="field field--with-error">
                <input
                  type="email"
                  name="comment[email]"
                  id="CommentForm-email"
                  autocomplete="email"
                  class="field__input"
                  value="{{ form.email }}"
                  autocorrect="off"
                  autocapitalize="off"
                  aria-required="true"
                  required
                  {% if form.errors contains 'email' %}
                    aria-invalid="true"
                    aria-describedby="CommentForm-email-error"
                  {% endif %}
                  placeholder="{{ 'blogs.article.email' | t }}"
                >
                <label class="field__label" for="CommentForm-email">
                  {{- 'blogs.article.email' | t }}
                  <span aria-hidden="true">*</span></label
                >
                {%- if form.errors contains 'email' -%}
                  <small id="CommentForm-email-error">
                    <span class="form__message">
                      <span class="svg-wrapper">
                        {{- 'icon-error.svg' | inline_asset_content -}}
                      </span>
                      {{- 'blogs.article.email' | t }}
                      {{ form.errors.messages.email }}.</span
                    >
                  </small>
                {%- endif -%}
              </div>
            </div>
            <div class="field field--with-error field--textarea">
              <textarea
                rows="5"
                name="comment[body]"
                id="CommentForm-body"
                class="text-area field__input"
                aria-required="true"
                required
                {% if form.errors contains 'body' %}
                  aria-invalid="true"
                  aria-describedby="CommentForm-body-error"
                {% endif %}
                placeholder="{{ 'blogs.article.message' | t }}"
              >{{ form.body }}</textarea>
              <label class="form__label field__label" for="CommentForm-body">
                {{- 'blogs.article.message' | t }}
                <span aria-hidden="true">*</span></label
              >
            </div>
            {%- if form.errors contains 'body' -%}
              <small id="CommentForm-body-error">
                <span class="form__message">
                  <span class="svg-wrapper">
                    {{- 'icon-error.svg' | inline_asset_content -}}
                  </span>
                  {{- 'blogs.article.message' | t }}
                  {{ form.errors.messages.body }}.</span
                >
              </small>
            {%- endif -%}
          </div>
          {%- if blog.moderated? -%}
            <p class="article-template__comment-warning caption">{{ 'blogs.article.moderated' | t }}</p>
          {%- endif -%}
          <input type="submit" class="button" value="{{ 'blogs.article.post' | t }}">
        {% endform %}
      </div>
    </div>
  {%- endif -%}
</article>

<script type="application/ld+json">
  {{ article | structured_data }}
</script>

{% schema %}
{
  "name": "t:sections.main-article.name",
  "tag": "section",
  "class": "section section--main-article",
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    }
  ],
  "blocks": [
    {
      "type": "custom_liquid",
      "name": "t:sections.custom-liquid.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "default": "My custom liquid",
          "label": "Title",
          "info": "Only used to organize custom liquid in side bar."
        },
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "@app"
    },
    {
      "type": "featured_image",
      "name": "t:sections.main-article.blocks.featured_image.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "image_height",
          "options": [
            {
              "value": "adapt",
              "label": "t:sections.main-article.blocks.featured_image.settings.image_height.options__1.label"
            },
            {
              "value": "small",
              "label": "t:sections.main-article.blocks.featured_image.settings.image_height.options__2.label"
            },
            {
              "value": "medium",
              "label": "t:sections.main-article.blocks.featured_image.settings.image_height.options__3.label"
            },
            {
              "value": "large",
              "label": "t:sections.main-article.blocks.featured_image.settings.image_height.options__4.label"
            }
          ],
          "default": "adapt",
          "label": "t:sections.main-article.blocks.featured_image.settings.image_height.label",
          "info": "t:sections.main-article.blocks.featured_image.settings.image_height.info"
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.main-article.blocks.title.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "title_font_size",
          "options": [
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__h6.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__h5.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__h4.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__h3.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "hxl",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "hxxl",
              "label": "t:sections.all.heading_size.options__5.label"
            }
          ],
          "default": "h1",
          "label": "Title font size"
        },
        {
          "type": "checkbox",
          "id": "blog_show_date",
          "default": true,
          "label": "t:sections.main-article.blocks.title.settings.blog_show_date.label"
        },
        {
          "type": "checkbox",
          "id": "blog_show_author",
          "default": false,
          "label": "t:sections.main-article.blocks.title.settings.blog_show_author.label"
        }
      ]
    },
    {
      "type": "content",
      "name": "t:sections.main-article.blocks.content.name",
      "limit": 1
    },
    {
      "type": "share",
      "name": "t:sections.main-article.blocks.share.name",
      "limit": 2,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "share_label",
          "label": "Text",
          "default": "t:sections.main-article.blocks.share.settings.text.default"
        },
        {
          "type": "liquid",
          "id": "share_label_liquid",
          "label": "Text liquid",
          "info": "Overrides Text above"
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-article.blocks.share.settings.featured_image_info.content"
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-article.blocks.share.settings.title_info.content"
        }
      ]
    },
    {
      "type": "video",
      "name": "Video",
      "settings": [
        {
          "type": "header",
          "content": "t:sections.video.settings.header__1.content"
        },
        {
          "type": "video",
          "id": "video",
          "label": "t:sections.video.settings.video.label"
        },
        {
          "type": "text",
          "id": "video_subtitle",
          "label": "Video subtitle"
        }
      ]
    },
    {
      "type": "heading",
      "name": "t:sections.rich-text.blocks.heading.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "This is a heading block",
          "label": "t:sections.rich-text.blocks.heading.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__h6.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__h5.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__h4.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__h3.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "hxl",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "hxxl",
              "label": "t:sections.all.heading_size.options__5.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        },
        {
          "type": "select",
          "id": "text_indent",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "indent--mobile",
              "label": "Mobile only"
            },
            {
              "value": "indent--desktop",
              "label": "Desktop only"
            },
            {
              "value": "indent--mobile indent--desktop",
              "label": "Both"
            }
          ],
          "default": "",
          "label": "Text indent"
        }
      ]
    },
    {
      "type": "caption",
      "name": "t:sections.rich-text.blocks.caption.name",
      "settings": [
        {
          "type": "text",
          "id": "caption",
          "default": "Add a tagline",
          "label": "t:sections.rich-text.blocks.caption.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "subtitle",
              "label": "t:sections.rich-text.blocks.caption.settings.text_style.options__1.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.rich-text.blocks.caption.settings.text_style.options__2.label"
            }
          ],
          "default": "caption-with-letter-spacing",
          "label": "t:sections.rich-text.blocks.caption.settings.text_style.label"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "small",
              "label": "t:sections.rich-text.blocks.caption.settings.caption_size.options__1.label"
            },
            {
              "value": "medium",
              "label": "t:sections.rich-text.blocks.caption.settings.caption_size.options__2.label"
            },
            {
              "value": "large",
              "label": "t:sections.rich-text.blocks.caption.settings.caption_size.options__3.label"
            }
          ],
          "default": "medium",
          "label": "t:sections.rich-text.blocks.caption.settings.caption_size.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.rich-text.blocks.text.name",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>This is a text block</p>",
          "label": "t:sections.rich-text.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_indent",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "indent--mobile",
              "label": "Mobile only"
            },
            {
              "value": "indent--desktop",
              "label": "Desktop only"
            },
            {
              "value": "indent--mobile indent--desktop",
              "label": "Both"
            }
          ],
          "default": "",
          "label": "Text indent"
        }
      ]
    },
    {
      "type": "image",
      "name": "t:sections.collage.blocks.image.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.image-banner.settings.image.label"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "t:sections.image-banner.settings.image_2.label"
        },
        {
          "type": "text",
          "id": "image_subtitle",
          "label": "Media subtitle"
        },
        {
          "type": "select",
          "id": "image_height",
          "options": [
            {
              "value": "adapt",
              "label": "t:sections.image-banner.settings.image_height.options__1.label"
            },
            {
              "value": "small",
              "label": "t:sections.image-banner.settings.image_height.options__2.label"
            },
            {
              "value": "medium",
              "label": "t:sections.image-banner.settings.image_height.options__3.label"
            },
            {
              "value": "large",
              "label": "t:sections.image-banner.settings.image_height.options__4.label"
            }
          ],
          "default": "medium",
          "label": "t:sections.image-banner.settings.image_height.label",
          "info": "t:sections.image-banner.settings.image_height.info"
        },
        {
          "type": "checkbox",
          "id": "stack_images_on_mobile",
          "default": true,
          "label": "t:sections.image-banner.settings.stack_images_on_mobile.label"
        },
        {
          "type": "range",
          "id": "stack_images_spacing_on_mobile",
          "min": 0,
          "max": 5,
          "step": 0.5,
          "unit": "rem",
          "label": "Stacked image spacing (mobile)",
          "info": "Set spacing in between stacked images",
          "default": 1
        },
        {
          "type": "range",
          "id": "stack_images_spacing_on_desktop",
          "min": 0,
          "max": 5,
          "step": 0.5,
          "unit": "rem",
          "label": "Stacked image spacing (desktop)",
          "info": "Set spacing in between stacked images",
          "default": 1
        },
      ]
    },
  ]
}
{% endschema %}