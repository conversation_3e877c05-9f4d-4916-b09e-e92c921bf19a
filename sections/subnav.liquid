{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}

  .section--subnav {
    position: sticky;
    top: var(--header-bottom-position, 0px);
    z-index: 2;
  }

  /* prevents layer issues */
  body.overflow-hidden .section--subnav,
  body.overflow-hidden-mobile .section--subnav,
  body.overflow-hidden-tablet .section--subnav {
    display: none;
  }

  .subnav-wrapper {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
  }

  .subnav-wrapper .button {
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

{%- endstyle -%}

<div class="{{ section.settings.custom_css_class }}">
  <div class="subnav__container color-{{ section.settings.color_scheme }} gradient">
    <div class="{{ section.settings.section_width }}">
      <div class="section-{{ section.id }}-padding {{ section.settings.section_alignment }}">
        <div class="subnav-wrapper">
          {%- if section.settings.custom_liquid != blank -%}
            <div>
              {{ section.settings.custom_liquid }}
            </div>
          {%- else -%}
            <h2 class="{{ section.settings.text_style }}">{{ section.settings.text_liquid | default: section.settings.text }}</h2>
  
            {%- assign button_label_content = section.settings.button_label_liquid | default: section.settings.button_label -%}
            {%- if button_label_content != blank -%}
              <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true" {% else %}
                href="{{ section.settings.button_link }}" {% endif %}
                class="{{ section.settings.button_style }}">
                <span>{{ button_label_content }}</span>
              </a>
            {%- endif -%}
          {%- endif -%}
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Subnav bar",
  "tag": "section",
  "class": "section section--subnav",
  "disabled_on": {
    "groups": ["header"]
  },
  "limit": 1,
  "settings": [
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Color Scheme"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "header",
      "content": "Section width"
    },
    {
      "type": "select",
      "id": "section_width",
      "options": [
        {
          "value": "full-width",
          "label": "Full width on all devices"
        },
        {
          "value": "page-width",
          "label": "Page width (based on theme settings)"
        },
        {
          "value": "page-width page-width--narrow",
          "label": "Page width narrow (tablet and desktop only)"
        }
      ],
      "default": "page-width",
      "label": "Section width"
    },
    {
      "type": "select",
      "id": "section_alignment",
      "options": [
        {
          "value": "full-width",
          "label": "Full width"
        },       
        {
          "value": "left-one-third",
          "label": "Left one third"
        },
        {
          "value": "left-one-half",
          "label": "Left one half"
        },
        {
          "value": "left-two-thirds",
          "label": "Left two thirds"
        },
        {
          "value": "right-one-third",
          "label": "Right one third"
        },
        {
          "value": "right-one-half",
          "label": "Right one half"
        },
        {
          "value": "right-two-thirds",
          "label": "Right two thirds"
        }
      ],
      "default": "full-width",
      "label": "Section alignment",
      "info": "Only applies to desktop (min-width: 990px)"
    },
    {
      "type": "header",
      "content": "Section padding mobile (max-width: 749px)"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 8
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 8
    },
    {
      "type": "range",
      "id": "padding_left_mobile",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Padding left mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_right_mobile",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Padding right mobile",
      "default": 0
    },
    {
      "type": "header",
      "content": "Section padding desktop (min-width: 750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 8
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 8
    },
    {
      "type": "range",
      "id": "padding_left_desktop",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Padding left desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_right_desktop",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "label": "Padding right desktop",
      "default": 0
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "inline_richtext",
      "id": "text",
      "label": "Text"
    },
    {
      "type": "liquid",
      "id": "text_liquid",
      "label": "Text liquid",
      "info": "Overrides text above",
      "default": "{{ page_title }}"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__h6.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__h5.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__h4.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__h3.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h3",
      "label": "Text size"
    },
    {
      "type": "inline_richtext",
      "id": "button_label",
      "default": "Button label",
      "label": "Button label",
      "info": "Leave the label blank to hide the button"
    },
    {
      "type": "liquid",
      "id": "button_label_liquid",
      "label": "Button label liquid",
      "info": "Overrides First button label above"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "button_style",
      "options": [
        {
          "value": "button button--primary",
          "label": "Solid"
        },
        {
          "value": "button button--secondary",
          "label": "Outline"
        },
        {
          "value": "link underlined-link",
          "label": "Link"
        },
        {
          "value": "button button-1",
          "label": "Custom 1"
        },
        {
          "value": "button button-2",
          "label": "Custom 2"
        },
        {
          "value": "button button-3",
          "label": "Custom 3"
        },
        {
          "value": "button button-4",
          "label": "Custom 4"
        },
        {
          "value": "button button-5",
          "label": "Custom 5"
        }
      ],
      "default": "button button--primary",
      "label": "Button style"
    },
    {
      "type": "header",
      "content": "Advanced"
    },
    {
      "type": "paragraph",
      "content": "Use this for section id: '#shopify-section-{{ section.id }}'"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "Custom liquid",
      "info": "Overrides content in bar (text & button from above)"
    }
  ],
  "presets": [
    {
      "name": "Subnav bar"
    }
  ]
}
{% endschema %}