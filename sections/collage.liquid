{% comment %}
  Customizations from Dawn
  - ability to hide/show section
  - ability to have separate padding for desktop/mobile
  - updated product card settings
{% endcomment %}

{{ 'collage.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-modal-video.css' | asset_url | stylesheet_tag }}
{{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

{{ section.settings.liquid_before }}
<div class="color-{{ section.settings.color_scheme }} gradient isolate {{ section.settings.custom_css_class }}">
  <div class="page-width section-{{ section.id }}-padding">
    {%- if section.settings.heading != blank -%}
      <h2 class="collage-wrapper-title inline-richtext {{ section.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
        {{ section.settings.heading }}
      </h2>
    {%- endif -%}
    <div class="collage{% if section.settings.mobile_layout == 'collage' %} collage--mobile{% endif %}">
      {% assign skip_card_product_styles = false %}
      {%- for block in section.blocks -%}
        <div
          class="collage__item collage__item--{{ block.type }} collage__item--{{ section.settings.desktop_layout }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
          {{ block.shopify_attributes }}
          {% if settings.animations_reveal_on_scroll %}
            data-cascade
            style="--animation-order: {{ forloop.index }};"
          {% endif %}
        >
          {%- assign placeholder_image_index = forloop.index0 | modulo: 4 | plus: 1 -%}
          {%- case block.type -%}
            {%- when 'image' -%}
              <div class="collage-card {% if section.settings.card_styles == 'none' %}global-media-settings{% else %}card-wrapper {{ section.settings.card_styles }} color-{{ settings.card_color_scheme }} gradient{% endif %}">
                <div
                  class="media media--transparent ratio"
                  {% if block.settings.image != blank %}
                    style="--ratio-percent: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%"
                  {% else %}
                    style="--ratio-percent: 100%"
                  {% endif %}
                >
                  {%- if block.settings.image != blank -%}
                    {%- liquid
                      if section.settings.desktop_layout == 'left'
                        assign large_block = forloop.first
                      else
                        assign large_block = forloop.last
                      endif

                      assign grid_space_desktop = settings.spacing_grid_horizontal | divided_by: 2 | append: 'px'
                      assign grid_space_mobile = settings.spacing_grid_horizontal | divided_by: 4 | append: 'px'
                    -%}
                    {%- if large_block -%}
                      {%- capture sizes -%}
                        {% if section.blocks.size == 1 -%}(min-width: {{ settings.page_width }}px) calc({{ settings.page_width }}px - 100px){% else %}(min-width: {{ settings.page_width }}px) calc(({{ settings.page_width }}px - 100px) * 2 / 3 - {{ grid_space_desktop }}){% endif %},
                        {% if section.blocks.size == 1 -%}(min-width: 750px) calc(100vw - 100px){% else %}(min-width: 750px) calc((100vw - 100px) * 2 / 3 - {{ grid_space_desktop }}){% endif %},
                        {% if section.blocks.size == 2 and section.settings.mobile_layout == 'collage' -%}calc((100vw - 30px) / 2){% else %}calc(100vw - 30px){% endif %}
                        {%- endcapture -%}
                      {{
                        block.settings.image
                        | image_url: width: 3200
                        | image_tag:
                          sizes: sizes,
                          widths: '50, 75, 100, 150, 200, 300, 400, 500, 750, 1000, 1250, 1500, 1750, 2000, 2250, 2500, 2750, 3000, 3200'
                      }}
                    {%- else -%}
                      {%- capture sizes -%}
                          (min-width: {{ settings.page_width }}px) calc(({{ settings.page_width }}px - 100px) * 1 / 3 - {{ grid_space_desktop }}),
                          (min-width: 750px) calc((100vw - 100px) * 1 / 3 - {{ grid_space_desktop }}),
                          {% if section.settings.mobile_layout == 'collage' %}calc((100vw - 30px) / 2 - {{ grid_space_mobile }}){% else %}calc(100vw - 30px){% endif %}
                        {%- endcapture -%}
                      {{
                        block.settings.image
                        | image_url: width: 3200
                        | image_tag:
                          sizes: sizes,
                          widths: '50, 75, 100, 150, 200, 300, 400, 500, 750, 1000, 1250, 1500, 1750, 2000, 2250, 2500, 2750, 3000, 3200'
                      }}
                    {%- endif -%}
                  {%- else -%}
                    {{ 'detailed-apparel-1' | placeholder_svg_tag: 'placeholder-svg' }}
                  {%- endif -%}
                </div>
              </div>
            {%- when 'product' -%}
              {% liquid
                assign media_aspect_ratio = block.settings.image_ratio | default: settings.card_image_ratio
                assign show_secondary_image = settings.card_show_secondary_image
                if block.settings.show_secondary_image == "true"
                  assign show_secondary_image = true
                elsif block.settings.show_secondary_image == "false"
                  assign show_secondary_image = false
                endif                
                assign show_rating = settings.card_show_rating
                if block.settings.show_rating == "true"
                  assign show_rating = true
                elsif block.settings.show_rating == "false"
                  assign show_rating = false
                endif                
                assign card_heading_font_size = block.settings.card_heading_font_size | default: settings.card_heading_font_size
                assign show_alternative_title = settings.card_show_alternative_title
                if block.settings.show_alternative_title == "true"
                  assign show_alternative_title = true
                elsif block.settings.show_alternative_title == "false"
                  assign show_alternative_title = false
                endif
                assign show_card_product_custom_field = settings.card_show_card_product_custom_field
                if block.settings.show_card_product_custom_field == "true"
                  assign show_card_product_custom_field = true
                elsif block.settings.show_card_product_custom_field == "false"
                  assign show_card_product_custom_field = false
                endif
              %}
              {%- assign placeholder_image = 'product-apparel-' | append: placeholder_image_index -%}
              {% render 'card-product',
                card_product: block.settings.product,
                media_aspect_ratio: media_aspect_ratio,
                show_secondary_image: show_secondary_image,
                show_rating: show_rating,
                extend_height: true,
                card_heading_font_size: card_heading_font_size,
                placeholder_image: placeholder_image,
                skip_styles: skip_card_product_styles,
                show_alternative_title: show_alternative_title,
                show_card_product_custom_field: show_card_product_custom_field
              %}
              {% assign skip_card_product_styles = true %}
            {%- when 'collection' -%}
              {%- assign placeholder_image = 'collection-apparel-' | append: placeholder_image_index -%}
              {% render 'card-collection',
                card_collection: block.settings.collection,
                media_aspect_ratio: 'adapt',
                columns: 2,
                extend_height: true,
                wrapper_class: section.settings.card_styles,
                placeholder_image: placeholder_image
              %}
            {%- when 'video' -%}
              <div class="collage-card {% if section.settings.card_styles == 'none' %}global-media-settings{% else %}{{ section.settings.card_styles }} color-{{ settings.card_color_scheme }} gradient{% endif %}">
                <modal-opener data-modal="#PopupModal-{{ block.id }}">
                  <div class="deferred-media">
                    <button
                      class="deferred-media__poster full-unstyled-link"
                      type="button"
                      aria-label="{{ 'sections.video.load_video' | t: description: block.settings.description | escape }}"
                      aria-haspopup="dialog"
                      data-media-id="{{ block.settings.video_url.id }}"
                    >
                      <div
                        class="media media--transparent ratio"
                        {% if block.settings.cover_image != blank %}
                          style="--ratio-percent: {{ 1 | divided_by: block.settings.cover_image.aspect_ratio | times: 100 }}%"
                        {% else %}
                          style="--ratio-percent: 100%"
                        {% endif %}
                      >
                        <span class="deferred-media__poster-button motion-reduce">
                          <span class="svg-wrapper">
                            {{- 'icon-play.svg' | inline_asset_content -}}
                          </span>
                        </span>

                        {%- if block.settings.cover_image != blank -%}
                          {%- capture sizes -%}
                            (min-width: {{ settings.page_width }}px)
                            {% if section.blocks.size == 1 -%}
                              calc({{ settings.page_width }}px - 100px)
                            {%- else -%}
                              {{- settings.page_width | minus: 100 | times: 0.67 | round }}px
                            {%- endif -%}
                            , (min-width: 750px)
                            {%- if section.blocks.size == 1 %} calc(100vw - 100px){% else %} 500px{% endif -%}
                            , calc(100vw - 30px)
                          {%- endcapture -%}
                          {{
                            block.settings.cover_image
                            | image_url: width: 3000
                            | image_tag: sizes: sizes, widths: '550, 720, 990, 1100, 1500, 2200, 3000'
                          }}
                        {%- else -%}
                          {{ 'hero-apparel-3' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                        {%- endif -%}
                      </div>
                    </button>
                  </div>
                </modal-opener>
                <modal-dialog
                  id="PopupModal-{{ block.id }}"
                  class="modal-video media-modal color-{{ settings.color_schemes | first }}"
                >
                  <div
                    class="modal-video__content"
                    role="dialog"
                    aria-label="{{ block.settings.description | escape }}"
                    aria-modal="true"
                    tabindex="-1"
                  >
                    <button
                      id="ModalClose-{{ block.id }}"
                      type="button"
                      class="modal-video__toggle"
                      aria-label="{{ 'accessibility.close' | t }}"
                    >
                      <span class="svg-wrapper">
                        {{- 'icon-close.svg' | inline_asset_content -}}
                      </span>
                    </button>
                    <div class="modal-video__content-info">
                      <deferred-media class="modal-video__video template-popup">
                        <template>
                          {%- if block.settings.video_url.type == 'youtube' -%}
                            <iframe
                              src="https://www.youtube.com/embed/{{ block.settings.video_url.id }}?enablejsapi=1"
                              class="js-youtube"
                              allow="autoplay; encrypted-media"
                              allowfullscreen
                              title="{{ block.settings.description | escape }}"
                            ></iframe>
                          {%- else -%}
                            <iframe
                              src="https://player.vimeo.com/video/{{ block.settings.video_url.id }}"
                              class="js-vimeo"
                              allow="autoplay; encrypted-media"
                              allowfullscreen
                              title="{{ block.settings.description | escape }}"
                            ></iframe>
                          {%- endif -%}
                        </template>
                      </deferred-media>
                    </div>
                  </div>
                </modal-dialog>
              </div>
          {%- endcase -%}
        </div>
      {%- endfor -%}
    </div>
  </div>
</div>
{{ section.settings.liquid_after }}

{% schema %}
{
  "name": "t:sections.collage.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "t:sections.collage.settings.heading.default",
      "label": "t:sections.collage.settings.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "header",
      "content": "t:sections.collage.settings.header_layout.content"
    },
    {
      "type": "select",
      "id": "desktop_layout",
      "options": [
        {
          "value": "left",
          "label": "t:sections.collage.settings.desktop_layout.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.collage.settings.desktop_layout.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.collage.settings.desktop_layout.label"
    },
    {
      "type": "select",
      "id": "mobile_layout",
      "options": [
        {
          "value": "collage",
          "label": "t:sections.collage.settings.mobile_layout.options__1.label"
        },
        {
          "value": "column",
          "label": "t:sections.collage.settings.mobile_layout.options__2.label"
        }
      ],
      "default": "column",
      "label": "t:sections.collage.settings.mobile_layout.label"
    },
    {
      "type": "select",
      "id": "card_styles",
      "options": [
        {
          "value": "none",
          "label": "t:sections.collage.settings.card_styles.options__1.label"
        },
        {
          "value": "product-card-wrapper",
          "label": "t:sections.collage.settings.card_styles.options__2.label"
        }
      ],
      "default": "product-card-wrapper",
      "info": "t:sections.collage.settings.card_styles.info",
      "label": "t:sections.collage.settings.card_styles.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading_desktop"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 400,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top_desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 400,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom_desktop",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading_mobile"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 400,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top_mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 400,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom_mobile",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "liquid_before",
      "label": "Liquid before",
      "info": "Add custom liquid before all section content"
    },
    {
      "type": "liquid",
      "id": "liquid_after",
      "label": "Liquid after",
      "info": "Add custom liquid after all section content"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "t:sections.collage.blocks.image.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.collage.blocks.image.settings.image.label"
        }
      ]
    },
    {
      "type": "product",
      "name": "t:sections.collage.blocks.product.name",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "t:sections.collage.blocks.product.settings.product.label"
        },
        {
          "type": "header",
          "content": "Optional: Product card overrides"
        },
        {
          "type": "paragraph",
          "content": "Product cards are configured in theme settings. To override values for this section only, use the inputs below."
        },
        {
          "type": "text",
          "id": "image_ratio",
          "label": "t:sections.featured-collection.settings.image_ratio.label",
          "info": "Options: square, portrait, adapt"
        },
        {
          "type": "text",
          "id": "show_secondary_image",
          "label": "t:sections.featured-collection.settings.show_secondary_image.label",
          "info": "Options: true, false"
        },
        {
          "type": "text",
          "id": "card_heading_font_size",
          "label": "Card heading font size",
          "info": "Options: h1, h2, h3, h4, h5, h6 etc."
        },
        {
          "type": "text",
          "id": "show_rating",
          "label": "t:sections.featured-collection.settings.show_rating.label",
          "info": "Options: true, false"
        },
        {
          "type": "text",
          "id": "show_alternative_title",
          "label": "Show alternative title",
          "info": "Options: true, false"
        },
        {
          "type": "text",
          "id": "show_card_product_custom_field",
          "label": "Show custom field",
          "info": "Options: true, false"
        }
      ]
    },
    {
      "type": "collection",
      "name": "t:sections.collage.blocks.collection.name",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.collage.blocks.collection.settings.collection.label"
        }
      ]
    },
    {
      "type": "video",
      "name": "t:sections.collage.blocks.video.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "cover_image",
          "label": "t:sections.collage.blocks.video.settings.cover_image.label"
        },
        {
          "type": "video_url",
          "id": "video_url",
          "accept": ["youtube", "vimeo"],
          "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
          "label": "t:sections.collage.blocks.video.settings.video_url.label",
          "placeholder": "t:sections.collage.blocks.video.settings.video_url.placeholder",
          "info": "t:sections.collage.blocks.video.settings.video_url.info"
        },
        {
          "type": "text",
          "id": "description",
          "default": "t:sections.collage.blocks.video.settings.description.default",
          "label": "t:sections.collage.blocks.video.settings.description.label",
          "info": "t:sections.collage.blocks.video.settings.description.info"
        }
      ]
    }
  ],
  "max_blocks": 3,
  "presets": [
    {
      "name": "t:sections.collage.presets.name",
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "product"
        },
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}
