{% comment %}
  bs-mod
  - added hide/show options
  - gradient options added for new approach to color schemes
  - modified padding to replicate new approach
  - added support for autoplay, mute/unmute, etc with external video library 
{% endcomment %}
{%- unless section.settings.video == blank and section.settings.video_url == blank %}

{{ 'video-section.css' | asset_url | stylesheet_tag }}
{{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

{%- liquid
  assign video_id = section.settings.video.id | default: section.settings.video_url.id
  assign video_alt = section.settings.video.alt | default: section.settings.description
  assign alt = 'sections.video.load_video' | t: description: video_alt | escape
  assign poster = section.settings.video.preview_image | default: section.settings.cover_image

  if section.settings.video != null
    assign ratio_diff = section.settings.video.aspect_ratio | minus: poster.aspect_ratio | abs
    if ratio_diff < 0.01 and ratio_diff > 0
      assign fix_ratio = true
    endif
  endif
-%}

{%- capture sizes -%}
  {% if section.settings.full_width -%}
    100vw
  {%- else -%}
    (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 }}px, (min-width: 750px)
    calc(100vw - 10rem), 100vw
  {%- endif %}
{%- endcapture -%}

<div class="{{ section.settings.custom_css_class }}">
  <div class="color-{{ section.settings.color_scheme }} gradient">
    <div class="video-section isolate{% unless section.settings.full_width %} page-width{% endunless %} section-{{ section.id }}-padding">
      <div
        {% if section.settings.full_width %}
          class="page-width"
        {% endif %}
      >
        {%- unless section.settings.heading == blank -%}
          <div class="title-wrapper title-wrapper--no-top-margin{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
            <h2 class="title inline-richtext {{ section.settings.heading_size }}">{{ section.settings.heading }}</h2>
          </div>
        {%- endunless -%}
      </div>
    {%- if section.settings.autoplay_video and section.settings.video == null -%}
      {%- assign video_html_id = section.id | handleize | replace: '_', '' | replace: '-', '' -%}
      <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" media="print" onload="this.media='all'">
      <style>
        .section-{{ section.id }}-padding {
          --plyr-color-main: rgb(var(--color-background));
  
          --plyr-video-control-color: rgb(var(--color-background));
          --plyr-video-control-background-hover: rgb(var(--color-foreground));
  
          --plyr-video-control-color-hover: rgb(var(--color-background));
          --plyr-video-control-background-hover: rgb(var(--color-foreground));
  
          --plyr-font-family: var(--font-body-family);
        }
  
        {% if section.settings.video_click_action == 'toggle_mute' %}
        .section-{{ section.id }}-padding .plyr__video-wrapper {
          pointer-events: none;
        }
  
        #bs_player_{{ video_html_id }}__container {
          z-index: 9999;
        }
  
        #bs_player_{{ video_html_id }}__container:hover {
          cursor: url('{{ 'icon-mute.png' | asset_img_url: '40x' }}'), auto;
        }
  
        #bs_player_{{ video_html_id }}__container.unmuted:hover {
          cursor: url('{{ 'icon-unmute.png' | asset_img_url: '40x' }}'), auto;
        }
        {% endif %}
      </style>
      <div class="{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
        <div id="bs_player_{{ video_html_id }}__container">
          <div class="plyr__video-embed" id="bs_player_{{ video_html_id }}">
            {%- liquid
              assign video_id = section.settings.video_url.id
              assign loop = ''
              if section.settings.enable_video_looping
                assign loop = '&loop=1&playlist=' | append: video_id
              endif
            -%}
            {%- if section.settings.video_url.type == 'youtube' -%}
              <iframe class="plyr_autoplay_custom" width="100%" height="100%" frameborder="0"
                allowfullscreen
                allowtransparency
                allow="autoplay"
                title="{{ section.settings.description | escape }}"
                src="https://www.youtube.com/embed/{{ video_id }}?enablejsapi=1&autoplay=1{{ loop }}"></iframe>
            {%- else -%}
              <iframe class="plyr_autoplay_custom" width="100%" height="100%" frameborder="0" allow="autoplay; fullscreen; encrypted-media"
                allowfullscreen="allowfullscreen" data-ready="true" data-vimeo-initialized="true"
                src="https://player.vimeo.com/video/{{ video_id }}?autoplay=1{{ loop }}&amp;autopause=0&amp;controls=0&amp;muted=1"></iframe>
            {%- endif -%}
          </div>
        </div>
      </div>
      <script>
        window.addEventListener('DOMContentLoaded', function() {
          (function (a) {
            var c = a.createElement("script");
            var d = a.querySelector("script");
            c.src = "https://cdn.plyr.io/3.7.8/plyr.js";
            c.async = true;
            c.onload = function () {
              const bs_player_{{ video_html_id }}__container = document.querySelector('#bs_player_{{ video_html_id }}__container');
              const bs_player_{{ video_html_id }} = new Plyr('#bs_player_{{ video_html_id }}',{
                keyboard: {
                  global: true,
                },
                vimeo: {
                  referrerPolicy: 'no-referrer',
                  muted: true,
                  autoplay: true,
                },
                clickToPlay: {% if section.settings.video_click_action == 'toggle_pause' %}true{% else %}false{% endif %},
                hideControls: {% if section.settings.video_click_action == 'toggle_pause' %}false{% else %}true{% endif %},
                {% if section.settings.show_video_controls == true %}
                  controls: ['play', 'progress', 'current-time', 'play-large', 'mute'],
                  {% else %}
                  controls: [],
                {% endif %}
                disableContextMenu: true
              });
  
              {% if section.settings.cover_image != blank %}
              bs_player_{{ video_html_id }}.poster = '{{ section.settings.cover_image | image_url }}';
              {% endif %}
              bs_player_{{ video_html_id }}.on('ready', event => {
                bs_player_{{ video_html_id }}.volume = 0;
                bs_player_{{ video_html_id }}.muted = true;
                bs_player_{{ video_html_id }}.play();
              });
  
              {% if section.settings.video_click_action == 'toggle_mute' %}
                bs_player_{{ video_html_id }}__container.addEventListener('click', function(e) {
                  e.preventDefault();
                  bs_player_{{ video_html_id }}__container.classList.toggle('unmuted');
                  if (bs_player_{{ video_html_id }}.muted || bs_player_{{ video_html_id }}.volume == 0) {
                    bs_player_{{ video_html_id }}.volume = 1;
                  } else {
                    bs_player_{{ video_html_id }}.volume = 0;
                  }
                });
              {% endif %}
            };
            d.parentNode.insertBefore(c, d);
          })(document);
        });
      </script>
  
    {%- else -%}
      <deferred-media
        class="video-section__media deferred-media gradient global-media-settings{% if section.settings.full_width %} global-media-settings--full-width{% endif %}{% if fix_ratio %} media-fit-cover{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
        data-media-id="{{ video_id }}"
        {% if poster != null %}
          style="--ratio-percent: {{ 1 | divided_by: poster.aspect_ratio | times: 100 }}%;"
        {% endif %}
      >
        <button
          id="Deferred-Poster-Modal-{{ video_id }}"
          class="video-section__poster media deferred-media__poster media--landscape"
          data-autoplay="{{ section.settings.autoplay_video }}"
          type="button"
          aria-label="{{ alt }}"
        >
          {%- if poster != null -%}
            {{
              poster
              | image_url: width: 3840
              | image_tag: sizes: sizes, widths: '375, 750, 1100, 1500, 1780, 2000, 3000, 3840', alt: alt
            }}
          {%- else -%}
            {{ 'hero-apparel-3' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
          {%- endif -%}
          <span class="deferred-media__poster-button motion-reduce">
            <span class="svg-wrapper">
              {{- 'icon-play.svg' | inline_asset_content -}}
            </span>
          </span>
        </button>
        <template>
          {%- if section.settings.video == null and section.settings.video_url != null -%}
            {%- liquid
              assign loop = ''
              if section.settings.enable_video_looping
                assign loop = '&loop=1&playlist=' | append: video_id
              endif
            -%}
            {%- if section.settings.video_url.type == 'youtube' -%}
              <iframe
                src="https://www.youtube.com/embed/{{ video_id }}?enablejsapi=1&autoplay=1{{ loop }}"
                class="js-youtube"
                allow="autoplay; encrypted-media"
                allowfullscreen
                title="{{ section.settings.description | escape }}"
              ></iframe>
            {%- else -%}
              <iframe
                src="https://player.vimeo.com/video/{{ video_id }}?autoplay=1{{ loop }}"
                class="js-vimeo"
                allow="autoplay; encrypted-media"
                allowfullscreen
                title="{{ section.settings.description | escape }}"
              ></iframe>
            {%- endif -%}
          {%- else -%}
          {{
            section.settings.video
              | video_tag:
                image_size: '1100x',
                autoplay: true,
                loop: section.settings.enable_video_looping,
                controls: section.settings.show_video_controls,
                muted: false
            }}
          {%- endif -%}
        </template>
      </deferred-media>
  
    {%- endif -%}
  
    </div>
  </div>
</div>

{{ section.settings.custom_liquid }}

{%- endunless -%}

{% schema %}
{
  "name": "t:sections.video.name",
  "tag": "section",
  "class": "section section--video",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Heading options"
    },
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "t:sections.video.settings.heading.default",
      "label": "t:sections.video.settings.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__h6.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__h5.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__h4.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__h3.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "header",
      "content": "t:sections.video.settings.header__1.content"
    },
    {
      "type": "video",
      "id": "video",
      "label": "t:sections.video.settings.video.label"
    },
    {
      "type": "header",
      "content": "t:sections.video.settings.header__2.content"
    },
    {
      "type": "paragraph",
      "content": "t:sections.video.settings.paragraph.content"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "accept": ["youtube", "vimeo"],
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
      "label": "t:sections.video.settings.video_url.label",
      "info": "t:sections.video.settings.video_url.info"
    },
    {
      "type": "image_picker",
      "id": "cover_image",
      "label": "t:sections.video.settings.cover_image.label"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "label": "t:sections.video.settings.enable_video_looping.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "autoplay_video",
      "label": "Autoplay video",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_video_controls",
      "label": "Show video controls",
      "default": true
    },
    {
      "type": "text",
      "id": "description",
      "label": "t:sections.video.settings.description.label",
      "info": "t:sections.video.settings.description.info"
    },
    {
      "type": "header",
      "content": "Video advanced settings",
      "info": "Only works for embeded videos"
    },
    {
      "type": "select",
      "id": "video_click_action",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "toggle_mute",
          "label": "Toggle mute"
        },
        {
          "value": "toggle_pause",
          "label": "Toggle pause"
        }
      ],
      "default": "none",
      "label": "Video click event"
    },
    {
      "type": "header",
      "content": "t:sections.video.settings.header__3.content"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.video.settings.full_width.label",
      "default": false
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Section spacing mobile (<750px)"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 36
    },
    {
      "type": "header",
      "content": "Section spacing desktop (>750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom overrides"
    },
    {
      "type": "paragraph",
      "content": "Use this for section id: #shopify-section-{{ section.id }}"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "page",
      "id": "page",
      "label": "Linked Page",
      "info": "Allows page linking to access metafields in unique cases (e.g. home page)."
    }
  ],
  "presets": [
    {
      "name": "t:sections.video.presets.name"
    }
  ]
}
{% endschema %}