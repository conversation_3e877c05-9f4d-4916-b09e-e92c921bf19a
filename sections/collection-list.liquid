{% comment %}
  bs-add
  - ability to hide/show section
  - ability to have separate padding for desktop/mobile
  - support for additional color scheme and gradient
  - liquid override for title
{% endcomment %}

{{ 'section-collection-list.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

{%- liquid
  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and section.blocks.size > columns_mobile_int
    assign show_mobile_slider = true
  endif
-%}

<div class="{{ section.settings.custom_css_class }}">
  <div class="color-{{ section.settings.color_scheme }} gradient">
    {%- assign title_content = section.settings.title_liquid | default: section.settings.title -%}
    <div class="collection-list-wrapper page-width isolate{% if show_mobile_slider %} page-width-desktop{% endif %}{% if title_content == blank %} no-heading{% endif %}{% if section.settings.show_view_all == false or section.blocks.size > collections.size %} no-mobile-link{% endif %} section-{{ section.id }}-padding">
      {%- unless title_content == blank -%}
        <div class="title-wrapper-with-link{% if show_mobile_slider %} title-wrapper--self-padded-tablet-down{% else %} title-wrapper--self-padded-mobile{% endif %} title-wrapper--no-top-margin">
          <h2
            id="SectionHeading-{{ section.id }}"
            class="collection-list-title inline-richtext {{ section.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
          >
            {{ title_content }}
          </h2>

          {%- if section.settings.show_view_all and show_mobile_slider -%}
            <a
              href="{{ routes.collections_url }}"
              id="ViewAll-{{ section.id }}"
              class="link underlined-link large-up-hide{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
              aria-labelledby="ViewAll-{{ section.id }} SectionHeading-{{ section.id }}"
            >
              {{- 'sections.collection_list.view_all' | t -}}
            </a>
          {%- endif -%}
        </div>
      {%- endunless -%}

      <slider-component class="slider-mobile-gutter{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
        <ul
          class="collection-list contains-card contains-card--collection{% if settings.card_style == 'standard' %} contains-card--standard{% endif %} grid grid--{{ section.settings.columns_desktop }}-col-desktop grid--{{ section.settings.columns_mobile }}-col-tablet-down{% if section.settings.swipe_on_mobile %} slider slider--tablet grid--peek{% endif %} collection-list--{{ section.blocks.size }}-items"
          id="Slider-{{ section.id }}"
          role="list"
        >
          {%- liquid
            assign columns = section.blocks.size
            if columns > 3
              assign columns = 3
            endif
          -%}
          {%- for block in section.blocks -%}
            <li
              id="Slide-{{ section.id }}-{{ forloop.index }}"
              class="collection-list__item grid__item{% if show_mobile_slider %} slider__slide{% endif %}{% if block.settings.collection.featured_image == nil %} collection-list__item--no-media{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
              {{ block.shopify_attributes }}
              {% if settings.animations_reveal_on_scroll %}
                data-cascade
                style="--animation-order: {{ forloop.index }};"
              {% endif %}
            >
              {%- assign placeholder_image_index = forloop.index0 | modulo: 4 | plus: 1 -%}
              {%- assign placeholder_image = 'collection-apparel-' | append: placeholder_image_index -%}
              {% render 'card-collection',
                card_collection: block.settings.collection,
                media_aspect_ratio: section.settings.image_ratio,
                columns: columns,
                placeholder_image: placeholder_image
              %}
            </li>
          {%- endfor -%}
        </ul>
        {%- if show_mobile_slider -%}
          <div class="slider-buttons">
            <button
              type="button"
              class="slider-button slider-button--prev"
              name="previous"
              aria-label="{{ 'general.slider.previous_slide' | t }}"
            >
              <span class="svg-wrapper">
                {{- 'icon-caret.svg' | inline_asset_content -}}
              </span>
            </button>
            <div class="slider-counter caption">
              <span class="slider-counter--current">1</span>
                <span aria-hidden="true"> / </span>
                <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
              <span class="slider-counter--total">{{ section.blocks.size }}</span>
            </div>
            <button
              type="button"
              class="slider-button slider-button--next"
              name="next"
              aria-label="{{ 'general.slider.next_slide' | t }}"
            >
              <span class="svg-wrapper">
                {{- 'icon-caret.svg' | inline_asset_content -}}
              </span>
            </button>
          </div>
        {%- endif -%}
      </slider-component>

      {%- if section.settings.show_view_all and section.blocks.size < collections.size -%}
        <div
          class="center collection-list-view-all{% if show_mobile_slider %} small-hide medium-hide{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
          {% if settings.animations_reveal_on_scroll %}
            data-cascade
          {% endif %}
        >
          <a
            href="{{ routes.collections_url }}"
            class="button"
            id="ViewAllButton-{{ section.id }}"
            aria-labelledby="ViewAllButton-{{ section.id }} SectionHeading-{{ section.id }}"
          >
            {{- 'sections.collection_list.view_all' | t -}}
          </a>
        </div>
      {%- endif -%}
    </div>
  </div>
</div>

{{ section.settings.custom_liquid }}

{% schema %}
{
  "name": "t:sections.collection-list.name",
  "tag": "section",
  "class": "section section-collection-list",
  "max_blocks": 15,
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "t:sections.collection-list.settings.title.default",
      "label": "Heading"
    },
    {
      "type": "liquid",
      "id": "title_liquid",
      "label": "Heading liquid",
      "info": "Overrides Heading above"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.collection-list.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.collection-list.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.collection-list.settings.image_ratio.options__3.label"
        }
      ],
      "default": "square",
      "label": "t:sections.collection-list.settings.image_ratio.label",
      "info": "t:sections.collection-list.settings.image_ratio.info"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 3,
      "label": "t:sections.collection-list.settings.columns_desktop.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": false,
      "label": "t:sections.collection-list.settings.show_view_all.label"
    },
    {
      "type": "header",
      "content": "t:sections.collection-list.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.collection-list.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.collection-list.settings.columns_mobile.options__2.label"
        }
      ],
      "default": "1",
      "label": "t:sections.collection-list.settings.columns_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.collection-list.settings.swipe_on_mobile.label"
    },
    {
      "type": "header",
      "content": "Section spacing mobile (<750px)"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 36
    },
    {
      "type": "header",
      "content": "Section spacing desktop (>750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 36
    },
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Custom overrides"
    },
    {
      "type": "paragraph",
      "content": "Use this for section id: #shopify-section-{{ section.id }}"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    }
  ],
  "blocks": [
    {
      "type": "featured_collection",
      "name": "t:sections.collection-list.blocks.featured_collection.name",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.collection-list.blocks.featured_collection.settings.collection.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.collection-list.presets.name",
      "blocks": [
        {
          "type": "featured_collection"
        },
        {
          "type": "featured_collection"
        },
        {
          "type": "featured_collection"
        }
      ]
    }
  ]
}
{% endschema %}
