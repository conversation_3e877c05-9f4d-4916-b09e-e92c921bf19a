{%- liquid
  assign product = section.settings.product
  if product == null
    assign placeholder = true
  endif
-%}

<product-info
  data-section="{{ section.id }}"
  data-product-id="{{ product.id }}"
  data-update-url="false"
  data-url="{{ product.url }}"
  {% if section.settings.image_zoom == 'hover' %}
    data-zoom-on-hover
  {% endif %}
>
  {{ 'section-main-product.css' | asset_url | stylesheet_tag }}
  {{ 'section-featured-product.css' | asset_url | stylesheet_tag }}
  {{ 'component-accordion.css' | asset_url | stylesheet_tag }}
  {{ 'component-price.css' | asset_url | stylesheet_tag }}
  {{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}
  {{ 'component-rating.css' | asset_url | stylesheet_tag }}
  {{ 'component-volume-pricing.css' | asset_url | stylesheet_tag }}
  {% unless section.settings.product.has_only_default_variant %}
    {{ 'component-product-variant-picker.css' | asset_url | stylesheet_tag }}
    {{ 'component-swatch.css' | asset_url | stylesheet_tag }}
    {{ 'component-swatch-input.css' | asset_url | stylesheet_tag }}
  {% endunless %}

  {%- style -%}
    {% render 'section-hide-css-logic' %}
    {% render 'section-padding-css-logic' %}

    .section-{{ section.id }}-padding .product__accordion .accordion__content {
      padding: 0 {{ section.settings.accordion_padding }}rem;
    }
  {%- endstyle -%}

  <script src="{{ 'product-info.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'show-more.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'price-per-item.js' | asset_url }}" defer="defer"></script>

  {% comment %} TODO: assign `product.selected_or_first_available_variant` to variable and replace usage to reduce verbosity {% endcomment %}

  {%- assign first_3d_model = product.media | where: 'media_type', 'model' | first -%}
  {%- if first_3d_model -%}
    {{ 'component-product-model.css' | asset_url | stylesheet_tag }}
    <link
      id="ModelViewerStyle"
      rel="stylesheet"
      href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css"
      media="print"
      onload="this.media='all'"
    >
    <link
      id="ModelViewerOverride"
      rel="stylesheet"
      href="{{ 'component-model-viewer-ui.css' | asset_url }}"
      media="print"
      onload="this.media='all'"
    >
  {%- endif -%}

  {% assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src' %}

<div class="{{ section.settings.custom_css_class }}">
  <section class="color-{{ section.settings.color_scheme }} {% if section.settings.secondary_background %}background-secondary{% endif %} gradient">
    <div class="page-width section-{{ section.id }}-padding{% if section.settings.secondary_background %} isolate{% endif %}">
      <div class="featured-product {% if section.settings.show_gallery_layouts %}featured-product--gallery{% endif %} product product--{{ section.settings.media_size }} grid grid--1-col gradient color-{{ section.settings.color_scheme }} product--{{ section.settings.media_position }} {% if section.settings.show_gallery_layouts %}product--{{ section.settings.gallery_layout }} product--mobile-{{ section.settings.mobile_thumbnails }}{% endif %}{% if section.settings.secondary_background == false %} isolate{% endif %} {% if product.media.size > 0 or section.settings.product == blank %}grid--2-col-tablet{% else %}product--no-media{% endif %}">
        <div class="grid__item product__media-wrapper">
          {%- unless placeholder -%}
            {%- if section.settings.show_gallery_layouts -%}
              {% render 'product-media-gallery', product: product, variant_images: variant_images %}
            {%- else -%}
              {% render 'product-media-gallery', product: product, variant_images: variant_images, limit: 1 %}
            {%- endif -%}
          {%- else -%}
            <div class="product__media-item">
              <div
                class="product-media-container global-media-settings gradient{% if section.settings.constrain_to_viewport %} constrain-height{% endif %}"
                style="--ratio: 1.0; --preview-ratio: 1.0;"
              >
                {{ 'product-apparel-1' | placeholder_svg_tag: 'placeholder-svg' }}
              </div>
            </div>
          {%- endunless -%}
        </div>
        <div class="product__info-wrapper grid__item{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
          <section
            id="ProductInfo-{{ section.id }}"
            class="product__info-container"
            data-section="{{ section.id }}"
            data-url="{{ product.url }}"
          >
            {%- assign product_form_id = 'product-form-' | append: section.id -%}

            {%- for block in section.blocks -%}
              {%- comment -%}
                bs-add
                - support for block show/hide
              {%- endcomment -%}
              <div id="FeaturedProductBlock-{{ block.id }}" class="section-block section-block--{{ block.type | replace: '@', '' }} {{ block.settings.hide_size }}">
                {%- case block.type -%}
                  {%- when '@app' -%}
                    {% render block %}
                  {%- when 'text' -%}
                    <p
                      class="product__text inline-richtext {% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      {{- block.settings.text_liquid | default: block.settings.text -}}
                    </p>
                  {%- when 'title' -%}
                    <h2 class="product__title {{ block.settings.heading_size }}" {{ block.shopify_attributes }}>
                      {%- unless placeholder -%}
                        {{ product.title | escape }}
                      {%- else -%}
                        {{ 'onboarding.product_title' | t }}
                      {%- endunless -%}
                    </h2>
                  {%- when 'price' -%}
                    <div id="price-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
                      {%- render 'price',
                        product: product,
                        placeholder: placeholder,
                        use_variant: true,
                        show_badges: true,
                        price_class: 'price--large'
                      -%}
                    </div>
                    {%- if product.quantity_price_breaks_configured? -%}
                      <div class="volume-pricing-note" id="Volume-Note-{{ section.id }}">
                        <span>{{ 'products.product.volume_pricing.note' | t }}</span>
                      </div>
                    {%- endif -%}
                    {%- if block.settings.custom_above_taxes_liquid != blank -%}
                      <div class="product__custom">
                        {{ block.settings.custom_above_taxes_liquid }}
                      </div>
                    {%- endif -%}
                    {%- if cart.taxes_included or cart.duties_included or shop.shipping_policy.body != blank -%}
                      <div class="product__tax caption rte">
                        {%- if cart.duties_included and cart.taxes_included and block.settings.show_duties_included and block.settings.show_taxes_included -%}
                          {{ 'products.product.duties_and_taxes_included' | t }}
                        {%- elsif cart.taxes_included and block.settings.show_taxes_included -%}
                          {{ 'products.product.taxes_included' | t }}
                        {%- elsif cart.duties_included and block.settings.show_duties_included -%}
                          {{ 'products.product.duties_included' | t }}
                        {%- endif -%}
                        {%- if shop.shipping_policy.body != blank and block.settings.show_shipping_policy -%}
                          {{ 'products.product.shipping_policy_html' | t: link: shop.shipping_policy.url }}
                        {%- endif -%}
                      </div>
                    {%- endif -%}
                    {%- unless placeholder -%}
                      <div {{ block.shopify_attributes }}>
                        {%- form 'product', product -%}
                          <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                          {%- if block.settings.show_payment_terms -%}
                            {{ form | payment_terms }}
                          {%- endif -%}
                        {%- endform -%}
                      </div>
                    {%- endunless -%}
                  {%- when 'sku' -%}
                    <p
                      class="product__sku{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}{% if product.selected_or_first_available_variant.sku.size == 0 %} visibility-hidden{% endif %}"
                      id="Sku-{{ section.id }}"
                      role="status"
                      {{ block.shopify_attributes }}
                    >
                      <span class="visually-hidden">{{ 'products.product.sku' | t }}:</span>
                      {{- product.selected_or_first_available_variant.sku -}}
                    </p>
                  {%- when 'quantity_selector' -%}
                    <div
                      id="Quantity-Form-{{ section.id }}"
                      class="product-form__input product-form__quantity{% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_vertical_offset < 0 %} product-form__quantity-top{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      {% comment %} TODO: enable theme-check once `item_count_for_variant` is accepted as valid filter {% endcomment %}
                      {% # theme-check-disable %}
                      {%- assign cart_qty = cart
                        | item_count_for_variant: product.selected_or_first_available_variant.id
                      -%}
                      {% # theme-check-enable %}
                      <label class="quantity__label form__label" for="Quantity-{{ section.id }}">
                        {{ 'products.product.quantity.label' | t }}
                        <span class="quantity__rules-cart{% if cart_qty == 0 %} hidden{% endif %}">
                          {%- render 'loading-spinner' -%}
                          <span
                            >(
                            {{- 'products.product.quantity.in_cart_html' | t: quantity: cart_qty -}}
                            )</span
                          >
                        </span>
                      </label>
                      <div class="price-per-item__container">
                        <quantity-input class="quantity">
                          <button class="quantity__button" name="minus" type="button">
                            <span class="visually-hidden">
                              {{- 'products.product.quantity.decrease' | t: product: product.title | escape -}}
                            </span>
                            <span class="svg-wrapper">
                              {{- 'icon-minus.svg' | inline_asset_content -}}
                            </span>
                          </button>
                          <input
                            class="quantity__input"
                            type="number"
                            name="quantity"
                            id="Quantity-{{ section.id }}"
                            data-cart-quantity="{{ cart_qty }}"
                            data-min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                            min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                            {% if product.selected_or_first_available_variant.quantity_rule.max != null %}
                              data-max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                              max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                            {% endif %}
                            step="{{ product.selected_or_first_available_variant.quantity_rule.increment }}"
                            value="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                            form="{{ product_form_id }}"
                          >
                          <button class="quantity__button" name="plus" type="button">
                            <span class="visually-hidden">
                              {{- 'products.product.quantity.increase' | t: product: product.title | escape -}}
                            </span>
                            <span class="svg-wrapper">
                              {{- 'icon-plus.svg' | inline_asset_content -}}
                            </span>
                          </button>
                        </quantity-input>
                        {%- liquid
                          assign volume_pricing_array = product.selected_or_first_available_variant.quantity_price_breaks | sort: 'quantity' | reverse
                          assign current_qty_for_volume_pricing = cart_qty | plus: product.selected_or_first_available_variant.quantity_rule.min
                          if cart_qty > 0
                            assign current_qty_for_volume_pricing = cart_qty | plus: product.selected_or_first_available_variant.quantity_rule.increment
                          endif
                        -%}
                        {%- if product.quantity_price_breaks_configured? -%}
                          <price-per-item
                            id="Price-Per-Item-{{ section.id }}"
                            data-section-id="{{ section.id }}"
                            data-variant-id="{{ product.selected_or_first_available_variant.id }}"
                          >
                            {%- if product.selected_or_first_available_variant.quantity_price_breaks.size > 0 -%}
                              {%- assign variant_price_compare = product.selected_or_first_available_variant.compare_at_price -%}
                              <div class="price-per-item">
                                {%- if variant_price_compare -%}
                                  <dl class="price-per-item--current">
                                    <dt class="visually-hidden">
                                      {{ 'products.product.price.regular_price' | t }}
                                    </dt>
                                    <dd>
                                      <s class="variant-item__old-price">
                                        {{ variant_price_compare | money_with_currency }}
                                      </s>
                                    </dd>
                                  </dl>
                                {%- endif -%}
                                {%- if current_qty_for_volume_pricing < volume_pricing_array.last.minimum_quantity -%}
                                  {%- assign variant_price = product.selected_or_first_available_variant.price
                                    | money_with_currency
                                  -%}
                                  <span class="price-per-item--current">
                                    {{- 'products.product.volume_pricing.price_at_each' | t: price: variant_price -}}
                                  </span>
                                {%- else -%}
                                  {%- for price_break in volume_pricing_array -%}
                                    {%- if current_qty_for_volume_pricing >= price_break.minimum_quantity -%}
                                      {%- assign price_break_price = price_break.price | money_with_currency -%}
                                      <span class="price-per-item--current">
                                        {{-
                                          'products.product.volume_pricing.price_at_each'
                                          | t: price: price_break_price
                                        -}}
                                      </span>
                                      {%- break -%}
                                    {%- endif -%}
                                  {%- endfor -%}
                                {%- endif -%}
                              </div>
                            {%- else -%}
                              {%- assign variant_price = product.selected_or_first_available_variant.price
                                | money_with_currency
                              -%}
                              {%- assign variant_price_compare = product.selected_or_first_available_variant.compare_at_price -%}
                              <div class="price-per-item">
                                {%- if variant_price_compare -%}
                                  <dl class="price-per-item--current">
                                    <dt class="visually-hidden">
                                      {{ 'products.product.price.regular_price' | t }}
                                    </dt>
                                    <dd>
                                      <s class="variant-item__old-price">
                                        {{ variant_price_compare | money_with_currency }}
                                      </s>
                                    </dd>
                                    <dt class="visually-hidden">
                                      {{ 'products.product.price.sale_price' | t }}
                                    </dt>
                                    <dd>
                                      <span class="price-per-item--current">
                                        {{- 'products.product.volume_pricing.price_at_each' | t: price: variant_price -}}
                                      </span>
                                    </dd>
                                  </dl>
                                {%- else -%}
                                  <span class="price-per-item--current">
                                    {{- 'products.product.volume_pricing.price_at_each' | t: price: variant_price -}}
                                  </span>
                                {%- endif -%}
                              </div>
                            {%- endif -%}
                          </price-per-item>
                        {%- endif -%}
                      </div>
                      <div class="quantity__rules caption" id="Quantity-Rules-{{ section.id }}">
                        {%- if product.selected_or_first_available_variant.quantity_rule.increment > 1 -%}
                          <span class="divider">
                            {{-
                              'products.product.quantity.multiples_of'
                              | t: quantity: product.selected_or_first_available_variant.quantity_rule.increment
                            -}}
                          </span>
                        {%- endif -%}
                        {%- if product.selected_or_first_available_variant.quantity_rule.min > 1 -%}
                          <span class="divider">
                            {{-
                              'products.product.quantity.minimum_of'
                              | t: quantity: product.selected_or_first_available_variant.quantity_rule.min
                            -}}
                          </span>
                        {%- endif -%}
                        {%- if product.selected_or_first_available_variant.quantity_rule.max != null -%}
                          <span class="divider">
                            {{-
                              'products.product.quantity.maximum_of'
                              | t: quantity: product.selected_or_first_available_variant.quantity_rule.max
                            -}}
                          </span>
                        {%- endif -%}
                      </div>
                      {%- if product.quantity_price_breaks_configured? -%}
                        <volume-pricing class="parent-display" id="Volume-{{ section.id }}">
                          {%- if product.selected_or_first_available_variant.quantity_price_breaks.size > 0 -%}
                            <span class="caption-large">{{ 'products.product.volume_pricing.title' | t }}</span>
                            <ul class="list-unstyled">
                              <li>
                                <span>{{ product.selected_or_first_available_variant.quantity_rule.min }}+</span>
                                {%- assign price = product.selected_or_first_available_variant.price
                                  | money_with_currency
                                -%}
                                <span data-text="{{ 'products.product.volume_pricing.price_at_each' | t: price: variant_price }}">
                                  {{ 'sections.quick_order_list.each' | t: money: price -}}
                                </span>
                              </li>
                              {%- for price_break in product.selected_or_first_available_variant.quantity_price_breaks -%}
                                {%- assign price_break_price = price_break.price | money_with_currency -%}
                                <li class="{%- if forloop.index >= 3 -%}show-more-item hidden{%- endif -%}">
                                  <span>
                                    {{- price_break.minimum_quantity -}}
                                    <span aria-hidden="true">+</span></span
                                  >
                                  {%- assign price = price_break.price | money_with_currency -%}
                                  <span data-text="{{ 'products.product.volume_pricing.price_at_each' | t: price: price_break_price }}">
                                    {{ 'sections.quick_order_list.each' | t: money: price -}}
                                  </span>
                                </li>
                              {%- endfor -%}
                            </ul>
                            {%- if product.selected_or_first_available_variant.quantity_price_breaks.size >= 3 -%}
                              <show-more-button>
                                <button
                                  class="button-show-more link underlined-link"
                                  id="Show-More-{{ section.id }}"
                                  type="button"
                                >
                                  <span class="label-show-more label-text"
                                    ><span aria-hidden="true">+ </span>{{ 'products.facets.show_more' | t }}
                                  </span>
                                </button>
                              </show-more-button>
                            {%- endif -%}
                          {%- endif -%}
                        </volume-pricing>
                      {%- endif -%}
                    </div>
                  {%- when 'share' -%}
                    {% liquid
                      assign share_url = product.selected_variant.url | default: product.url | prepend: request.origin 
                      render 'share-button', block: block, share_link: share_url
                    %}
                  {%- when 'variant_picker' -%}
                    {%- assign product_variants_size = product.variants | size -%}
                    {%- unless block.settings.hide_single_product_variant_swatches and product_variants_size < 2 -%}
                      {% render 'product-variant-picker',
                        product: product,
                        block: block,
                        product_form_id: product_form_id
                      %}
                    {%- endunless -%}
                  {%- when 'buy_buttons' -%}
                    {%- render 'buy-buttons',
                      block: block,
                      product: product,
                      product_form_id: product_form_id,
                      section_id: section.id
                    -%}
                  {%- when 'custom_liquid' -%}
                    {{ block.settings.custom_liquid }}
                  {%- when 'rating' -%}
                    {%- if product.metafields.reviews.rating.value != blank -%}
                      <div class="rating-wrapper">
                        {% liquid
                          assign rating_decimal = 0
                          assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1
                          if decimal >= 0.3 and decimal <= 0.7
                            assign rating_decimal = 0.5
                          elsif decimal > 0.7
                            assign rating_decimal = 1
                          endif
                        %}
                        <div
                          class="rating"
                          role="img"
                          aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}"
                        >
                          <span
                            aria-hidden="true"
                            class="rating-star"
                            style="--rating: {{ product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
                          ></span>
                        </div>
                        <p class="rating-text caption">
                          <span aria-hidden="true">
                            {{- product.metafields.reviews.rating.value }} /
                            {{ product.metafields.reviews.rating.value.scale_max -}}
                          </span>
                        </p>
                        <p class="rating-count caption">
                          <span aria-hidden="true">({{ product.metafields.reviews.rating_count }})</span>
                          <span class="visually-hidden">
                            {{- product.metafields.reviews.rating_count }}
                            {{ 'accessibility.total_reviews' | t -}}
                          </span>
                        </p>
                      </div>
                    {%- endif -%}
                  {%- when 'icon-with-text' -%}
                    {% render 'icon-with-text', block: block %}
                  {%- when 'collapsible_tab' -%}
                    {%- if block.settings.content_liquid != blank
                      or block.settings.content != blank
                      or block.settings.page.content != blank
                    %}
                      <div class="product__accordion accordion quick-add-hidden" {{ block.shopify_attributes }}>
                        <details id="Details-{{ block.id }}-{{ section.id }}">
                          <summary>
                            <div class="summary__title">
                              {% render 'icon-accordion', icon: block.settings.icon, image_icon: block.settings.image_icon %}
                              <h2 class="{{ section.settings.accordion_title_size }} accordion__title inline-richtext">
                                {{
                                  block.settings.heading_liquid
                                  | default: block.settings.heading
                                  | default: block.settings.page.title
                                }}
                              </h2>
                            </div>
                            {{- 'icon-caret.svg' | inline_asset_content -}}
                          </summary>
                          <div class="accordion__content rte" id="ProductAccordion-{{ block.id }}-{{ section.id }}">
                            {{ block.settings.content_liquid | default: block.settings.content }}
                            {{ block.settings.page.content }}
                          </div>
                        </details>
                      </div>
                    {%- endif -%}
                {%- endcase -%}
              </div>
            {%- endfor -%}
            <a
              {% if product == blank %}
                role="link" aria-disabled="true"
              {% else %}
                href="{{ product.url }}"
              {% endif %}
              class="link product__view-details animate-arrow"
            >
              {{ 'products.product.view_full_details' | t }}
              {{- 'icon-arrow.svg' | inline_asset_content -}}
            </a>
          </section>
        </div>
      </div>
      {% render 'product-media-modal', product: product, variant_images: variant_images %}    
    </div>
  </section>
</div>

  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
  {%- if section.settings.image_zoom == 'hover' -%}
    <script id="EnableZoomOnHover-featured" src="{{ 'magnify.js' | asset_url }}" defer="defer"></script>
  {%- endif %}
  {%- if request.design_mode -%}
    <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}

  {%- if first_3d_model -%}
    <script type="application/json" id="ProductJSON-{{ product.id }}">
      {{ product.media | where: 'media_type', 'model' | json }}
    </script>
    <script src="{{ 'product-model.js' | asset_url }}" defer></script>
  {%- endif -%}

  {%- liquid
    if product.selected_or_first_available_variant.featured_media
      assign seo_media = product.selected_or_first_available_variant.featured_media
    else
      assign seo_media = product.featured_media
    endif
  -%}

  <script type="application/ld+json">
    {{ product | structured_data }}
  </script>

  {% if product.media.size > 0 %}
    <script src="{{ 'product-modal.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'media-gallery.js' | asset_url }}" defer="defer"></script>
  {% endif %}
</product-info>

{{ section.settings.custom_liquid }}

{% schema %}
{
  "name": "t:sections.featured-product.name",
  "tag": "section",
  "class": "section section-featured-product",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "text",
      "name": "t:sections.featured-product.blocks.text.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "t:sections.featured-product.blocks.text.settings.text.default",
          "label": "t:sections.featured-product.blocks.text.settings.text.label"
        },
        {
          "type": "liquid",
          "id": "text_liquid",
          "label": "Text liquid",
          "info": "Overrides Text above"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.featured-product.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.featured-product.blocks.text.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.featured-product.blocks.text.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.featured-product.blocks.text.settings.text_style.label"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.featured-product.blocks.title.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "hxl",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "hxxl",
              "label": "t:sections.all.heading_size.options__5.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "price",
      "name": "t:sections.featured-product.blocks.price.name",
      "limit": 1,
      "settings": [
        {
          "type": "liquid",
          "id": "custom_above_taxes_liquid",
          "label": "Custom liquid",
          "info": "Placed above taxes"
        },
        {
          "type": "checkbox",
          "id": "show_taxes_included",
          "label": "Show included taxes",
          "info": "Copy is located under 'Include taxes' in Theme content",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_duties_included",
          "label": "Show duties taxes",
          "info": "Copy is located under 'Include duties' in Theme content",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_shipping_policy",
          "label": "Show shipping policy",
          "info": "Copy is located under 'Shipping policy html' in Theme content",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_payment_terms",
          "label": "Show payment terms",
          "default": true
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "sku",
      "name": "t:sections.featured-product.blocks.sku.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.featured-product.blocks.sku.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.featured-product.blocks.sku.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.featured-product.blocks.sku.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.featured-product.blocks.sku.settings.text_style.label"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "t:sections.featured-product.blocks.quantity_selector.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "variant_picker",
      "name": "t:sections.featured-product.blocks.variant_picker.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "picker_type",
          "options": [
            {
              "value": "dropdown",
              "label": "t:sections.featured-product.blocks.variant_picker.settings.picker_type.options__1.label"
            },
            {
              "value": "button",
              "label": "t:sections.featured-product.blocks.variant_picker.settings.picker_type.options__2.label"
            }
          ],
          "default": "button",
          "label": "t:sections.featured-product.blocks.variant_picker.settings.picker_type.label"
        },
        {
          "id": "swatch_shape",
          "label": "t:sections.featured-product.blocks.variant_picker.settings.swatch_shape.label",
          "type": "select",
          "info": "t:sections.featured-product.blocks.variant_picker.settings.swatch_shape.info",
          "options": [
            {
              "value": "circle",
              "label": "t:sections.featured-product.blocks.variant_picker.settings.swatch_shape.options__1.label"
            },
            {
              "value": "square",
              "label": "t:sections.featured-product.blocks.variant_picker.settings.swatch_shape.options__2.label"
            },
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__3.label"
            }
          ],
          "default": "circle"
        },
        {
          "type": "checkbox",
          "id": "hide_single_product_variant_swatches",
          "default": false,
          "label": "Hide product swatches with single variant"
        },
        {
          "type": "header",
          "content": "Custom field"
        },
        {
          "type": "paragraph",
          "content": "Requires a variant metafield with namespace and key set to: bonshore.color_swatch"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "t:sections.featured-product.blocks.buy_buttons.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_dynamic_checkout",
          "default": false,
          "label": "t:sections.featured-product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
          "info": "t:sections.featured-product.blocks.buy_buttons.settings.show_dynamic_checkout.info"
        },
        {
          "type": "liquid",
          "id": "custom_liquid_buttons",
          "label": "Buttons liquid",
          "info": "Shows after dynamic checkout buttons"
        },
        {
          "type": "checkbox",
          "id": "show_gift_card_recipient",
          "default": false,
          "label": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.label",
          "info": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.info"
        },
        {
          "type": "select",
          "id": "button_style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "button button--primary",
          "label": "Button style"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "share",
      "name": "t:sections.featured-product.blocks.share.name",
      "limit": 1,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "share_label",
          "label": "t:sections.featured-product.blocks.share.settings.text.label",
          "default": "t:sections.featured-product.blocks.share.settings.text.default"
        },
        {
          "type": "liquid",
          "id": "share_label_liquid",
          "label": "Text liquid",
          "info": "Overrides Text above"
        },
        {
          "type": "paragraph",
          "content": "t:sections.featured-product.blocks.share.settings.featured_image_info.content"
        },
        {
          "type": "paragraph",
          "content": "t:sections.featured-product.blocks.share.settings.title_info.content"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.custom-liquid.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "default": "My custom liquid",
          "label": "Title",
          "info": "Only used to organize custom liquid in side bar."
        },
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "rating",
      "name": "t:sections.featured-product.blocks.rating.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.featured-product.blocks.rating.settings.paragraph.content"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "icon-with-text",
      "name": "t:sections.main-product.blocks.icon_with_text.name",
      "settings": [
        {
          "type": "select",
          "id": "layout",
          "options": [
            {
              "value": "horizontal",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__1.label"
            },
            {
              "value": "vertical",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__2.label"
            }
          ],
          "default": "horizontal",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.label"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.icon_with_text.settings.content.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.content.info"
        },
        {
          "type": "select",
          "id": "icon_1",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "not_allowed",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__23_1.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "heart",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_1.label"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_1.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_1",
          "default": "t:sections.main-product.blocks.icon_with_text.settings.heading_1.default",
          "label": "First heading",
          "info": "Leave the heading label blank to hide the icon column."
        },
        {
          "type": "liquid",
          "id": "heading_1_liquid",
          "label": "First heading liquid",
          "info": "Overrides First heading above"
        },
        {
          "type": "select",
          "id": "icon_2",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "not_allowed",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__23_1.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "return",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_2.label"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_2.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_2",
          "default": "t:sections.main-product.blocks.icon_with_text.settings.heading_2.default",
          "label": "Second heading",
          "info": "Leave the heading label blank to hide the icon column."
        },
        {
          "type": "liquid",
          "id": "heading_2_liquid",
          "label": "Second heading liquid",
          "info": "Overrides Second heading above"
        },
        {
          "type": "select",
          "id": "icon_3",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "not_allowed",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__23_1.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "truck",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_3.label"
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_3.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_3",
          "default": "t:sections.main-product.blocks.icon_with_text.settings.heading_3.default",
          "label": "Third heading",
          "info": "Leave the heading label blank to hide the icon column."
        },
        {
          "type": "liquid",
          "id": "heading_3_liquid",
          "label": "Third heading liquid",
          "info": "Overrides Third heading above"
        },
        {
          "type": "select",
          "id": "heading_title_size",
          "options": [
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__h6.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__h5.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__h4.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__h3.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            }
          ],
          "default": "h4",
          "label": "Heading title size"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "collapsible_tab",
      "name": "t:sections.main-product.blocks.collapsible_tab.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "Collapsible row",
          "label": "Heading"
        },
        {
          "type": "liquid",
          "id": "heading_liquid",
          "label": "Heading liquid",
          "info": "Overrides Heading above"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "not_allowed",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__23_1.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "none",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
        },
        {
          "type": "image_picker",
          "id": "image_icon",
          "label": "Image icon",
          "info": "Overrides icon above"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.content.label"
        },
        {
          "type": "liquid",
          "id": "content_liquid",
          "label": "Row content liquid",
          "info": "Overrides content above"
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.page.label"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "product",
      "id": "product",
      "label": "t:sections.featured-product.settings.product.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "secondary_background",
      "default": false,
      "label": "t:sections.featured-product.settings.secondary_background.label"
    },
    {
      "type": "header",
      "content": "t:sections.featured-product.settings.header.content",
      "info": "t:sections.featured-product.settings.header.info"
    },
    {
      "type": "select",
      "id": "media_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.main-product.settings.media_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.main-product.settings.media_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.main-product.settings.media_size.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.main-product.settings.media_size.label",
      "info": "t:sections.main-product.settings.media_size.info"
    },
    {
      "type": "checkbox",
      "id": "constrain_to_viewport",
      "default": true,
      "label": "t:sections.main-product.settings.constrain_to_viewport.label"
    },
    {
      "type": "select",
      "id": "media_fit",
      "options": [
        {
          "value": "contain",
          "label": "t:sections.main-product.settings.media_fit.options__1.label"
        },
        {
          "value": "cover",
          "label": "t:sections.main-product.settings.media_fit.options__2.label"
        }
      ],
      "default": "contain",
      "label": "t:sections.main-product.settings.media_fit.label"
    },
    {
      "type": "checkbox",
      "id": "show_gallery_layouts",
      "default": false,
      "label": "Show gallery layouts"
    },
    {
      "type": "select",
      "id": "gallery_layout",
      "options": [
        {
          "value": "stacked",
          "label": "t:sections.main-product.settings.gallery_layout.options__1.label"
        },
        {
          "value": "columns",
          "label": "t:sections.main-product.settings.gallery_layout.options__2.label"
        },
        {
          "value": "thumbnail",
          "label": "t:sections.main-product.settings.gallery_layout.options__3.label"
        },
        {
          "value": "thumbnail_slider",
          "label": "t:sections.main-product.settings.gallery_layout.options__4.label"
        }
      ],
      "default": "thumbnail_slider",
      "label": "t:sections.main-product.settings.gallery_layout.label"
    },
    {
      "type": "select",
      "id": "media_position",
      "options": [
        {
          "value": "left",
          "label": "t:sections.featured-product.settings.media_position.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.featured-product.settings.media_position.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.featured-product.settings.media_position.label",
      "info": "t:sections.featured-product.settings.media_position.info"
    },
    {
      "type": "select",
      "id": "image_zoom",
      "options": [
        {
          "value": "lightbox",
          "label": "t:sections.main-product.settings.image_zoom.options__1.label"
        },
        {
          "value": "hover",
          "label": "t:sections.main-product.settings.image_zoom.options__2.label"
        },
        {
          "value": "none",
          "label": "t:sections.main-product.settings.image_zoom.options__3.label"
        }
      ],
      "default": "lightbox",
      "label": "t:sections.main-product.settings.image_zoom.label",
      "info": "t:sections.main-product.settings.image_zoom.info"
    },
    {
      "type": "select",
      "id": "mobile_thumbnails",
      "options": [
        {
          "value": "columns",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__1.label"
        },
        {
          "value": "show",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__2.label"
        },
        {
          "value": "hide",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__3.label"
        }
      ],
      "default": "hide",
      "label": "t:sections.main-product.settings.mobile_thumbnails.label"
    },
    {
      "type": "checkbox",
      "id": "hide_variants",
      "default": false,
      "label": "t:sections.main-product.settings.hide_variants.label"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "default": false,
      "label": "t:sections.featured-product.settings.enable_video_looping.label"
    },
    {
      "type": "header",
      "content": "Accordion settings"
    },
    {
      "type": "range",
      "id": "accordion_padding",
      "min": 0,
      "max": 10,
      "step": 0.5,
      "unit": "rem",
      "label": "Accordion padding",
      "info": "Sets left and right padding for collapsible rows",
      "default": 1
    },
    {
      "type": "select",
      "id": "accordion_title_size",
      "options": [
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__h6.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__h5.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__h4.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__h3.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        }
      ],
      "default": "h4",
      "label": "Accordion title size"
    },
    {
      "type": "header",
      "content": "Section spacing mobile (<750px)"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 36
    },
    {
      "type": "header",
      "content": "Section spacing desktop (>750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 36
    },
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Custom overrides"
    },
    {
      "type": "paragraph",
      "content": "Use this for section id: #shopify-section-{{ section.id }}"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    }
  ],
  "presets": [
    {
      "name": "t:sections.featured-product.presets.name",
      "blocks": [
        {
          "type": "text",
          "settings": {
            "text": "{{ section.settings.product.vendor }}",
            "text_style": "uppercase"
          }
        },
        {
          "type": "title"
        },
        {
          "type": "price"
        },
        {
          "type": "variant_picker"
        },
        {
          "type": "quantity_selector"
        },
        {
          "type": "buy_buttons"
        },
        {
          "type": "share"
        }
      ]
    }
  ]
}
{% endschema %}