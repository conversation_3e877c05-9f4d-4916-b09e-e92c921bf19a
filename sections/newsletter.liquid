{% comment %}
  bs-add
  - show/hide option added
  - added custom liquid block
  - gradient option added
  - heading inline rich text option with liquid override added
  - 
{% endcomment %}

{{ 'component-newsletter.css' | asset_url | stylesheet_tag }}
{{ 'newsletter-section.css' | asset_url | stylesheet_tag }}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

<div class="newsletter center {% if section.settings.full_width == false %}newsletter--narrow page-width{% endif %}">
  <div class="newsletter__wrapper color-{{ section.settings.color_scheme }} gradient content-container isolate{% if section.settings.full_width %} content-container--full-width{% endif %} section-{{ section.id }}-padding">
    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when 'custom_liquid' -%}
          {{ block.settings.custom_liquid }}
        {%- when '@app' -%}
          {% render block %}
        {%- when 'heading' -%}
          <h2
            class="inline-richtext {{ block.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
            {{ block.shopify_attributes }}
            {% if settings.animations_reveal_on_scroll %}
              data-cascade
              style="--animation-order: {{ forloop.index }};"
            {% endif %}
          >
            {{ block.settings.heading_liquid | default: block.settings.heading }}
          </h2>
        {%- when 'paragraph' -%}
          <div
            class="newsletter__subheading rte{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
            {{ block.shopify_attributes }}
            {% if settings.animations_reveal_on_scroll %}
              data-cascade
              style="--animation-order: {{ forloop.index }};"
            {% endif %}
          >
            {{ block.settings.text }}
          </div>
        {%- when 'email_form' -%}
          <div {{ block.shopify_attributes }}>
            {% form 'customer', class: 'newsletter-form' %}
              <input type="hidden" name="contact[tags]" value="newsletter">
              <div
                class="newsletter-form__field-wrapper{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                {% if settings.animations_reveal_on_scroll %}
                  data-cascade
                  style="--animation-order: {{ forloop.index }};"
                {% endif %}
              >
                <div class="field">
                  <input
                    id="NewsletterForm--{{ section.id }}"
                    type="email"
                    name="contact[email]"
                    class="field__input"
                    value="{{ form.email }}"
                    aria-required="true"
                    autocorrect="off"
                    autocapitalize="off"
                    autocomplete="email"
                    {% if form.errors %}
                      autofocus
                      aria-invalid="true"
                      aria-describedby="Newsletter-error--{{ section.id }}"
                    {% elsif form.posted_successfully? %}
                      aria-describedby="Newsletter-success--{{ section.id }}"
                    {% endif %}
                    placeholder="{{ 'newsletter.label' | t }}"
                    required
                  >
                  <label class="field__label" for="NewsletterForm--{{ section.id }}">
                    {{ 'newsletter.label' | t }}
                  </label>
                  <button
                    type="submit"
                    class="newsletter-form__button field__button"
                    name="commit"
                    id="Subscribe"
                    aria-label="{{ 'newsletter.button_label' | t }}"
                  >
                    <span class="svg-wrapper">
                      {{- 'icon-arrow.svg' | inline_asset_content -}}
                    </span>
                  </button>
                </div>
                {%- if form.errors -%}
                  <small class="newsletter-form__message form__message" id="Newsletter-error--{{ section.id }}">
                    <span class="svg-wrapper">
                      {{- 'icon-error.svg' | inline_asset_content -}}
                    </span>
                    {{- form.errors.translated_fields.email | capitalize }}
                    {{ form.errors.messages.email -}}
                  </small>
                {%- endif -%}
              </div>
              {%- if form.posted_successfully? -%}
                <h3
                  class="newsletter-form__message newsletter-form__message--success form__message"
                  id="Newsletter-success--{{ section.id }}"
                  tabindex="-1"
                  autofocus
                >
                  <span class="svg-wrapper">
                    {{- 'icon-success.svg' | inline_asset_content -}}
                  </span>
                  {{- 'newsletter.success' | t }}
                </h3>
              {%- endif -%}
            {% endform %}
          </div>
      {%- endcase -%}
    {%- endfor -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.newsletter.name",
  "tag": "section",
  "class": "section section--newsletter",
  "disabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "default": true,
      "label": "t:sections.newsletter.settings.full_width.label"
    },
    {
      "type": "paragraph",
      "content": "t:sections.newsletter.settings.paragraph.content"
    },
    {
      "type": "header",
      "content": "Section spacing mobile (<750px)"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 36
    },
    {
      "type": "header",
      "content": "Section spacing desktop (>750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 36
    },
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    }
  ],
  "blocks": [
    {
      "type": "custom_liquid",
      "name": "t:sections.custom-liquid.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "default": "My custom liquid",
          "label": "Title",
          "info": "Only used to organize custom liquid in side bar."
        },
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "heading",
      "name": "t:sections.newsletter.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "t:sections.newsletter.blocks.heading.settings.heading.default",
          "label": "t:sections.newsletter.blocks.heading.settings.heading.label"
        },
        {
          "type": "liquid",
          "id": "heading_liquid",
          "label": "Heading liquid",
          "info": "Overrides Heading above"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "hxl",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "hxxl",
              "label": "t:sections.all.heading_size.options__5.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        }
      ]
    },
    {
      "type": "paragraph",
      "name": "t:sections.newsletter.blocks.paragraph.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "t:sections.newsletter.blocks.paragraph.settings.paragraph.default",
          "label": "t:sections.newsletter.blocks.paragraph.settings.paragraph.label"
        }
      ]
    },
    {
      "type": "email_form",
      "name": "t:sections.newsletter.blocks.email_form.name",
      "limit": 1
    },
    {
      "type": "@app"
    }
  ],
  "presets": [
    {
      "name": "t:sections.newsletter.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "paragraph"
        },
        {
          "type": "email_form"
        }
      ]
    }
  ]
}
{% endschema %}
