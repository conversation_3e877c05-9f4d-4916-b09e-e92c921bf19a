{% comment %}
  bs-add
  - ability to hide/show icons (account, search, cart)
  - gradient color support 
  - blocks added for mega menu along with grid layout mimicking footer setup
  - option to show swatches on product card
  - option to show alternative title on product card
{% endcomment %}

<link rel="stylesheet" href="{{ 'component-list-menu.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-search.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-menu-drawer.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-cart-notification.css' | asset_url }}" media="print" onload="this.media='all'">

{%- if settings.predictive_search_enabled -%}
  <link rel="stylesheet" href="{{ 'component-price.css' | asset_url }}" media="print" onload="this.media='all'">
{%- endif -%}

{%- if section.settings.menu_type_desktop == 'mega' -%}
  <link rel="stylesheet" href="{{ 'component-mega-menu.css' | asset_url }}" media="print" onload="this.media='all'">

  {%- if section.blocks.size > 0 -%}
    <link rel="stylesheet" href="{{ 'component-card.css' | asset_url }}" media="print" onload="this.media='all'">
  {%- endif -%}
{%- endif -%}

{%- liquid
  assign transparent_header = false
  if request.page_type != blank
    if section.settings.enable_transparent_header_home and request.page_type == 'index'
      assign transparent_header = true
    elsif section.settings.enable_transparent_header_custom_list contains request.page_type or section.settings.enable_transparent_header_custom_list contains template.suffix
      assign transparent_header = true
    endif
  endif
-%}

<style>
  header-drawer {
    justify-self: start;
    margin-left: -1.2rem;
  }

  {% if transparent_header %}
    .section-header:not(.scrolled-past-header) .header-wrapper:not(:focus-within) .h2,
    .section-header:not(.scrolled-past-header) .header-wrapper:not(:focus-within) .header__icon,
    .section-header:not(.scrolled-past-header) .header-wrapper:not(:focus-within) .header__menu-item,
    .section-header:not(.scrolled-past-header) .header-wrapper:not(:focus-within) .header__active-menu-item,
    .section-header:not(.scrolled-past-header) .header-wrapper:not(:focus-within) .header__heading-link,
    .section-header:not(.scrolled-past-header) .header-wrapper:not(:focus-within) .localization-selector.link {
      color: {{ section.settings.transparent_text_color }};
    }

    @media screen and (min-width: 990px) {
      .section-header:not(.scrolled-past-header) .header-wrapper--dropdown .h2,
      .section-header:not(.scrolled-past-header) .header-wrapper--dropdown .header__icon,
      .section-header:not(.scrolled-past-header) .header-wrapper--dropdown .header__menu-item,
      .section-header:not(.scrolled-past-header) .header-wrapper--dropdown .header__active-menu-item,
      .section-header:not(.scrolled-past-header) .header-wrapper--dropdown .header__heading-link,
      .section-header:not(.scrolled-past-header) .header-wrapper--dropdown .localization-selector.link {
        color: {{ section.settings.transparent_text_color }};
      }
   }
  {% endif %}

  {% if section.settings.sticky_header_type == 'on-scroll-up' %}
    .section-header.scrolled-past-header .header-wrapper.gradient:not(:focus-within) {
      animation: headerReveal var(--duration-long) ease;
    }
    @keyframes headerReveal {
      0% {
        background-color: transparent;
        transform: translateY(-2em);
      }
      50% {
        transform: translateY(0em);
      }
      100% {
        background-color: var(--gradient-background);
      }
    }
  {% endif %}

  {%- if section.settings.sticky_header_type == 'reduce-logo-size' -%}
    .scrolled-past-header .header__heading-logo-wrapper {
      width: 75%;
    }
  {%- endif -%}

  {%- if section.settings.menu_type_desktop != "drawer" -%}
    @media screen and (min-width: 990px) {
      header-drawer {
        display: none;
      }
    }
  {%- endif -%}

  .menu-drawer-container {
    display: flex;
  }

  .list-menu {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .list-menu--inline {
    display: inline-flex;
    flex-wrap: wrap;
  }

  summary.list-menu__item {
    padding-right: 2.7rem;
  }

  .list-menu__item {
    display: flex;
    align-items: center;
    line-height: calc(1 + 0.3 / var(--font-body-scale));
  }

  .list-menu__item--link {
    text-decoration: none;
    padding-bottom: 1rem;
    padding-top: 1rem;
    line-height: calc(1 + 0.8 / var(--font-body-scale));
  }

  @media screen and (min-width: 750px) {
    .list-menu__item--link {
      padding-bottom: 0.5rem;
      padding-top: 0.5rem;
    }
  }
</style>

{%- style -%}
  .header {
    padding: {{ section.settings.padding_top | times: 0.5 | round: 0 }}px 3rem {{ section.settings.padding_bottom | times: 0.5 | round: 0 }}px 3rem;
  }

  .section-header {
    position: sticky; /* This is for fixing a Safari z-index issue. PR #2147 */
    margin-bottom: {{ section.settings.margin_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-header {
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }

  @media screen and (min-width: 990px) {
    .header {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<script src="{{ 'cart-notification.js' | asset_url }}" defer="defer"></script>

{%- liquid
  for block in section.blocks
    if block.type == '@app'
      assign has_app_block = true
    endif
  endfor

  assign drawer_menu = settings.menu_drawer | default: section.settings.menu
-%}

{% liquid
  assign header_tag = 'div'
  if section.settings.sticky_header_type != 'none'
    assign header_tag = 'sticky-header'
  endif
%}

<{{ header_tag }} {% if section.settings.sticky_header_type != 'none' %}data-sticky-type="{{ section.settings.sticky_header_type }}"{% endif %} class="header-wrapper color-{{ section.settings.color_scheme }} header-wrapper--{{ section.settings.menu_type_desktop }} gradient {% if section.settings.show_line_separator %} header-wrapper--border-bottom{% endif %}{% if transparent_header %} header-wrapper--transparent{% endif %}">
  {%- liquid
    assign social_links = false
    assign localization_forms = false

    if settings.social_twitter_link != blank or settings.social_facebook_link != blank or settings.social_pinterest_link != blank or settings.social_instagram_link != blank or settings.social_youtube_link != blank or settings.social_vimeo_link != blank or settings.social_tiktok_link != blank or settings.social_tumblr_link != blank or settings.social_snapchat_link != blank
      assign social_links = true
    endif

    if localization.available_countries.size > 1 and section.settings.enable_country_selector or section.settings.enable_language_selector and localization.available_languages.size > 1
      assign localization_forms = true
    endif
  -%}
  <header class="header header--{{ section.settings.logo_position }} header--mobile-{{ section.settings.mobile_logo_position }} page-width{% if section.settings.menu_type_desktop == 'drawer' %} drawer-menu{% endif %}{% if drawer_menu != blank %} header--has-menu{% endif %}{% if has_app_block %} header--has-app{% endif %}{% if social_links %} header--has-social{% endif %}{% if shop.customer_accounts_enabled %} header--has-account{% endif %}{% if localization_forms %} header--has-localizations{% endif %}">
    {%- liquid
      if drawer_menu != blank
        render 'header-drawer', drawer_menu: drawer_menu
      endif

      if section.settings.logo_position == 'top-center' or section.settings.menu == blank
        render 'header-search', input_id: 'Search-In-Modal-1'
      endif
    -%}

    {%- if section.settings.logo_position != 'middle-center' -%}
      {%- if request.page_type == 'index' and section.settings.show_homepage_logo_h1 -%}
        <h1 class="header__heading">
      {%- endif -%}
          <a href="{{ routes.root_url }}" class="header__heading-link link link--text focus-inset">
            {%- if settings.logo != blank -%}
              <div class="header__heading-logo-wrapper">
                {%- liquid
                  assign preload = true
                  if transparent_header and section.settings.transparent_logo != blank
                    assign preload = false
                  endif
                -%}
                {%- assign logo_alt = settings.logo.alt | default: shop.name | escape -%}
                {%- assign logo_height = settings.logo_width | divided_by: settings.logo.aspect_ratio -%}
                {% capture sizes %}(max-width: {{ settings.logo_width | times: 2 }}px) 50vw, {{ settings.logo_width }}px{% endcapture %}
                {% capture widths %}{{ settings.logo_width }}, {{ settings.logo_width | times: 1.5 | round }}, {{ settings.logo_width | times: 2 }}{% endcapture %}
                {{ settings.logo | image_url: width: 600 | image_tag:
                  class: 'header__heading-logo header__heading-logo--default motion-reduce',
                  widths: widths,
                  height: logo_height,
                  width: settings.logo_width,
                  alt: logo_alt,
                  sizes: sizes,
                  preload: preload
                }}
                {%- if transparent_header and section.settings.transparent_logo != blank -%}
                  {%- assign transparent_logo_height = section.settings.transparent_logo | divided_by: section.settings.transparent_logo.aspect_ratio -%}
                  {{ section.settings.transparent_logo | image_url: width: 600 | image_tag:
                    class: 'header__heading-logo header__heading-logo--transparent motion-reduce',
                    widths: widths,
                    height: transparent_logo_height,
                    width: settings.logo_width,
                    alt: logo_alt,
                    sizes: sizes,
                    preload: true
                  }}
                {%- endif -%}
              </div>
            {%- else -%}
              <span class="h2">{{ shop.name }}</span>
            {%- endif -%}
          </a>
      {%- if request.page_type == 'index' and section.settings.show_homepage_logo_h1 -%}
        </h1>
      {%- endif -%}
    {%- endif -%}

    {%- liquid
      if section.settings.menu != blank
        if section.settings.menu_type_desktop == 'dropdown'
          render 'header-dropdown-menu'
        elsif section.settings.menu_type_desktop != 'drawer'
          render 'header-mega-menu'
        endif
      endif
    %}

    {%- if section.settings.logo_position == 'middle-center' -%}
      {%- if request.page_type == 'index' and section.settings.show_homepage_logo_h1 -%}
        <h1 class="header__heading">
      {%- endif -%}
          <a href="{{ routes.root_url }}" class="header__heading-link link link--text focus-inset">
            {%- if settings.logo != blank -%}
              <div class="header__heading-logo-wrapper">
                {%- liquid
                  assign preload = true
                  if transparent_header and section.settings.transparent_logo != blank
                    assign preload = false
                  endif
                -%}
                {%- assign logo_alt = settings.logo.alt | default: shop.name | escape -%}
                {%- assign logo_height = settings.logo_width | divided_by: settings.logo.aspect_ratio -%}
                {% capture sizes %}(min-width: 750px) {{ settings.logo_width }}px, 50vw{% endcapture %}
                {% capture widths %}{{ settings.logo_width }}, {{ settings.logo_width | times: 1.5 | round }}, {{ settings.logo_width | times: 2 }}{% endcapture %}
                {{ settings.logo | image_url: width: 600 | image_tag:
                  class: 'header__heading-logo header__heading-logo--default motion-reduce',
                  widths: widths,
                  height: logo_height,
                  width: settings.logo_width,
                  alt: logo_alt,
                  sizes: sizes,
                  preload: preload
                }}
                {%- if transparent_header and section.settings.transparent_logo != blank -%}
                  {%- assign transparent_logo_height = section.settings.transparent_logo | divided_by: section.settings.transparent_logo.aspect_ratio -%}
                  {{ section.settings.transparent_logo | image_url: width: 600 | image_tag:
                    class: 'header__heading-logo header__heading-logo--transparent motion-reduce',
                    widths: widths,
                    height: transparent_logo_height,
                    width: settings.logo_width,
                    alt: logo_alt,
                    sizes: sizes,
                    preload: true
                  }}
                {%- endif -%}
              </div>
            {%- else -%}
              <span class="h2">{{ shop.name }}</span>
            {%- endif -%}
          </a>
      {%- if request.page_type == 'index' and section.settings.show_homepage_logo_h1 -%}
        </h1>
      {%- endif -%}
    {%- endif -%}

    <div class="header__icons{% if section.settings.enable_country_selector or section.settings.enable_language_selector %} header__icons--localization header-localization{% endif %}">
      {{ section.settings.icons_menu_custom_liquid }}

      <div class="desktop-localization-wrapper">
        {%- if section.settings.enable_country_selector and localization.available_countries.size > 1 -%}
          <localization-form class="small-hide medium-hide" data-prevent-hide>
            {%- form 'localization', id: 'HeaderCountryForm', class: 'localization-form' -%}
              <div>
                <p class="visually-hidden" id="HeaderCountryLabel">{{ 'localization.country_label' | t }}</p>
                {%- render 'country-localization', localPosition: 'HeaderCountry' -%}
              </div>
            {%- endform -%}
          </localization-form>
        {% endif %}

        {%- if section.settings.enable_language_selector and localization.available_languages.size > 1 -%}
          <localization-form class="small-hide medium-hide" data-prevent-hide>
            {%- form 'localization', id: 'HeaderLanguageForm', class: 'localization-form' -%}
              <div>
                <p class="visually-hidden" id="HeaderLanguageLabel">{{ 'localization.language_label' | t }}</p>
                {%- render 'language-localization', localPosition: 'HeaderLanguage' -%}
              </div>
            {%- endform -%}
          </localization-form>
        {%- endif -%}
      </div>
      {%- if settings.search_display != 'none' -%}
        {% render 'header-search', input_id: 'Search-In-Modal' %}
      {%- endif -%}

      {%- if shop.customer_accounts_enabled and settings.account_display != 'none' -%}
        <a
          href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}"
          class="header__icon header__icon__{{ settings.account_display }} header__icon--account link focus-inset{% if section.settings.menu != blank %} small-hide{% endif %}"
          rel="nofollow"
        >
          {%- if settings.account_display != 'icon' -%}
          <span>
            {%- liquid
              if customer
                echo 'customer.account_fallback' | t
              else
                echo 'customer.log_in' | t
              endif
            -%}
          </span>
          {%- else -%}
            {%- if section.settings.enable_customer_avatar -%}
              <account-icon>
                {%- if customer and customer.has_avatar? -%}
                  {{ customer | avatar }}
                {%- else -%}
                <span class="svg-wrapper">{{ 'icon-account.svg' | inline_asset_content }}</span>
                {%- endif -%}
              </account-icon>
            {%- else -%}
              <span class="svg-wrapper">{{ 'icon-account.svg' | inline_asset_content }}</span>
            {%- endif -%}
            <span class="visually-hidden">
              {%- liquid
                if customer
                  echo 'customer.account_fallback' | t
                else
                  echo 'customer.log_in' | t
                endif
              -%}
            </span>
          {%- endif -%}
        </a>
      {%- endif -%}

      {%- for block in section.blocks -%}
        {%- case block.type -%}
          {%- when '@app' -%}
            {% render block %}
        {%- endcase -%}
      {%- endfor -%}

      {%- if settings.cart_display != 'none' -%}
        <a href="{{ routes.cart_url }}" class="header__icon header__icon__{{ settings.cart_display }} header__icon--cart link focus-inset" id="cart-icon-bubble">
          {%- render 'cart-icon-bubble-content' -%}
        </a>
      {%- endif -%}

      {{ section.settings.icons_menu_custom_liquid_after }}
    </div>
  </header>
</{{ header_tag }}>

{%- if settings.cart_type == "notification" -%}
  {%- render 'cart-notification', color_scheme: section.settings.color_scheme, desktop_menu_type: section.settings.menu_type_desktop -%}
{%- endif -%}

{% javascript %}
  class StickyHeader extends HTMLElement {
    constructor() {
      super();
    }

    connectedCallback() {
      this.header = document.querySelector('.section-header');
      this.headerIsAlwaysSticky = this.getAttribute('data-sticky-type') === 'always' || this.getAttribute('data-sticky-type') === 'reduce-logo-size';
      this.headerBounds = {};

      this.setHeaderHeight();

      window.matchMedia('(max-width: 990px)').addEventListener('change', this.setHeaderHeight.bind(this));

      if (this.headerIsAlwaysSticky) {
        this.header.classList.add('shopify-section-header-sticky');
      };

      this.currentScrollTop = 0;
      this.preventReveal = false;
      this.predictiveSearch = this.querySelector('predictive-search');

      this.onScrollHandler = this.onScroll.bind(this);
      this.hideHeaderOnScrollUp = () => this.preventReveal = true;

      this.addEventListener('preventHeaderReveal', this.hideHeaderOnScrollUp);
      window.addEventListener('scroll', this.onScrollHandler, false);

      this.createObserver();
    }

    setHeaderHeight() {
      document.documentElement.style.setProperty('--header-height', `${this.offsetHeight}px`);
    }

    disconnectedCallback() {
      // used for subnav section (reset)
      document.documentElement.style.setProperty('--header-bottom-position', `0px`);

      this.removeEventListener('preventHeaderReveal', this.hideHeaderOnScrollUp);
      window.removeEventListener('scroll', this.onScrollHandler);
    }

    createObserver() {
      let observer = new IntersectionObserver((entries, observer) => {
        this.headerBounds = entries[0].intersectionRect;
        observer.disconnect();
      });

      observer.observe(this.header);
    }

    onScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      if (this.predictiveSearch && this.predictiveSearch.isOpen) return;

      if (scrollTop > this.currentScrollTop && scrollTop > this.headerBounds.bottom) {
        this.header.classList.add('scrolled-past-header');
        if (this.preventHide) return;
        requestAnimationFrame(this.hide.bind(this));
      } else if (scrollTop < this.currentScrollTop && scrollTop > this.headerBounds.bottom) {
        this.header.classList.add('scrolled-past-header');
        if (!this.preventReveal) {
          requestAnimationFrame(this.reveal.bind(this));
        } else {
          window.clearTimeout(this.isScrolling);

          this.isScrolling = setTimeout(() => {
            this.preventReveal = false;
          }, 66);

          requestAnimationFrame(this.hide.bind(this));
        }
      } else if (scrollTop <= this.headerBounds.top) {
        this.header.classList.remove('scrolled-past-header');
        requestAnimationFrame(this.reset.bind(this));
      }

      this.currentScrollTop = scrollTop;
    }

    hide() {
      if (this.headerIsAlwaysSticky) return;
      this.header.classList.add('shopify-section-header-hidden', 'shopify-section-header-sticky');
      this.closeMenuDisclosure();
      this.closeSearchModal();

      // used for subnav section
      document.documentElement.style.setProperty('--header-bottom-position', `0px`);
    }

    reveal() {
      // used for subnav section
      this.borderOffset = this.borderOffset || this.header.querySelector('.header-wrapper').classList.contains('header-wrapper--border-bottom') ? 1 : 0;
      document.documentElement.style.setProperty('--header-bottom-position', `${parseInt(this.header.getBoundingClientRect().bottom - this.borderOffset)}px`);

      if (this.headerIsAlwaysSticky) return;
      this.header.classList.add('shopify-section-header-sticky', 'animate');
      this.header.classList.remove('shopify-section-header-hidden');
    }

    reset() {
      if (this.headerIsAlwaysSticky) return;
      this.header.classList.remove('shopify-section-header-hidden', 'shopify-section-header-sticky', 'animate');
    }

    closeMenuDisclosure() {
      this.disclosures = this.disclosures || this.header.querySelectorAll('header-menu');
      /* bs-fix
        - fix for sub-nav when icons on nav are off
      */
      if (this.disclosures) this.disclosures.forEach(disclosure => disclosure.close());
    }

    closeSearchModal() {
      this.searchModal = this.searchModal || this.header.querySelector('details-modal');
      /* bs-fix
        - fix for sub-nav when icons on nav are off
      */
      if (this.searchModal) this.searchModal.close(false);
    }
  }

  customElements.define('sticky-header', StickyHeader);
{% endjavascript %}

<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "Organization",
    "name": {{ shop.name | json }},
    {% if settings.logo %}
      "logo": {{ settings.logo | image_url: width: 500 | prepend: "https:" | json }},
    {% endif %}
    "sameAs": [
      {{ settings.social_twitter_link | json }},
      {{ settings.social_facebook_link | json }},
      {{ settings.social_pinterest_link | json }},
      {{ settings.social_instagram_link | json }},
      {{ settings.social_tiktok_link | json }},
      {{ settings.social_tumblr_link | json }},
      {{ settings.social_snapchat_link | json }},
      {{ settings.social_youtube_link | json }},
      {{ settings.social_vimeo_link | json }}
    ],
    "url": {{ request.origin | append: page.url | json }}
  }
</script>

{%- if request.page_type == 'index' -%}
  {% assign potential_action_target = request.origin | append: routes.search_url | append: "?q={search_term_string}" %}
  <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": {{ shop.name | json }},
      "potentialAction": {
        "@type": "SearchAction",
        "target": {{ potential_action_target | json }},
        "query-input": "required name=search_term_string"
      },
      "url": {{ request.origin | append: page.url | json }}
    }
  </script>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.header.name",
  "class": "section-header",
  "settings": [
    {
      "type": "select",
      "id": "logo_position",
      "options": [
        {
          "value": "top-left",
          "label": "t:sections.header.settings.logo_position.options__2.label"
        },
        {
          "value": "top-center",
          "label": "t:sections.header.settings.logo_position.options__3.label"
        },
        {
          "value": "middle-left",
          "label": "t:sections.header.settings.logo_position.options__1.label"
        },
        {
          "value": "middle-center",
          "label": "t:sections.header.settings.logo_position.options__4.label"
        }
      ],
      "default": "middle-left",
      "label": "t:sections.header.settings.logo_position.label",
      "info": "t:sections.header.settings.logo_help.content"
    },
    {
      "type": "link_list",
      "id": "menu",
      "default": "main-menu",
      "label": "t:sections.header.settings.menu.label"
    },
    {
      "type": "select",
      "id": "menu_type_desktop",
      "options": [
        {
          "value": "dropdown",
          "label": "t:sections.header.settings.menu_type_desktop.options__1.label"
        },
        {
          "value": "mega",
          "label": "t:sections.header.settings.menu_type_desktop.options__2.label"
        },
        {
          "value": "drawer",
          "label": "t:sections.header.settings.menu_type_desktop.options__3.label"
        }
      ],
      "default": "dropdown",
      "label": "t:sections.header.settings.menu_type_desktop.label",
      "info": "t:sections.header.settings.menu_type_desktop.info"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 3,
      "label": "t:sections.multicolumn.settings.columns_desktop.label",
      "info": "Applies only to mega menu"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.collection-list.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.collection-list.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.collection-list.settings.image_ratio.options__3.label"
        }
      ],
      "default": "square",
      "label": "t:sections.collection-list.settings.image_ratio.label",
      "info": "Applies only to mega menu blocks"
    },
    {
      "type": "select",
      "id": "sticky_header_type",
      "options": [
        {
          "value": "none",
          "label": "t:sections.header.settings.sticky_header_type.options__1.label"
        },
        {
          "value": "on-scroll-up",
          "label": "t:sections.header.settings.sticky_header_type.options__2.label"
        },
        {
          "value": "always",
          "label": "t:sections.header.settings.sticky_header_type.options__3.label"
        },
        {
          "value": "reduce-logo-size",
          "label": "t:sections.header.settings.sticky_header_type.options__4.label"
        }
      ],
      "default": "on-scroll-up",
      "label": "t:sections.header.settings.sticky_header_type.label"
    },
    {
      "type": "checkbox",
      "id": "show_line_separator",
      "default": true,
      "label": "t:sections.header.settings.show_line_separator.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__1.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "color_scheme",
      "id": "menu_color_scheme",
      "label": "t:sections.header.settings.menu_color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.transparent_overlay_header.content"
    },
    {
      "type": "checkbox",
      "id": "enable_transparent_header_home",
      "default": false,
      "label": "t:sections.header.settings.enable_transparent_header.label"
    },
    {
      "type": "text",
      "id": "enable_transparent_header_custom_list",
      "label": "t:sections.header.settings.enable_transparent_header_custom_list.label",
      "info": "t:sections.header.settings.enable_transparent_header_custom_list.info"
    },
    {
      "type": "image_picker",
      "id": "transparent_logo",
      "label": "t:sections.header.settings.transparent_logo.label",
      "info": "t:sections.header.settings.transparent_logo.info"
    },
    {
      "type": "color",
      "id": "transparent_text_color",
      "default": "#FFFFFF",
      "label": "t:sections.header.settings.transparent_text_color.label",
      "info": "t:sections.header.settings.transparent_text_color.info"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__3.content",
      "info": "t:sections.header.settings.header__4.info"
    },
    {
      "type": "checkbox",
      "id": "enable_country_selector",
      "default": false,
      "label": "t:sections.header.settings.enable_country_selector.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__5.content",
      "info": "t:sections.header.settings.header__6.info"
    },
    {
      "type": "checkbox",
      "id": "enable_language_selector",
      "default": false,
      "label": "t:sections.header.settings.enable_language_selector.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__7.content",
      "info": "t:sections.header.settings.header__7.info"
    },
    {
      "type": "checkbox",
      "id": "enable_customer_avatar",
      "default": true,
      "label": "t:sections.header.settings.enable_customer_avatar.label",
      "info": "t:sections.header.settings.enable_customer_avatar.info"
    },
    {
      "type": "header",
      "content": "SEO Options"
    },
    {
      "type": "checkbox",
      "id": "show_homepage_logo_h1",
      "label": "Show logo as h1 on homepage",
      "default": true
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.mobile_layout.content"
    },
    {
      "type": "select",
      "id": "mobile_logo_position",
      "options": [
        {
          "value": "center",
          "label": "t:sections.header.settings.mobile_logo_position.options__1.label"
        },
        {
          "value": "left",
          "label": "t:sections.header.settings.mobile_logo_position.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.header.settings.mobile_logo_position.label"
    },
    {
      "type": "checkbox",
      "id": "show_content_in_drawer_menu",
      "default": false,
      "label": "Enable content in drawer menu",
      "info": "Content must be added as blocks"
    },
    {
      "type": "header",
      "content": "Custom overrides area"
    },
    {
      "type": "liquid",
      "id": "icons_menu_custom_liquid",
      "label": "Bar icons custom liquid",
      "info": "Code renders to left of icons on main menu bar (also present on mobile)"
    },
    {
      "type": "liquid",
      "id": "icons_menu_custom_liquid_after",
      "label": "Bar icons custom liquid after",
      "info": "Code renders to right of icons on main menu bar (also present on mobile)"
    },
    {
      "type": "liquid",
      "id": "drawer_custom_liquid",
      "label": "Drawer custom liquid",
      "info": "Code renders just under menu area on menu drawer"
    },
    {
      "type": "header",
      "content": "t:sections.all.spacing"
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.header.settings.margin_bottom.label",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 36,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 20
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 36,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 20
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "sub_menu_columns",
      "name": "Sub menu columns",
      "settings": [
        {
          "type": "text",
          "id": "title_match",
          "label": "Menu title to place under"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            },
            {
              "value": "3",
              "label": "3"
            },
            {
              "value": "4",
              "label": "4"
            },
            {
              "value": "5",
              "label": "5"
            },
            {
              "value": "6",
              "label": "6"
            }
          ],
          "default": "3",
          "label": "Column span",
          "info": "Overrides header setting 'Number of columns on desktop' for sub-menu."
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.custom-liquid.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "default": "My custom liquid",
          "label": "Title",
          "info": "Only used to organize custom liquid in side bar."
        },
        {
          "type": "text",
          "id": "title_match",
          "label": "Menu title to place under"
        },
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            },
            {
              "value": "3",
              "label": "3"
            },
            {
              "value": "4",
              "label": "4"
            },
            {
              "value": "5",
              "label": "5"
            },
            {
              "value": "6",
              "label": "6"
            }
          ],
          "default": "1",
          "label": "Column span"
        }
      ]
    },
    {
      "type": "image",
      "name": "t:sections.collage.blocks.image.name",
      "settings": [
        {
          "type": "text",
          "id": "title_match",
          "label": "Menu title to place under"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.collage.blocks.image.settings.image.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.multicolumn.blocks.column.settings.link.label"
        },
        {
          "type": "text",
          "id": "link_label",
          "label": "Link label",
          "info": "Leave the label blank to hide."
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            },
            {
              "value": "3",
              "label": "3"
            },
            {
              "value": "4",
              "label": "4"
            },
            {
              "value": "5",
              "label": "5"
            },
            {
              "value": "6",
              "label": "6"
            }
          ],
          "default": "1",
          "label": "Column span"
        }
      ]
    },
    {
      "type": "product",
      "name": "t:sections.collage.blocks.product.name",
      "settings": [
        {
          "type": "text",
          "id": "title_match",
          "label": "Menu title to place under"
        },
        {
          "type": "product",
          "id": "product",
          "label": "t:sections.collage.blocks.product.settings.product.label"
        },
        {
          "type": "checkbox",
          "id": "second_image",
          "default": false,
          "label": "t:sections.collage.blocks.product.settings.second_image.label"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            },
            {
              "value": "3",
              "label": "3"
            },
            {
              "value": "4",
              "label": "4"
            },
            {
              "value": "5",
              "label": "5"
            },
            {
              "value": "6",
              "label": "6"
            }
          ],
          "default": "1",
          "label": "Column span"
        },
        {
          "type": "header",
          "content": "Custom data (metafields)"
        },
        {
          "type": "paragraph",
          "content": "These settings leverage metafields for more product card options. They require product or variant metafields to be set in order to show data."
        },
        {
          "type": "checkbox",
          "id": "show_alternative_title",
          "default": false,
          "label": "Show alternative title",
          "info": "Requires a product metafield (type: single line text) with namespace and key set to: custom.alternative_product_title"
        }
      ]
    },
    {
      "type": "collection",
      "name": "t:sections.collage.blocks.collection.name",
      "settings": [
        {
          "type": "text",
          "id": "title_match",
          "label": "Menu title to place under"
        },
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.collage.blocks.collection.settings.collection.label"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "1",
              "label": "1"
            },
            {
              "value": "2",
              "label": "2"
            },
            {
              "value": "3",
              "label": "3"
            },
            {
              "value": "4",
              "label": "4"
            },
            {
              "value": "5",
              "label": "5"
            },
            {
              "value": "6",
              "label": "6"
            }
          ],
          "default": "1",
          "label": "Column span"
        }
      ]
    }
  ]
}
{% endschema %}