{% comment %}
  bs-add
  - hide/show option added
  - added color scheme options
  - added padding options
{% endcomment %}

{{ 'section-main-page.css' | asset_url | stylesheet_tag }}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="page-width page-width--narrow section-{{ section.id }}-padding">
    <h2 class="page-title {{ section.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
      {%- if section.settings.page.title != blank -%}
        {{ section.settings.page.title | escape }}
      {%- else -%}
        {{ 'sections.page.title' | t }}
      {%- endif -%}
    </h2>
    <div class="rte{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
      {%- if section.settings.page.content != blank -%}
        {{ section.settings.page.content }}
      {%- else -%}
        <div class="page-placeholder-wrapper placeholder">
          {{ 'page' | placeholder_svg_tag: 'page-placeholder' }}
        </div>
      {%- endif -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.page.name",
  "tag": "section",
  "class": "section section--page",
  "disabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "page",
      "id": "page",
      "label": "t:sections.page.settings.page.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "header",
      "content": "Color Scheme"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Padding options"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 36
    },
    {
      "type": "header",
      "content": "Section spacing desktop (>750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 36
    },
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    }
  ],
  "presets": [
    {
      "name": "t:sections.page.presets.name"
    }
  ]
}
{% endschema %}
