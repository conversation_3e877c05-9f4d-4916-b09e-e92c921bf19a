/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "name": "t:sections.footer.name",
  "type": "footer",
  "sections": {
    "96e551f4-7268-4f7f-a5f9-fb9abc31466d": {
      "type": "custom-liquid",
      "settings": {
        "description": "Google Tag Manager Body",
        "custom_liquid": "<!-- Google Tag Manager (noscript) -->\n<noscript><iframe src=\"https://www.googletagmanager.com/ns.html?id=GTM-WBF8MG3P\"\nheight=\"0\" width=\"0\" style=\"display:none;visibility:hidden\"></iframe></noscript>\n<!-- End Google Tag Manager (noscript) -->",
        "hide_size": "",
        "color_scheme": "",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0
      }
    },
    "d2832fea-8b19-413d-a8cb-5f9762149dd9": {
      "type": "custom-liquid",
      "settings": {
        "description": "Script - text-wrap-balance",
        "custom_liquid": "<script>\ndocument.addEventListener(\"DOMContentLoaded\", (event) => {\n  if (!window.matchMedia('screen and (max-width: 749px)').matches && !window.CSS.supports('text-wrap', 'balance')) {\n    const elements = document.querySelectorAll('.textWrapBalance,.banner__box')\n    const bonResizeObserver = new ResizeObserver((entries) => {\n      entries.forEach((entry) => {\n        relayout(entry.target)\n      })\n    })\n    elements.forEach((element) => {\n      relayout(element)\n      bonResizeObserver.observe(element)\n    })\n    window.addEventListener('resize', () => {\n      elements.forEach((element) => {\n        relayout(element)\n      })\n    })\n  }\n});\n\nfunction relayout (wrapper, ratio = 1) {\n  const container = wrapper.parentElement\n\n  const update = (width) => (wrapper.style.maxWidth = width + 'px')\n\n  wrapper.style.display = 'inline-block'\n  wrapper.style.verticalAlign = 'top'\n  // Reset wrapper width\n  wrapper.style.maxWidth = ''\n\n  // Get the initial container size\n  const width = container.clientWidth\n  const height = container.clientHeight\n\n  // Synchronously do binary search and calculate the layout\n  let lower = width / 2 - 0.25\n  let upper = width + 0.5\n  let middle\n\n  if (width) {\n    // Ensure we don't search widths lower than when the text overflows\n    update(lower)\n    lower = Math.max(wrapper.scrollWidth, lower)\n\n    while (lower + 1 < upper) {\n      middle = Math.round((lower + upper) / 2)\n      update(middle)\n      if (container.clientHeight === height) {\n        upper = middle\n      } else {\n        lower = middle\n      }\n    }\n\n    update(upper * ratio + width * (1 - ratio))\n  }\n}\n</script>",
        "hide_size": "",
        "color_scheme": "",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0
      }
    },
    "footer": {
      "type": "footer",
      "blocks": {
        "89d1789f-ca05-4c37-afd4-17786166d04a": {
          "type": "shopify://apps/forms/blocks/inline/8744a304-fcb1-4347-b211-bb6b4759a76a",
          "disabled": true,
          "settings": {
            "form_id": "45638",
            "text_color": "#202020",
            "button_background_color": "#202020",
            "button_label_color": "#ffffff",
            "links_color": "#1878b9",
            "errors_color": "#e02229",
            "text_alignment": "center",
            "form_alignment": "center",
            "padding_top": 0,
            "padding_bottom": 0,
            "padding_right": 0,
            "padding_left": 0
          }
        },
        "6d92b59a-3805-46e7-9088-76016b08c0af": {
          "type": "newsletter",
          "settings": {
            "heading": "",
            "heading_liquid": "<div class=\"bon-footer-header--newsletter textWrapBalance\">\n<span style=\"line-height: 1.3;\">SUBSCRIBE TO OUR NEWSLETTER</span>\n</div>\n<style>\nul.policies:empty {\n  display: none;\n}\n@media screen and (max-width: 749px) {\n#shopify-section-{{ section.id }} .footer__content-bottom {\n  padding-top: 0;\n}\n#shopify-section-{{ section.id }} .footer-block__details-content {\n  margin-bottom: 2rem;\n}\n.footer__content-bottom {\n  border: none;\n}\n}\n</style>",
            "footer_location": "top",
            "content_alignment_mobile": "left",
            "content_alignment_desktop": "left",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-3-desktop",
            "hide_size": ""
          }
        },
        "27744a6c-cd6e-4c4d-a57c-9b296aca844d": {
          "type": "link_list",
          "settings": {
            "heading": "Company",
            "heading_liquid": "",
            "menu": "footer-menu-company",
            "make_collapsible_row": true,
            "footer_location": "top",
            "content_alignment_mobile": "left",
            "content_alignment_desktop": "left",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop",
            "hide_size": ""
          }
        },
        "footer-0": {
          "type": "link_list",
          "settings": {
            "heading": "Explore",
            "heading_liquid": "",
            "menu": "footer",
            "make_collapsible_row": true,
            "footer_location": "top",
            "content_alignment_mobile": "left",
            "content_alignment_desktop": "left",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop",
            "hide_size": ""
          }
        },
        "83d029d2-9be9-4593-9c87-d509bf3414eb": {
          "type": "link_list",
          "disabled": true,
          "settings": {
            "heading": "Compliance",
            "heading_liquid": "",
            "menu": "footer-menu-compliance",
            "make_collapsible_row": true,
            "footer_location": "top",
            "content_alignment_mobile": "left",
            "content_alignment_desktop": "left",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop",
            "hide_size": ""
          }
        },
        "9cd834f6-04ca-4ab2-a02a-30200b06d3a8": {
          "type": "custom_liquid",
          "settings": {
            "title": "My custom liquid",
            "custom_liquid": "<a class=\"bon-footer-image\" href=\"{{ shop.url }}\" style=\"display:flex;max-width:75px;height:75px; object-fit: contain;\">{{ images['downshift_logo_light_sideways.svg'] | image_url: width: 70 | image_tag: class: 'bon-image' }}</a>\n<style>\n@media screen and (max-width: 989px) {\n .bon-footer-image {\n   margin: 1rem auto;\n }\n}\n</style>",
            "footer_location": "top",
            "content_alignment_mobile": "center",
            "content_alignment_desktop": "left",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop",
            "hide_size": ""
          }
        },
        "footer-3": {
          "type": "copyright_payments",
          "settings": {
            "payment_enable": false,
            "show_policy": true,
            "additional_policy_menu": "",
            "additional_policy_menu_order": "after",
            "footer_location": "top",
            "content_alignment_mobile": "center",
            "content_alignment_desktop": "center",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-3-desktop",
            "hide_size": ""
          }
        },
        "57031279-cd04-4e5e-ac88-e5be69b7e66b": {
          "type": "custom_liquid",
          "settings": {
            "title": "My custom liquid",
            "custom_liquid": "<ul class=\"list-unstyled list-social content--mobile-center content--desktop-right footer__list-social\" role=\"list\">\n  <li class=\"list-social__item\">\n    <a target=\"_blank\" href=\"https://www.instagram.com/enjoyshift/\" class=\"link list-social__link\">\n      <span class=\"svg-wrapper\">\n        {{- 'icon-instagram.svg' | inline_asset_content -}}\n      </span>\n      <span class=\"visually-hidden\">Instagram</span>\n    </a>\n  </li>\n  <li class=\"list-social__item\">\n    <a target=\"_blank\" href=\"https://www.youtube.com/@EnjoyShift\" class=\"link list-social__link\">\n      <span class=\"svg-wrapper\">\n        {{- 'icon-youtube.svg' | inline_asset_content -}}\n      </span>\n      <span class=\"visually-hidden\">YouTube</span>\n    </a>\n  </li>\n  <li class=\"list-social__item\">\n    <a target=\"_blank\" href=\"https://www.tiktok.com/@enjoyshift\" class=\"link list-social__link\">\n      <span class=\"svg-wrapper\">\n        {{- 'icon-tiktok.svg' | inline_asset_content -}}\n      </span>\n      <span class=\"visually-hidden\">TikTok</span>\n    </a>\n  </li>\n</ul>",
            "footer_location": "top",
            "content_alignment_mobile": "center",
            "content_alignment_desktop": "right",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop",
            "hide_size": ""
          }
        },
        "5ffac379-65d5-4baf-a9e9-0d87b11687a7": {
          "type": "social",
          "disabled": true,
          "settings": {
            "footer_location": "top",
            "content_alignment_mobile": "center",
            "content_alignment_desktop": "right",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop",
            "hide_size": ""
          }
        },
        "ba3a7b6a-346c-4d0e-8d3f-db661fe274b1": {
          "type": "image",
          "disabled": true,
          "settings": {
            "image": "shopify://shop_images/downshift_logo_light_sideways.svg",
            "image_width": 70,
            "alignment": "",
            "footer_location": "bottom",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop",
            "hide_size": ""
          }
        },
        "e0e505b9-f525-484a-8a39-75bb882fc8a0": {
          "type": "text",
          "settings": {
            "heading": "",
            "heading_liquid": "",
            "subtext": "",
            "subtext_liquid": "<p class=\"bon-text-justify bon-line-height-125\" style=\"font-size: .85rem;opacity:.7;justify: inter-word;\">\nShift Naturals' products containing hemp-derived THC are intended exclusively for adults aged 21 and over. Our offerings, which include Delta-9 Tetrahydrocannabinol (THC), comply with the Controlled Substances Act, containing less than 0.3% THC derived from industrial hemp. These products are available for shipping in select states and should be used in accordance with the instructions on their labels. We advise against the use of our products during pregnancy or while nursing. Before incorporating any supplement into your routine, especially those containing hemp derivatives, we strongly recommend consulting a healthcare professional for guidance. These statements have not been evaluated by the FDA. This product is not intended to diagnose, treat, cure or prevent any disease.</p>",
            "footer_location": "bottom",
            "content_alignment_mobile": "left",
            "content_alignment_desktop": "left",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-3-desktop",
            "hide_size": ""
          }
        }
      },
      "block_order": [
        "89d1789f-ca05-4c37-afd4-17786166d04a",
        "6d92b59a-3805-46e7-9088-76016b08c0af",
        "27744a6c-cd6e-4c4d-a57c-9b296aca844d",
        "footer-0",
        "83d029d2-9be9-4593-9c87-d509bf3414eb",
        "9cd834f6-04ca-4ab2-a02a-30200b06d3a8",
        "footer-3",
        "57031279-cd04-4e5e-ac88-e5be69b7e66b",
        "5ffac379-65d5-4baf-a9e9-0d87b11687a7",
        "ba3a7b6a-346c-4d0e-8d3f-db661fe274b1",
        "e0e505b9-f525-484a-8a39-75bb882fc8a0"
      ],
      "custom_css": [
        ".footer-block__image-wrapper img {height: 80px; width: auto;}",
        ".footer-block__image-wrapper {margin-bottom: 0;}",
        ".footer-block__heading {font-size: calc(var(--font-heading-scale) * 1.6rem);}",
        ".footer-block__details-content .list-menu__item--link,.accordion__title,.accordion {text-transform: uppercase;}",
        ".footer__content-bottom-wrapper {align-items: center;}",
        ".footer-block .footer__list-social {padding-top: 2rem;}"
      ],
      "settings": {
        "color_scheme": "scheme-2",
        "columns_mobile": "1",
        "columns_tablet": 1,
        "columns_desktop": 5,
        "columns_footer_mobile": "1",
        "columns_footer_tablet": 2,
        "columns_footer_desktop": 5,
        "margin_top": 0,
        "padding_top": 60,
        "padding_bottom": 20,
        "padding_top_2": 24,
        "padding_bottom_2": 24
      }
    }
  },
  "order": [
    "96e551f4-7268-4f7f-a5f9-fb9abc31466d",
    "d2832fea-8b19-413d-a8cb-5f9762149dd9",
    "footer"
  ]
}
