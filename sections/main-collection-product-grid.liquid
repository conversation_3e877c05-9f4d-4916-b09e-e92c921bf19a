{% comment %}
  bs-add
  - card heading font size option
  - quick add button style option
  - option to show swatches on product card
  - option to show alternative title on product card
{% endcomment %}

{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}

{% if section.settings.image_shape == 'blob' %}
  {{ 'mask-blobs.css' | asset_url | stylesheet_tag }}
{%- endif -%}

{%- unless section.settings.quick_add == 'none' -%}
  {{ 'quick-add.css' | asset_url | stylesheet_tag }}
{%- endunless -%}

{%- if section.settings.quick_add == 'standard' -%}
  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- if section.settings.quick_add == 'bulk' -%}
  <script src="{{ 'quick-add-bulk.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'quantity-popover.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'price-per-item.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'quick-order-list.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- style -%}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

{% liquid
  assign media_aspect_ratio = section.settings.image_ratio | default: settings.card_image_ratio
  assign image_shape = section.settings.image_shape | default: settings.card_image_shape
  assign quick_add_behavior = section.settings.quick_add_behavior | default: settings.card_quick_add_behavior
  assign card_heading_font_size = section.settings.card_heading_font_size | default: settings.card_heading_font_size  
  assign show_secondary_image = settings.card_show_secondary_image
  if section.settings.show_secondary_image == "true"
    assign show_secondary_image = true
  elsif section.settings.show_secondary_image == "false"
    assign show_secondary_image = false
  endif
  assign show_vendor = settings.card_show_vendor
  if section.settings.show_vendor == "true"
    assign show_vendor = true
  elsif section.settings.show_vendor == "false"
    assign show_vendor = false
  endif
  assign show_rating = settings.card_show_rating
  if section.settings.show_rating == "true"
    assign show_rating = true
  elsif section.settings.show_rating == "false"
    assign show_rating = false
  endif
  assign show_alternative_title = settings.card_show_alternative_title
  if section.settings.show_alternative_title == "true"
    assign show_alternative_title = true
  elsif section.settings.show_alternative_title == "false"
    assign show_alternative_title = false
  endif
  assign show_card_product_custom_field = settings.card_show_card_product_custom_field
  if section.settings.show_card_product_custom_field == "true"
    assign show_card_product_custom_field = true
  elsif section.settings.show_card_product_custom_field == "false"
    assign show_card_product_custom_field = false
  endif
%}

<div class="section-{{ section.id }}-padding gradient color-{{ section.settings.color_scheme }}">
  {%- paginate collection.products by section.settings.products_per_page -%}
    {% comment %} Sort is the first tabbable element when filter type is vertical {% endcomment %}
    {%- if section.settings.enable_sorting and section.settings.filter_type == 'vertical' -%}
      <facet-filters-form class="facets facets-vertical-sort page-width small-hide">
        <form class="facets-vertical-form" id="FacetSortForm">
          <div class="facet-filters sorting caption">
            <div class="facet-filters__field">
              <h2 class="facet-filters__label caption-large text-body">
                <label for="SortBy">{{ 'products.facets.sort_by_label' | t }}</label>
              </h2>
              <div class="select">
                {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
                <select
                  name="sort_by"
                  class="facet-filters__sort select__select caption-large"
                  id="SortBy"
                  aria-describedby="a11y-refresh-page-message"
                >
                  {%- for option in collection.sort_options -%}
                    <option
                      value="{{ option.value | escape }}"
                      {% if option.value == sort_by %}
                        selected="selected"
                      {% endif %}
                    >
                      {{ option.name | escape }}
                    </option>
                  {%- endfor -%}
                </select>
                <span class="svg-wrapper">
                  {{- 'icon-caret.svg' | inline_asset_content -}}
                </span>
              </div>
            </div>
          </div>

          {%- if section.settings.show_count -%}
            <div class="product-count-vertical light" role="status">
              <h2 class="product-count__text text-body">
                <span id="ProductCountDesktop">
                  {%- if collection.results_count -%}
                    {{
                      'templates.search.results_with_count'
                      | t: terms: collection.terms, count: collection.results_count
                    }}
                  {%- elsif collection.products_count == collection.all_products_count -%}
                    {{ 'products.facets.product_count_simple' | t: count: collection.products_count }}
                  {%- else -%}
                    {{
                      'products.facets.product_count'
                      | t: product_count: collection.products_count, count: collection.all_products_count
                    }}
                  {%- endif -%}
                </span>
              </h2>
              {%- render 'loading-spinner' -%}
            </div>
          {%- endif -%}
        </form>
      </facet-filters-form>
    {%- endif -%}

    <div class="{% if section.settings.filter_type == 'vertical' %} facets-vertical page-width{% endif %}">
      {{ 'component-facets.css' | asset_url | stylesheet_tag }}
      <script src="{{ 'facets.js' | asset_url }}" defer="defer"></script>
      {%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
        <aside
          aria-labelledby="verticalTitle"
          class="facets-wrapper{% unless section.settings.enable_filtering %} facets-wrapper--no-filters{% endunless %}{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}"
          id="main-collection-filters"
          data-id="{{ section.id }}"
        >
          {% if section.settings.vertical_filter_content_before and section.settings.filter_type == 'vertical' %}
            {{ section.settings.vertical_filter_content_before }}
          {% endif %}
          {% render 'facets',
            results: collection,
            enable_filtering: section.settings.enable_filtering,
            enable_sorting: section.settings.enable_sorting,
            show_count: section.settings.show_count,
            filter_type: section.settings.filter_type,
            paginate: paginate
          %}
        </aside>
      {%- endif -%}

      <div
        class="product-grid-container{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
        id="ProductGridContainer"
        {% if settings.animations_reveal_on_scroll %}
          data-cascade
        {% endif %}
      >
        {%- if collection.products.size == 0 -%}
          <div class="collection collection--empty page-width" id="product-grid" data-id="{{ section.id }}">
            <div class="loading-overlay gradient"></div>
            <div class="title-wrapper center">
              <h2 class="title title--primary">
                {{ 'sections.collection_template.empty' | t -}}
                <br>
                {{
                  'sections.collection_template.use_fewer_filters_html'
                  | t: link: collection.url, class: 'underlined-link link'
                }}
              </h2>
            </div>
          </div>
        {%- else -%}
          <div
            class="collection{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}"
          >
            <div class="loading-overlay gradient"></div>
            <ul
              id="product-grid"
              data-id="{{ section.id }}"
              class="
                grid product-grid grid--{{ section.settings.columns_mobile }}-col-tablet-down
                grid--{{ section.settings.columns_desktop }}-col-desktop
                {% if section.settings.quick_add == 'bulk' %} collection-quick-add-bulk{% endif %}
              "
            >
              {% assign skip_card_product_styles = false %}
              {%- for product in collection.products -%}
                {% assign lazy_load = false %}
                {%- if forloop.index > 2 -%}
                  {%- assign lazy_load = true -%}
                {%- endif -%}
                <li
                  class="grid__item{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                  {% if settings.animations_reveal_on_scroll %}
                    data-cascade
                    style="--animation-order: {{ forloop.index }};"
                  {% endif %}
                >
                  {% render 'card-product',
                    card_product: product,
                    skip_styles: skip_card_product_styles,
                    section_id: section.id,
                    lazy_load: lazy_load,
                    quick_add: section.settings.quick_add,
                    quick_add_button_style: section.settings.quick_add_button_style,
                    media_aspect_ratio: media_aspect_ratio,
                    image_shape: image_shape,
                    show_secondary_image: show_secondary_image,
                    show_vendor: show_vendor,
                    show_rating: show_rating,
                    quick_add_behavior: quick_add_behavior,
                    card_heading_font_size: card_heading_font_size,
                    show_alternative_title: show_alternative_title,
                    show_card_product_custom_field: show_card_product_custom_field
                  %}
                </li>
                {%- assign skip_card_product_styles = true -%}
              {%- endfor -%}
            </ul>

            {%- if paginate.pages > 1 -%}
              {% render 'pagination', paginate: paginate, anchor: '' %}
            {%- endif -%}
          </div>
        {%- endif -%}
      </div>
    </div>
  {%- endpaginate -%}
  {% if section.settings.image_shape == 'arch' %}
    {{ 'mask-arch.svg' | inline_asset_content }}
  {%- endif -%}
</div>

{% schema %}
{
  "name": "t:sections.main-collection-product-grid.name",
  "class": "section section--main-collection-product-grid",
  "settings": [
    {
      "type": "header",
      "content": "Color Scheme"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Mobile layout",
      "info": "Devices with screens < 750px"
    },
    {
      "type": "number",
      "id": "padding_top_mobile",
      "label": "Padding top mobile",
      "info": "unit: px"
    },
    {
      "type": "number",
      "id": "padding_bottom_mobile",
      "label": "Padding bottom",
      "info": "unit: px"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "2",
      "label": "t:sections.main-collection-product-grid.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.main-collection-product-grid.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.main-collection-product-grid.settings.columns_mobile.options__2.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "Desktop layout",
      "info": "Devices with screens >= 750px"
    },
    {
      "type": "number",
      "id": "padding_top_desktop",
      "label": "Padding top",
      "info": "unit: px"
    },
    {
      "type": "number",
      "id": "padding_bottom_desktop",
      "label": "Padding bottom",
      "info": "unit: px"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 4,
      "label": "t:sections.main-collection-product-grid.settings.columns_desktop.label"
    },
    {
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header__1.content"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_filtering.label",
      "info": "t:sections.main-collection-product-grid.settings.enable_filtering.info"
    },
    {
      "type": "select",
      "id": "filter_type",
      "options": [
        {
          "value": "horizontal",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__1.label"
        },
        {
          "value": "vertical",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__2.label"
        },
        {
          "value": "drawer",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__3.label"
        }
      ],
      "default": "horizontal",
      "label": "t:sections.main-collection-product-grid.settings.filter_type.label",
      "info": "t:sections.main-collection-product-grid.settings.filter_type.info"
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_sorting.label"
    },
    {
      "type": "checkbox",
      "id": "show_count",
      "default": true,
      "label": "Show count",
      "info": "Either sorting or filter need to be enabled for visiblity."
    },
    {
      "type": "header",
      "content": "Other section settings"
    },
    {
      "type": "range",
      "id": "products_per_page",
      "min": 8,
      "max": 36,
      "step": 4,
      "default": 16,
      "label": "t:sections.main-collection-product-grid.settings.products_per_page.label"
    },
    {
      "type": "select",
      "id": "quick_add",
      "default": "none",
      "label": "t:sections.main-collection-product-grid.settings.quick_add.label",
      "info": "t:sections.main-collection-product-grid.settings.quick_add.info",
      "options": [
        {
          "value": "none",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_1"
        },
        {
          "value": "standard",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_2"
        },
        {
          "value": "bulk",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_3"
        }
      ]
    },
    {
      "type": "select",
      "id": "quick_add_button_style",
      "options": [
        {
          "value": "button button--primary",
          "label": "Solid"
        },
        {
          "value": "button button--secondary",
          "label": "Outline"
        },
        {
          "value": "link underlined-link",
          "label": "Link"
        },
        {
          "value": "button button-1",
          "label": "Custom 1"
        },
        {
          "value": "button button-2",
          "label": "Custom 2"
        },
        {
          "value": "button button-3",
          "label": "Custom 3"
        },
        {
          "value": "button button-4",
          "label": "Custom 4"
        },
        {
          "value": "button button-5",
          "label": "Custom 5"
        }
      ],
      "default": "button button--primary",
      "label": "Quick add button style"
    },
    {
      "type": "liquid",
      "id": "vertical_filter_content_before",
      "label": "Add content before vertical filters"
    },
    {
      "type": "header",
      "content": "Optional: Product card overrides"
    },
    {
      "type": "paragraph",
      "content": "Product cards are configured in theme settings. To override values for this section only, use the inputs below."
    },
    {
      "type": "text",
      "id": "image_ratio",
      "label": "t:sections.featured-collection.settings.image_ratio.label",
      "info": "Options: square, portrait, adapt"
    },
    {
      "type": "text",
      "id": "image_shape",
      "label": "Image shape",
      "info": "Options: default, arch, blob, chevronleft, chevronright, diamond, parallelogram, round"
    },
    {
      "type": "text",
      "id": "show_secondary_image",
      "label": "t:sections.featured-collection.settings.show_secondary_image.label",
      "info": "Options: true, false"
    },
    {
      "type": "text",
      "id": "card_heading_font_size",
      "label": "Card heading font size",
      "info": "Options: h1, h2, h3, h4, h5, h6 etc."
    },
    {
      "type": "text",
      "id": "show_vendor",
      "label": "t:sections.featured-collection.settings.show_vendor.label",
      "info": "Options: true, false"
    },
    {
      "type": "text",
      "id": "show_rating",
      "label": "t:sections.featured-collection.settings.show_rating.label",
      "info": "Options: true, false"
    },
    {
      "type": "text",
      "id": "quick_add_behavior",
      "label": "Quick add behavior"
    },
    {
      "type": "text",
      "id": "show_alternative_title",
      "label": "Show alternative title",
      "info": "Options: true, false"
    },
    {
      "type": "text",
      "id": "show_card_product_custom_field",
      "label": "Show custom field",
      "info": "Options: true, false"
    }
  ]
}
{% endschema %}