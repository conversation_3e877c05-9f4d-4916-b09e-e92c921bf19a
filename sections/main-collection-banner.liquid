{% comment %}
  bs-add
  - additional color scheme options
  - gradient option for color scheme
{% endcomment %}

{% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
{{ 'component-collection-hero.css' | asset_url | stylesheet_tag }}

{%- style -%}
  @media screen and (max-width: 749px) {
    .collection-hero--with-image .collection-hero__inner {
      padding-bottom: calc({{ settings.media_shadow_vertical_offset | at_least: 0 }}px + 2rem);
    }
  }
{%- endstyle -%}

{%- assign collection_image = section.settings.custom_image | default: collection.image -%}
<div class="collection-hero{% if section.settings.show_collection_image and collection_image != blank %} collection-hero--with-image{% endif %} color-{{ section.settings.color_scheme }} gradient">
  <div class="collection-hero__inner page-width {% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}">
    <div class="collection-hero__text-wrapper content--desktop-{{ section.settings.content_alignment }} content--mobile-{{ section.settings.mobile_content_alignment | default: section.settings.content_alignment }}">
      <h1 class="collection-hero__title {{ section.settings.heading_size }}">
        <span class="visually-hidden">{{ 'sections.collection_template.title' | t }}: </span>
        {{- section.settings.header_custom | default: collection.title | escape -}}
      </h1>

      {%- if section.settings.show_collection_description -%}
        <div class="collection-hero__description rte">{{ section.settings.description_custom | default: collection.description }}</div>
      {%- endif -%}
    </div>

    {%- if section.settings.show_collection_image and collection_image != blank -%}
      <div class="collection-hero__image-container media gradient">
        <img
          srcset="
            {%- if collection_image.width >= 165 -%}{{ collection_image | image_url: width: 165 }} 165w,{%- endif -%}
            {%- if collection_image.width >= 360 -%}{{ collection_image | image_url: width: 360 }} 360w,{%- endif -%}
            {%- if collection_image.width >= 535 -%}{{ collection_image | image_url: width: 535 }} 535w,{%- endif -%}
            {%- if collection_image.width >= 750 -%}{{ collection_image | image_url: width: 750 }} 750w,{%- endif -%}
            {%- if collection_image.width >= 1070 -%}{{ collection_image | image_url: width: 1070 }} 1070w,{%- endif -%}
            {%- if collection_image.width >= 1500 -%}{{ collection_image | image_url: width: 1500 }} 1500w,{%- endif -%}
            {{ collection_image | image_url }} {{ collection_image.width }}w
          "
          src="{{ collection_image | image_url: width: 750 }}"
          sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px, (min-width: 750px) calc(50vw - 130px), calc(50vw - 55px)"
          alt="{{ collection_image.alt | escape }}"
          width="{{ collection_image.width }}"
          height="{{ collection_image.height }}"
        >
      </div>
    {%- endif -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main-collection-banner.name",
  "class": "section section--main-collection-banner",
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.main-collection-banner.settings.paragraph.content"
    },
    {
      "type": "checkbox",
      "id": "show_collection_description",
      "default": true,
      "label": "t:sections.main-collection-banner.settings.show_collection_description.label"
    },
    {
      "type": "checkbox",
      "id": "show_collection_image",
      "default": false,
      "label": "t:sections.main-collection-banner.settings.show_collection_image.label",
      "info": "t:sections.main-collection-banner.settings.show_collection_image.info"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Content alignment"
    },
    {
      "type": "select",
      "id": "content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.rich-text.settings.content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.rich-text.settings.content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.rich-text.settings.content_alignment.options__3.label"
        }
      ],
      "default": "left",
      "label": "t:sections.rich-text.settings.content_alignment.label"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "",
          "label": "Inherit"
        },
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "",
      "label": "Content alignment (mobile)"
    },
    {
      "type": "header",
      "content": "Heading options"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__h6.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__h5.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__h4.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__h3.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "inline_richtext",
      "id": "header_custom",
      "label": "Title",
      "info": "Overrides collection title"
    },
    {
      "type": "header",
      "content": "Description options"
    },
    {
      "type": "richtext",
      "id": "description_custom",
      "label": "t:sections.rich-text.blocks.text.settings.text.label",
      "info": "Overrides collection description"
    },
    {
      "type": "header",
      "content": "Image options"
    },
    {
      "type": "image_picker",
      "id": "custom_image",
      "label": "Image",
      "info": "Overrides collection image"
    },
  ]
}
{% endschema %}