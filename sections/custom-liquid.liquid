{% comment %}
  bs-add
  - ability to hide/show section
  - ability to have separate padding for desktop/mobile
  - new color options and also gradient flag for colors
{% endcomment %}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="section-{{ section.id }}-padding">
    {{ section.settings.custom_liquid }}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.custom-liquid.name",
  "tag": "section",
  "class": "section section--custom-liquid",
  "settings": [
    {
      "type": "textarea",
      "id": "description",
      "label": "Description (optional)",
      "info": "Describe what this custom liquid does"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    },
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Section spacing mobile (<750px)"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 0
    },
    {
      "type": "header",
      "content": "Section spacing desktop (>750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:sections.custom-liquid.presets.name"
    }
  ]
}
{% endschema %}
