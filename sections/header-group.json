/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "name": "t:sections.header.name",
  "type": "header",
  "sections": {
    "announcement-bar": {
      "type": "announcement-bar",
      "blocks": {
        "announcement_kwrHGy": {
          "type": "announcement",
          "settings": {
            "text": "<strong>Try a 12 pack and </strong><a href=\"/products/downshift-gummy-variety-pack\" title=\"Downshift Gummy 2-Flavor Variety Pack\"><strong>gummies</strong></a><strong>, get free shipping</strong>",
            "text_liquid": "",
            "font_size": "h6"
          }
        },
        "announcement-bar-0": {
          "type": "announcement",
          "disabled": true,
          "settings": {
            "text": "<a href=\"/products/shift-variety-pack\" title=\"SHIFT Variety Pack\">THIS WEEKEND ONLY - 15% OFF COCKTAILS</a>",
            "text_liquid": "",
            "font_size": "h5"
          }
        }
      },
      "block_order": [
        "announcement_kwrHGy",
        "announcement-bar-0"
      ],
      "settings": {
        "hide_size": "",
        "color_scheme": "scheme-7e1a7ee4-b3ff-4c39-9666-ccf40e8910c5",
        "border": "",
        "content_width": "page-width",
        "bar_behavior": "slideshow",
        "marquee_gap_size": 1,
        "marquee_gap_size_mobile": 1,
        "marquee_speed": 60,
        "marquee_speed_mobile": 60,
        "marquee_image_width": 100,
        "marquee_image_width_mobile": 100,
        "auto_rotate": false,
        "change_slides_speed": 5,
        "show_social": false,
        "enable_country_selector": false,
        "enable_language_selector": false,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "header": {
      "type": "header",
      "blocks": {
        "custom_liquid_F3wEUz": {
          "type": "custom_liquid",
          "settings": {
            "title": "My custom liquid",
            "title_match": "DownShift",
            "custom_liquid": "<div class=\"tw:h-full tw:w-[80%] tw:flex tw:items-center\">\n<p class=\"body-copy-large\">DownShift is a <strong>familiar mellow buzz</strong> that eases stress and helps you unwind <strong>without feeling overwhelmed</strong> or hazy the next day.</p>\n</div>",
            "colomn_span_amount_desktop": "1"
          }
        },
        "image_cxrEQL": {
          "type": "image",
          "settings": {
            "title_match": "DownShift",
            "image": "shopify://shop_images/downshift-variety-pack-412510.png",
            "link": "/products/down-shift-variety-pack?variant=45191338950901",
            "link_label": "Drinks",
            "colomn_span_amount_desktop": "1"
          }
        },
        "image_JbB6rR": {
          "type": "image",
          "settings": {
            "title_match": "DownShift",
            "image": "shopify://shop_images/downshift-asset-516125_7d0c2842-61e1-4d46-857b-aab8ab95a0c3.png",
            "link": "shopify://products/downshift-gummy-variety-pack",
            "link_label": "Gummies",
            "colomn_span_amount_desktop": "1"
          }
        }
      },
      "block_order": [
        "custom_liquid_F3wEUz",
        "image_cxrEQL",
        "image_JbB6rR"
      ],
      "custom_css": [
        ".header__menu-item,.mega-menu__link--level-2,.header__icon__text {--font-heading-family: acumin_pro_cond, sans-serif; font-family: var(--font-heading-family); font-style: var(--font-heading-style); font-weight: var(--font-heading-weight); letter-spacing: calc(var(--font-heading-scale) * 0.06rem); line-height: calc(1 + 0.3 / max(1, var(--font-heading-scale))); text-transform: uppercase; font-size: 2.2rem;}",
        ".list-menu--disclosure {width: 23rem;}"
      ],
      "settings": {
        "logo_position": "middle-center",
        "menu": "main-v2",
        "menu_type_desktop": "mega",
        "columns_desktop": 3,
        "image_ratio": "square",
        "sticky_header_type": "on-scroll-up",
        "show_line_separator": false,
        "color_scheme": "scheme-1",
        "menu_color_scheme": "scheme-1",
        "enable_transparent_header_home": true,
        "enable_transparent_header_custom_list": "downshift",
        "transparent_logo": "shopify://shop_images/shift-naturals-logo-4124.png",
        "transparent_text_color": "#d8dbc8",
        "enable_country_selector": false,
        "enable_language_selector": false,
        "enable_customer_avatar": true,
        "show_homepage_logo_h1": true,
        "mobile_logo_position": "center",
        "show_content_in_drawer_menu": false,
        "icons_menu_custom_liquid": "<a id=\"HeaderMenu-shop\" href=\"/pages/find-us\" class=\"small-hide medium-hide header__menu-item list-menu__item link link--text focus-inset\" style=\"padding-right: 2.7rem;\"><span>Find Us</span></a>\n<!--<a href=\"\" class=\"button button-2 button__small large-up-hide\">Buy</a>-->",
        "icons_menu_custom_liquid_after": "",
        "drawer_custom_liquid": "",
        "margin_bottom": 0,
        "padding_top": 8,
        "padding_bottom": 8
      }
    },
    "f7e58296-218f-4627-9e54-ebc249137543": {
      "type": "custom-liquid",
      "settings": {
        "description": "Google Tag Manager",
        "custom_liquid": "<!-- Google Tag Manager -->\n<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\nnew Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\nj=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n})(window,document,'script','dataLayer','GTM-WBF8MG3P');</script>\n<!-- End Google Tag Manager -->",
        "hide_size": "",
        "color_scheme": "",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0
      }
    },
    "custom_liquid_LYgnAe": {
      "type": "custom-liquid",
      "settings": {
        "description": "Microsoft Clarity (for heatmaps and screen recordings)",
        "custom_liquid": "<script type=\"text/javascript\">\n    (function(c,l,a,r,i,t,y){\n        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};\n        t=l.createElement(r);t.async=1;t.src=\"https://www.clarity.ms/tag/\"+i;\n        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);\n    })(window, document, \"clarity\", \"script\", \"o5r3x3ny8r\");\n</script>",
        "hide_size": "",
        "color_scheme": "",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0
      }
    }
  },
  "order": [
    "announcement-bar",
    "header",
    "f7e58296-218f-4627-9e54-ebc249137543",
    "custom_liquid_LYgnAe"
  ]
}
