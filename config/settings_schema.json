[{"name": "theme_info", "theme_name": "Longshore Super Theme", "theme_version": "15.2.0", "theme_author": "Longshore", "theme_documentation_url": "https://longshore.com/", "theme_support_url": "https://longshore.com/"}, {"name": "t:settings_schema.logo.name", "settings": [{"type": "image_picker", "id": "logo", "label": "t:settings_schema.logo.settings.logo_image.label"}, {"type": "range", "id": "logo_width", "min": 50, "max": 300, "step": 10, "default": 100, "unit": "px", "label": "t:settings_schema.logo.settings.logo_width.label"}, {"type": "image_picker", "id": "favicon", "label": "t:settings_schema.logo.settings.favicon.label", "info": "t:settings_schema.logo.settings.favicon.info"}]}, {"name": "t:settings_schema.colors.name", "settings": [{"type": "color_scheme_group", "id": "color_schemes", "definition": [{"type": "color", "id": "background", "label": "t:settings_schema.colors.settings.background.label", "default": "#FFFFFF"}, {"type": "color_background", "id": "background_gradient", "label": "t:settings_schema.colors.settings.background_gradient.label", "info": "t:settings_schema.colors.settings.background_gradient.info"}, {"type": "color", "id": "text", "label": "t:settings_schema.colors.settings.text.label", "default": "#121212"}, {"type": "color", "id": "button", "label": "t:settings_schema.colors.settings.button_background.label", "default": "#121212"}, {"type": "color", "id": "button_label", "label": "t:settings_schema.colors.settings.button_label.label", "default": "#FFFFFF"}, {"type": "color", "id": "secondary_button_label", "label": "t:settings_schema.colors.settings.secondary_button_label.label", "default": "#121212"}, {"type": "color", "id": "shadow", "label": "t:settings_schema.colors.settings.shadow.label", "default": "#121212"}], "role": {"text": "text", "background": {"solid": "background", "gradient": "background_gradient"}, "links": "secondary_button_label", "icons": "text", "primary_button": "button", "on_primary_button": "button_label", "primary_button_border": "button", "secondary_button": "background", "on_secondary_button": "secondary_button_label", "secondary_button_border": "secondary_button_label"}}]}, {"name": "t:settings_schema.typography.name", "settings": [{"type": "header", "content": "Default fonts"}, {"type": "font_picker", "id": "type_header_font", "default": "sans-serif", "label": "Header font"}, {"type": "range", "id": "heading_scale", "min": 100, "max": 150, "step": 5, "unit": "%", "label": "Heading font size scale", "info": "Scales heading fonts and other elements across the site to accommodate (e.g. icons)", "default": 100}, {"type": "font_picker", "id": "type_body_font", "default": "sans-serif", "label": "Body font"}, {"type": "range", "id": "body_scale", "min": 100, "max": 130, "step": 5, "unit": "%", "label": "Body font size scale", "info": "Scales body fonts and other elements across the site to accommodate (e.g. icons)", "default": 100}, {"type": "header", "content": "Add custom fonts?"}, {"type": "liquid", "id": "custom_fonts", "label": "Custom font", "info": "Accepts CSS directly & loads after all other font CSS"}]}, {"name": "t:settings_schema.layout.name", "settings": [{"type": "color_scheme", "id": "theme_color_scheme", "default": "scheme-1", "label": "t:sections.all.colors.label"}, {"type": "range", "id": "page_width", "min": 1000, "max": 1600, "step": 100, "default": 1200, "unit": "px", "label": "t:settings_schema.layout.settings.page_width.label"}, {"type": "range", "id": "spacing_sections", "min": 0, "max": 100, "step": 4, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_sections.label", "default": 0}, {"type": "header", "content": "t:settings_schema.layout.settings.header__grid.content"}, {"type": "paragraph", "content": "t:settings_schema.layout.settings.paragraph__grid.content"}, {"type": "range", "id": "spacing_grid_horizontal_mobile", "min": 0, "max": 40, "step": 1, "default": 4, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_grid_horizontal_mobile.label"}, {"type": "range", "id": "spacing_grid_horizontal", "min": 0, "max": 40, "step": 1, "default": 8, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_grid_horizontal.label"}, {"type": "range", "id": "spacing_grid_vertical_mobile", "min": 0, "max": 40, "step": 1, "default": 4, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_grid_vertical_mobile.label"}, {"type": "range", "id": "spacing_grid_vertical", "min": 0, "max": 40, "step": 1, "default": 8, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_grid_vertical.label"}]}, {"name": "Header", "settings": [{"type": "header", "content": "Menu options"}, {"type": "link_list", "id": "menu_drawer", "label": "Drawer menu", "info": "Optionaly choose a menu for drawer that differs from main header menu."}, {"type": "header", "content": "Icon options"}, {"type": "select", "id": "search_display", "options": [{"value": "icon", "label": "Icon"}, {"value": "text", "label": "Text"}, {"value": "none", "label": "none"}], "default": "icon", "label": "Search display"}, {"type": "select", "id": "account_display", "options": [{"value": "icon", "label": "Icon"}, {"value": "text", "label": "Text"}, {"value": "none", "label": "none"}], "default": "icon", "label": "Account display"}, {"type": "select", "id": "cart_display", "options": [{"value": "icon", "label": "Icon"}, {"value": "text", "label": "Text"}, {"value": "none", "label": "none"}], "default": "icon", "label": "Cart display"}]}, {"name": "t:settings_schema.animations.name", "settings": [{"type": "checkbox", "id": "animations_reveal_on_scroll", "label": "t:settings_schema.animations.settings.animations_reveal_on_scroll.label", "default": false}, {"type": "select", "id": "animations_hover_elements", "options": [{"value": "default", "label": "t:settings_schema.animations.settings.animations_hover_elements.options__1.label"}, {"value": "vertical-lift", "label": "t:settings_schema.animations.settings.animations_hover_elements.options__2.label"}, {"value": "3d-lift", "label": "t:settings_schema.animations.settings.animations_hover_elements.options__3.label"}], "default": "default", "label": "t:settings_schema.animations.settings.animations_hover_elements.label", "info": "t:settings_schema.animations.settings.animations_hover_elements.info"}]}, {"name": "t:settings_schema.buttons.name", "settings": [{"type": "header", "content": ".button:after CSS"}, {"type": "range", "id": "buttons_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "buttons_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 100}, {"type": "range", "id": "buttons_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": ".button:before CSS"}, {"type": "range", "id": "buttons_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "buttons_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "buttons_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "buttons_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}, {"type": "liquid", "id": "button-custom-css", "label": ".button additional CSS", "info": "Inserts directly into theme.liquid in between {% style %} tags. Use only .button or .button pseudo-classes"}, {"type": "header", "content": ".button-1 CSS"}, {"type": "color", "id": "button-1-background-color", "default": "#ffffff", "label": ".button-1 background"}, {"type": "color", "id": "button-1-border-color", "default": "#000000", "label": ".button-1 border color"}, {"type": "color", "id": "button-1-text-color", "default": "#000000", "label": ".button-1 text color"}, {"type": "liquid", "id": "button-1-custom-css", "label": ".button-1 additional CSS", "info": "Inserts directly into theme.liquid in between {% style %} tags. Use only .button-1, .button-1:hover, .button-1:after, .button-1:hover:after, .button-1:before or .button-1:hover:before"}, {"type": "header", "content": ".button-2 CSS"}, {"type": "color", "id": "button-2-background-color", "default": "#ffffff", "label": ".button-2 background"}, {"type": "color", "id": "button-2-border-color", "default": "#000000", "label": ".button-2 border color"}, {"type": "color", "id": "button-2-text-color", "default": "#000000", "label": ".button-2 text color"}, {"type": "liquid", "id": "button-2-custom-css", "label": ".button-2 additional CSS", "info": "Inserts directly into theme.liquid in between {% style %} tags. Use only .button-2, .button-2:hover, .button-2:after, .button-2:hover:after, .button-2:before or .button-2:hover:before"}, {"type": "header", "content": ".button-3 CSS"}, {"type": "color", "id": "button-3-background-color", "default": "#ffffff", "label": ".button-3 background"}, {"type": "color", "id": "button-3-border-color", "default": "#000000", "label": ".button-3 border color"}, {"type": "color", "id": "button-3-text-color", "default": "#000000", "label": ".button-3 text color"}, {"type": "liquid", "id": "button-3-custom-css", "label": ".button-3 additional CSS", "info": "Inserts directly into theme.liquid in between {% style %} tags. Use only .button-3, .button-3:hover, .button-3:after, .button-3:hover:after, .button-3:before or .button-3:hover:before"}, {"type": "header", "content": ".button-4 CSS"}, {"type": "color", "id": "button-4-background-color", "default": "#ffffff", "label": ".button-4 background"}, {"type": "color", "id": "button-4-border-color", "default": "#000000", "label": ".button-4 border color"}, {"type": "color", "id": "button-4-text-color", "default": "#000000", "label": ".button-4 text color"}, {"type": "liquid", "id": "button-4-custom-css", "label": ".button-4 additional CSS", "info": "Inserts directly into theme.liquid in between {% style %} tags. Use only .button-4, .button-4:hover, .button-4:after, .button-4:hover:after, .button-4:before or .button-4:hover:before"}, {"type": "header", "content": ".button-5 CSS"}, {"type": "color", "id": "button-5-background-color", "default": "#ffffff", "label": ".button-5 background"}, {"type": "color", "id": "button-5-border-color", "default": "#000000", "label": ".button-5 border color"}, {"type": "color", "id": "button-5-text-color", "default": "#000000", "label": ".button-5 text color"}, {"type": "liquid", "id": "button-5-custom-css", "label": ".button-5 additional CSS", "info": "Inserts directly into theme.liquid in between {% style %} tags. Use only .button-5, .button-5:hover, .button-5:after, .button-5:hover:after, .button-5:before or .button-5:hover:before"}]}, {"name": "t:settings_schema.variant_pills.name", "settings": [{"type": "paragraph", "content": "t:settings_schema.variant_pills.paragraph"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "variant_pills_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "variant_pills_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 55}, {"type": "range", "id": "variant_pills_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 40}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "variant_pills_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "variant_pills_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "variant_pills_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "variant_pills_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}, {"type": "header", "content": "Color swatches"}, {"type": "paragraph", "content": "Requires a variant metafield with namespace and key set to: bonshore.color_swatch. Applies only to color swatches."}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "variant_color_pills_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "variant_color_pills_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 55}, {"type": "range", "id": "variant_color_pills_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 40}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "variant_color_pills_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "variant_color_pills_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "variant_color_pills_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "variant_color_pills_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.inputs.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "inputs_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "inputs_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 55}, {"type": "range", "id": "inputs_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "range", "id": "inputs_textarea_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius_textarea.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "inputs_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "inputs_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "inputs_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "inputs_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.cards.name", "settings": [{"type": "select", "id": "card_style", "options": [{"value": "standard", "label": "t:settings_schema.cards.settings.style.options__1.label"}, {"value": "card", "label": "t:settings_schema.cards.settings.style.options__2.label"}], "default": "standard", "label": "t:settings_schema.cards.settings.style.label"}, {"type": "range", "id": "card_image_padding", "min": 0, "max": 20, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.image_padding.label", "default": 0}, {"type": "select", "id": "card_text_alignment", "options": [{"value": "left", "label": "t:settings_schema.global.settings.text_alignment.options__1.label"}, {"value": "center", "label": "t:settings_schema.global.settings.text_alignment.options__2.label"}, {"value": "right", "label": "t:settings_schema.global.settings.text_alignment.options__3.label"}], "default": "left", "label": "t:settings_schema.global.settings.text_alignment.label"}, {"type": "color_scheme", "id": "card_color_scheme", "label": "t:sections.all.colors.label", "default": "scheme-2"}, {"type": "select", "id": "card_image_ratio", "options": [{"value": "adapt", "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__1.label"}, {"value": "portrait", "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__2.label"}, {"value": "square", "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__3.label"}], "default": "adapt", "label": "t:sections.main-collection-product-grid.settings.image_ratio.label"}, {"type": "select", "id": "card_image_shape", "options": [{"value": "default", "label": "t:sections.all.image_shape.options__1.label"}, {"value": "arch", "label": "t:sections.all.image_shape.options__2.label"}, {"value": "blob", "label": "t:sections.all.image_shape.options__3.label"}, {"value": "chevronleft", "label": "t:sections.all.image_shape.options__4.label"}, {"value": "chevronright", "label": "t:sections.all.image_shape.options__5.label"}, {"value": "diamond", "label": "t:sections.all.image_shape.options__6.label"}, {"value": "parallelogram", "label": "t:sections.all.image_shape.options__7.label"}, {"value": "round", "label": "t:sections.all.image_shape.options__8.label"}], "default": "default", "label": "t:sections.all.image_shape.label", "info": "t:sections.all.image_shape.info"}, {"type": "select", "id": "card_quick_add_behavior", "options": [{"value": "add", "label": "Add to cart"}, {"value": "link", "label": "Link to product"}], "default": "add", "label": "Quick add behavior"}, {"type": "checkbox", "id": "card_show_secondary_image", "default": false, "label": "t:sections.main-collection-product-grid.settings.show_secondary_image.label"}, {"type": "checkbox", "id": "card_show_vendor", "default": false, "label": "t:sections.main-collection-product-grid.settings.show_vendor.label"}, {"type": "checkbox", "id": "card_show_rating", "default": false, "label": "t:sections.main-collection-product-grid.settings.show_rating.label", "info": "t:sections.main-collection-product-grid.settings.show_rating.info"}, {"type": "checkbox", "id": "card_show_alternative_title", "default": false, "label": "Show alternative title", "info": "Requires a product metafield (type: single line text) with namespace and key set to: custom.alternative_product_title"}, {"type": "checkbox", "id": "card_show_card_product_custom_field", "default": false, "label": "Show custom field", "info": "Requires a product metafield (type: single line text) with namespace and key set to: custom.card_product_custom_field"}, {"type": "checkbox", "id": "card_price_bottom", "default": false, "label": "Push price to bottom"}, {"type": "checkbox", "id": "card_quick_add_hide_mobile", "default": false, "label": "Hide quick add on mobile"}, {"type": "text", "id": "card_heading_font_size", "default": "h5", "label": "Card heading font size", "info": "Options: h1, h2, h3, h4, h5, h6 etc."}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "card_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "card_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "card_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "card_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "card_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "card_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "card_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.collection_cards.name", "settings": [{"type": "select", "id": "collection_card_style", "options": [{"value": "standard", "label": "t:settings_schema.collection_cards.settings.style.options__1.label"}, {"value": "card", "label": "t:settings_schema.collection_cards.settings.style.options__2.label"}], "default": "standard", "label": "t:settings_schema.collection_cards.settings.style.label"}, {"type": "range", "id": "collection_card_image_padding", "min": 0, "max": 20, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.image_padding.label", "default": 0}, {"type": "select", "id": "collection_card_text_alignment", "options": [{"value": "left", "label": "t:settings_schema.global.settings.text_alignment.options__1.label"}, {"value": "center", "label": "t:settings_schema.global.settings.text_alignment.options__2.label"}, {"value": "right", "label": "t:settings_schema.global.settings.text_alignment.options__3.label"}], "default": "left", "label": "t:settings_schema.global.settings.text_alignment.label"}, {"type": "color_scheme", "id": "collection_card_color_scheme", "label": "t:sections.all.colors.label", "default": "scheme-2"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "collection_card_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "collection_card_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "collection_card_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "collection_card_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "collection_card_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "collection_card_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "collection_card_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.blog_cards.name", "settings": [{"type": "select", "id": "blog_card_style", "options": [{"value": "standard", "label": "t:settings_schema.blog_cards.settings.style.options__1.label"}, {"value": "card", "label": "t:settings_schema.blog_cards.settings.style.options__2.label"}], "default": "standard", "label": "t:settings_schema.blog_cards.settings.style.label"}, {"type": "range", "id": "blog_card_image_padding", "min": 0, "max": 20, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.image_padding.label", "default": 0}, {"type": "select", "id": "blog_card_text_alignment", "options": [{"value": "left", "label": "t:settings_schema.global.settings.text_alignment.options__1.label"}, {"value": "center", "label": "t:settings_schema.global.settings.text_alignment.options__2.label"}, {"value": "right", "label": "t:settings_schema.global.settings.text_alignment.options__3.label"}], "default": "left", "label": "t:settings_schema.global.settings.text_alignment.label"}, {"type": "color_scheme", "id": "blog_card_color_scheme", "label": "t:sections.all.colors.label", "default": "scheme-2"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "blog_card_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "blog_card_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "blog_card_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "blog_card_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "blog_card_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "blog_card_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "blog_card_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.content_containers.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "text_boxes_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "text_boxes_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "text_boxes_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "text_boxes_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "text_boxes_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "text_boxes_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "text_boxes_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.media.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "media_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "media_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 5}, {"type": "range", "id": "media_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "media_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "media_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "media_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "media_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.popups.name", "settings": [{"type": "paragraph", "content": "t:settings_schema.popups.paragraph"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "popup_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "popup_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "popup_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "popup_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "popup_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "popup_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "popup_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.drawers.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "drawer_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "drawer_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "drawer_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "drawer_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "drawer_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "drawer_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.badges.name", "settings": [{"type": "select", "id": "badge_position", "options": [{"value": "bottom left", "label": "t:settings_schema.badges.settings.position.options__1.label"}, {"value": "bottom right", "label": "t:settings_schema.badges.settings.position.options__2.label"}, {"value": "top left", "label": "t:settings_schema.badges.settings.position.options__3.label"}, {"value": "top right", "label": "t:settings_schema.badges.settings.position.options__4.label"}], "default": "bottom left", "label": "t:settings_schema.badges.settings.position.label"}, {"type": "range", "id": "badge_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 40}, {"type": "color_scheme", "id": "sale_badge_color_scheme", "label": "t:settings_schema.badges.settings.sale_badge_color_scheme.label", "default": "scheme-5"}, {"type": "color_scheme", "id": "sold_out_badge_color_scheme", "label": "t:settings_schema.badges.settings.sold_out_badge_color_scheme.label", "default": "scheme-3"}, {"type": "select", "id": "sold_out_badge_copy_type", "options": [{"value": "on_sale_copy", "label": "On Sale"}, {"value": "on_sale_save_x", "label": "Save $x"}, {"value": "on_sale_x_off", "label": "x% off"}], "default": "on_sale_copy", "label": "Sold out badge copy"}, {"type": "header", "content": "Custom product card badge", "info": "To show a custom product badge, create a product metafield and name it custom.custom_badge. Custom badge is lower priority than sold out badge and higher priority than sale badge."}, {"type": "color_scheme", "id": "custom_badge_color_scheme", "default": "scheme-3", "label": "Custom badge color scheme"}, {"type": "header", "content": "Product image badges", "info": "Derived for each image alt text after '#' symbol being present."}, {"type": "select", "id": "product_image_badge_position", "options": [{"value": "bottom left", "label": "t:settings_schema.badges.settings.position.options__1.label"}, {"value": "bottom right", "label": "t:settings_schema.badges.settings.position.options__2.label"}, {"value": "top left", "label": "t:settings_schema.badges.settings.position.options__3.label"}, {"value": "top right", "label": "t:settings_schema.badges.settings.position.options__4.label"}], "default": "bottom left", "label": "Position on image"}, {"type": "color_scheme", "id": "product_image_badge_color_scheme", "default": "scheme-5", "label": "Product image badge color scheme"}, {"type": "header", "content": "Blog article tag badge"}, {"type": "color_scheme", "id": "article_tag_badge_color_scheme", "default": "scheme-3", "label": "Article tag badge color scheme"}]}, {"name": "t:settings_schema.brand_information.name", "settings": [{"type": "paragraph", "content": "t:settings_schema.brand_information.settings.paragraph.content"}, {"type": "inline_richtext", "id": "brand_headline", "label": "t:settings_schema.brand_information.settings.brand_headline.label"}, {"type": "richtext", "id": "brand_description", "label": "t:settings_schema.brand_information.settings.brand_description.label"}, {"type": "image_picker", "id": "brand_image", "label": "t:settings_schema.brand_information.settings.brand_image.label"}, {"type": "range", "id": "brand_image_width", "min": 50, "max": 550, "step": 5, "default": 100, "unit": "px", "label": "t:settings_schema.brand_information.settings.brand_image_width.label"}]}, {"name": "t:settings_schema.social-media.name", "settings": [{"type": "header", "content": "t:settings_schema.social-media.settings.header.content"}, {"type": "text", "id": "social_facebook_link", "label": "t:settings_schema.social-media.settings.social_facebook_link.label", "placeholder": "t:settings_schema.social-media.settings.social_facebook_link.info"}, {"type": "text", "id": "social_instagram_link", "label": "t:settings_schema.social-media.settings.social_instagram_link.label", "placeholder": "t:settings_schema.social-media.settings.social_instagram_link.info"}, {"type": "text", "id": "social_youtube_link", "label": "t:settings_schema.social-media.settings.social_youtube_link.label", "placeholder": "t:settings_schema.social-media.settings.social_youtube_link.info"}, {"type": "text", "id": "social_tiktok_link", "label": "t:settings_schema.social-media.settings.social_tiktok_link.label", "placeholder": "t:settings_schema.social-media.settings.social_tiktok_link.info"}, {"type": "text", "id": "social_twitter_link", "label": "t:settings_schema.social-media.settings.social_twitter_link.label", "placeholder": "t:settings_schema.social-media.settings.social_twitter_link.info"}, {"type": "text", "id": "social_snapchat_link", "label": "t:settings_schema.social-media.settings.social_snapchat_link.label", "placeholder": "t:settings_schema.social-media.settings.social_snapchat_link.info"}, {"type": "text", "id": "social_pinterest_link", "label": "t:settings_schema.social-media.settings.social_pinterest_link.label", "placeholder": "t:settings_schema.social-media.settings.social_pinterest_link.info"}, {"type": "text", "id": "social_tumblr_link", "label": "t:settings_schema.social-media.settings.social_tumblr_link.label", "placeholder": "t:settings_schema.social-media.settings.social_tumblr_link.info"}, {"type": "text", "id": "social_vimeo_link", "label": "t:settings_schema.social-media.settings.social_vimeo_link.label", "placeholder": "t:settings_schema.social-media.settings.social_vimeo_link.info"}]}, {"name": "t:settings_schema.search_input.name", "settings": [{"type": "header", "content": "t:settings_schema.search_input.settings.header.content"}, {"type": "checkbox", "id": "predictive_search_enabled", "default": true, "label": "t:settings_schema.search_input.settings.predictive_search_enabled.label"}, {"type": "checkbox", "id": "predictive_search_show_vendor", "default": false, "label": "t:settings_schema.search_input.settings.predictive_search_show_vendor.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_vendor.info"}, {"type": "checkbox", "id": "predictive_search_show_price", "default": false, "label": "t:settings_schema.search_input.settings.predictive_search_show_price.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_price.info"}, {"type": "liquid", "id": "search_liquid", "label": "Search options", "info": "Used for custom search behavior, see [Shopify search docs for more](https://shopify.dev/docs/themes/navigation-search/search#the-search-form)"}, {"type": "liquid", "id": "predictive_search_liquid", "label": "Predictive search options", "info": "This needs to match above for consistancy, see [Shopify search docs for more](https://shopify.dev/docs/api/ajax/reference/predictive-search)"}]}, {"name": "t:settings_schema.currency_format.name", "settings": [{"type": "header", "content": "t:settings_schema.currency_format.settings.content"}, {"type": "paragraph", "content": "t:settings_schema.currency_format.settings.paragraph"}, {"type": "checkbox", "id": "currency_code_enabled", "label": "t:settings_schema.currency_format.settings.currency_code_enabled.label", "default": true}]}, {"name": "t:settings_schema.cart.name", "settings": [{"type": "select", "id": "cart_type", "options": [{"value": "drawer", "label": "t:settings_schema.cart.settings.cart_type.drawer.label"}, {"value": "page", "label": "t:settings_schema.cart.settings.cart_type.page.label"}, {"value": "notification", "label": "t:settings_schema.cart.settings.cart_type.notification.label"}], "default": "notification", "label": "t:settings_schema.cart.settings.cart_type.label"}, {"type": "select", "id": "cart_navigation", "options": [{"value": "cart", "label": "<PERSON><PERSON>"}, {"value": "checkout", "label": "Checkout"}, {"value": "both", "label": "Both"}], "default": "checkout", "label": "Cart navigation", "info": "Not applicable to page cart type (above setting)."}, {"type": "checkbox", "id": "show_vendor", "label": "t:settings_schema.cart.settings.show_vendor.label", "default": false}, {"type": "checkbox", "id": "show_cart_note", "label": "t:settings_schema.cart.settings.show_cart_note.label", "default": false}, {"type": "number", "id": "cart_note_max_length", "label": "Cart note max length (characters)"}, {"type": "select", "id": "cart_button_style", "options": [{"value": "button button--primary", "label": "Solid"}, {"value": "button button--secondary", "label": "Outline"}, {"value": "link underlined-link", "label": "Link"}, {"value": "button button-1", "label": "Custom 1"}, {"value": "button button-2", "label": "Custom 2"}, {"value": "button button-3", "label": "Custom 3"}, {"value": "button button-4", "label": "Custom 4"}, {"value": "button button-5", "label": "Custom 5"}], "default": "button button--secondary", "label": "Cart <PERSON>ton style"}, {"type": "select", "id": "checkout_button_style", "options": [{"value": "button button--primary", "label": "Solid"}, {"value": "button button--secondary", "label": "Outline"}, {"value": "link underlined-link", "label": "Link"}, {"value": "button button-1", "label": "Custom 1"}, {"value": "button button-2", "label": "Custom 2"}, {"value": "button button-3", "label": "Custom 3"}, {"value": "button button-4", "label": "Custom 4"}, {"value": "button button-5", "label": "Custom 5"}], "default": "button button--primary", "label": "Checkout Button style"}, {"type": "header", "content": "Font options"}, {"type": "select", "id": "cart_product_title_font_size", "options": [{"value": "h6", "label": "t:sections.all.heading_size.options__h6.label"}, {"value": "h5", "label": "t:sections.all.heading_size.options__h5.label"}, {"value": "h4", "label": "t:sections.all.heading_size.options__h4.label"}, {"value": "h3", "label": "t:sections.all.heading_size.options__h3.label"}, {"value": "h2", "label": "t:sections.all.heading_size.options__1.label"}, {"value": "h1", "label": "t:sections.all.heading_size.options__2.label"}], "default": "h4", "label": "Product title font size"}, {"type": "header", "content": "t:settings_schema.cart.settings.cart_drawer.header"}, {"type": "checkbox", "id": "cart_drawer_force_scroll", "label": "Scroll entire cart", "default": false, "info": "This is defaul behavior for <650px screens. Feature allows for all screen heights."}, {"type": "liquid", "id": "cart_drawer_liquid_top", "label": "Custom liquid top (fixed)", "info": "Rendered just above cart items (will not show when empty)"}, {"type": "liquid", "id": "cart_drawer_liquid_bottom", "label": "Custom liquid bottom (fixed)", "info": "Rendered just below cart items (will not show when empty)"}, {"type": "liquid", "id": "cart_drawer_items_liquid_top", "label": "Custom liquid item top (scrolls)", "info": "Rendered at top of cart items (will not show when empty)"}, {"type": "liquid", "id": "cart_drawer_items_liquid_bottom", "label": "Custom liquid items bottom (scrolls)", "info": "Rendered at bottom of cart items (will not show when empty)"}, {"type": "collection", "id": "cart_drawer_collection", "label": "t:settings_schema.cart.settings.cart_drawer.collection.label", "info": "t:settings_schema.cart.settings.cart_drawer.collection.info"}, {"type": "color_scheme", "id": "cart_color_scheme", "label": "t:sections.all.colors.label", "default": "scheme-1"}, {"type": "header", "content": "X-sell options"}, {"type": "paragraph", "content": "Use these settings to add x-sell offers on cart drawer."}, {"type": "select", "id": "cart_x_sell_placement", "options": [{"value": "", "label": "<PERSON>de"}, {"value": "footer_items_bottom", "label": "Just below cart items (scrolls)"}, {"value": "footer_top", "label": "Just above cart footer"}, {"value": "footer_bottom", "label": "Just below cart footer"}], "label": "X-sell placement", "default": "footer_top", "info": "In order for feature to work, a valid collection needs to be selected along with a placement set."}, {"type": "inline_richtext", "id": "cart_x_sell_title", "label": "X-sell title", "default": "You Might Also Like"}, {"type": "select", "id": "cart_x_sell_title_size", "options": [{"value": "h6", "label": "t:sections.all.heading_size.options__h6.label"}, {"value": "h5", "label": "t:sections.all.heading_size.options__h5.label"}, {"value": "h4", "label": "t:sections.all.heading_size.options__h4.label"}, {"value": "h3", "label": "t:sections.all.heading_size.options__h3.label"}], "default": "h3", "label": "X-sell title font size"}, {"type": "collection", "id": "cart_x_sell_collection", "label": "X-sell collection"}, {"type": "color_scheme", "id": "cart_x_sell_color_scheme", "label": "t:sections.all.colors.label", "default": "scheme-2"}, {"type": "select", "id": "cart_x_sell_image_ratio", "options": [{"value": "adapt", "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.options.option_3"}, {"value": "portrait", "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.options.option_1"}, {"value": "square", "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.options.option_2"}], "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.label", "default": "portrait"}, {"type": "select", "id": "cart_x_sell_card_heading_font_size", "options": [{"value": "h6", "label": "t:sections.all.heading_size.options__h6.label"}, {"value": "h5", "label": "t:sections.all.heading_size.options__h5.label"}, {"value": "h4", "label": "t:sections.all.heading_size.options__h4.label"}, {"value": "h3", "label": "t:sections.all.heading_size.options__h3.label"}], "default": "h5", "label": "Card heading font size"}, {"type": "select", "id": "cart_x_sell_quick_add_button_style", "options": [{"value": "button button--primary", "label": "Solid"}, {"value": "button button--secondary", "label": "Outline"}, {"value": "link underlined-link", "label": "Link"}, {"value": "button button-1", "label": "Custom 1"}, {"value": "button button-2", "label": "Custom 2"}, {"value": "button button-3", "label": "Custom 3"}, {"value": "button button-4", "label": "Custom 4"}, {"value": "button button-5", "label": "Custom 5"}], "default": "button button--primary", "label": "Quick add button style"}, {"type": "select", "id": "cart_x_sell_card_style", "options": [{"value": "standard", "label": "t:settings_schema.cards.settings.style.options__1.label"}, {"value": "card", "label": "t:settings_schema.cards.settings.style.options__2.label"}], "default": "card", "label": "Card style"}, {"type": "color_scheme", "id": "cart_x_sell_card_color_scheme", "label": "Card color scheme", "default": "scheme-1"}]}, {"name": "Custom CSS", "settings": [{"type": "liquid", "id": "global_custom_css", "label": "CSS overrides", "info": "Inserts directly into layout templates in between {% style %} tags. Last css rendered in </head>."}]}, {"name": "Custom Liquid", "settings": [{"type": "liquid", "id": "global_custom_liquid", "label": "Global custom liquid", "info": "Last line rendered in </head>."}]}]