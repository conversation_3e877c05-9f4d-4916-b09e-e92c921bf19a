/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "rich_text_NVGRyP": {
      "type": "rich-text",
      "blocks": {
        "custom_liquid_8hLyfk": {
          "type": "custom_liquid",
          "settings": {
            "title": "Heading",
            "custom_liquid": "<h1 class=\"h2 tw:mb-0\">DownShift</h3>\n<p class=\"tw:mt-4! body-copy-large tw:max-w-3xl tw:mx-auto\">A familiar mellow buzz that helps you unwind from the grind without feeling hazy or overwhelmed.\n</p>"
          }
        },
        "custom_liquid_b6HPFw": {
          "type": "custom_liquid",
          "settings": {
            "title": "Flavor Options",
            "custom_liquid": "{%- if product.metafields.custom.linked_products != blank -%}\n  <div class=\"fake-variants\">\n    <ul class=\"tw:flex tw:flex-wrap tw:gap-4 tw:justify-center\">\n      {%- for fake_variant in product.metafields.custom.linked_products.value -%}\n        <li\n          data-product-handle=\"{{ fake_variant.handle }}\"\n        >\n          {%- if fake_variant.handle == product.handle -%}\n            <span class=\"h4 tw:md:text-[18px] tw:text-[15px] tw:uppercase tw:font-bold tw:flex tw:px-4 tw:py-2 tw:w-fit tw:rounded-xl tw:border-2 tw:border-[#2B2D20]\" style=\"color: {{ product.metafields.custom.flavor_color | default: '#D7DBC6' }}; background: #2B2D20;\">{{ fake_variant.metafields.custom.linked_products_name }}</span>\n          {%- else -%}\n              <a class=\"h4 tw:md:text-[18px] tw:text-[15px] tw:no-underline tw:uppercasel tw:font-bold tw:flex tw:px-4 tw:py-2 tw:w-fit tw:border-2 tw:rounded-xl tw:border-[#2B2D20]\" href=\"{{ fake_variant.variants[1].url }}\">{{ fake_variant.metafields.custom.linked_products_name }}</a>\n          {%- endif -%}\n        </li>\n      {%- endfor -%}\n    </ul>\n  </div>\n{%- endif -%}"
          }
        }
      },
      "block_order": [
        "custom_liquid_8hLyfk",
        "custom_liquid_b6HPFw"
      ],
      "custom_css": [],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "center",
        "color_scheme": "",
        "padding_top_mobile": 20,
        "padding_top": 10,
        "padding_bottom_mobile": 20,
        "padding_bottom": 40,
        "page": "",
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "main": {
      "type": "main-product",
      "blocks": {
        "custom_liquid_cadKbN": {
          "type": "custom_liquid",
          "disabled": true,
          "settings": {
            "title": "My custom liquid",
            "custom_liquid": "<h2 class=\"h4 tw:uppercase tw:font-bold tw:flex tw:px-3 tw:py-2 tw:w-fit tw:rounded-xl tw:border-2 tw:border-[#2B2D20]\" style=\"color: {{ product.metafields.custom.flavor_color | default: '#D7DBC6' }}; background: #2B2D20;\">{{ product.metafields.custom.linked_products_name }}</h2>",
            "hide_size": "",
            "custom_css": ""
          }
        },
        "description_DpwENb": {
          "type": "description",
          "settings": {
            "hide_size": "",
            "liquid_before": "",
            "liquid_after": "",
            "custom_css": ""
          }
        },
        "custom_liquid_QkUVt9": {
          "type": "custom_liquid",
          "disabled": true,
          "settings": {
            "title": "Flavor options",
            "custom_liquid": "{%- if product.metafields.custom.linked_products != blank -%}\n  <div class=\"fake-variants\">\n    <ul class=\"tw:flex tw:flex-wrap tw:gap-3\">\n      {%- for fake_variant in product.metafields.custom.linked_products.value -%}\n        <li\n          data-product-handle=\"{{ fake_variant.handle }}\"\n        >\n          {%- if fake_variant.handle == product.handle -%}\n            <span class=\"h4 tw:text-[18px] tw:uppercase tw:font-bold tw:flex tw:px-3 tw:py-2 tw:w-fit tw:rounded-xl tw:border-2 tw:border-[#2B2D20]\" style=\"color: {{ product.metafields.custom.flavor_color | default: '#D7DBC6' }}; background: #2B2D20;\">{{ fake_variant.metafields.custom.linked_products_name }}</span>\n          {%- else -%}\n              <a class=\"h4 tw:text-[18px] tw:no-underline tw:uppercasel tw:font-bold tw:flex tw:px-3 tw:py-2 tw:w-fit tw:border-2 tw:rounded-xl tw:border-[#2B2D20]\" href=\"{{ fake_variant.variants[1].url }}\">{{ fake_variant.metafields.custom.linked_products_name }}</a>\n          {%- endif -%}\n        </li>\n      {%- endfor -%}\n    </ul>\n  </div>\n{%- endif -%}",
            "hide_size": "",
            "custom_css": ""
          }
        },
        "fake_variant_picker_EktwnD": {
          "type": "fake_variant_picker",
          "disabled": true,
          "settings": {
            "fake_variants_name": "",
            "show_color_swatches": false,
            "show_image_swatches": false,
            "enable_quick_fake_variant_change": false,
            "hide_size": "",
            "liquid_before": "",
            "liquid_after": "",
            "custom_css": ""
          }
        },
        "variant_picker": {
          "type": "variant_picker",
          "settings": {
            "picker_type": "dropdown",
            "swatch_shape": "circle",
            "hide_single_product_variant_swatches": false,
            "hide_size": "",
            "liquid_before": "",
            "liquid_after": "",
            "custom_css": ""
          }
        },
        "buy_buttons": {
          "type": "buy_buttons",
          "settings": {
            "show_dynamic_checkout": false,
            "custom_liquid_buttons": "",
            "button_style": "button button-2",
            "show_gift_card_recipient": false,
            "hide_size": "",
            "liquid_before": "",
            "liquid_after": "",
            "custom_css": ""
          }
        },
        "text_tUjJtW": {
          "type": "text",
          "settings": {
            "text": "Try a 12 pack and <a href=\"/products/downshift-gummy-variety-pack\" title=\"Downshift Gummy 2-Flavor Variety Pack\">gummies</a>, get free shipping",
            "text_liquid": "",
            "text_style": "body",
            "hide_size": "",
            "liquid_before": "",
            "liquid_after": "",
            "custom_css": ""
          }
        },
        "custom_liquid_TRdWwr": {
          "type": "custom_liquid",
          "settings": {
            "title": "Icons gallery",
            "custom_liquid": "<style>\n  #shopify-section-{{ section.id }} .product__media-wrapper {\n    position: relative;\n  }\n  #shopify-section-{{ section.id }} .product__media-wrapper::after {\n    content: url('{{ images['pdp_icon_made_from_thc.svg'] | image_url: width: 70 }}');\n    position: absolute;\n    top: 8px;\n    right: 8px;\n    z-index: 1;\n  }\n/*\n  #shopify-section-{{ section.id }} .product__media-wrapper::before {\n    content: url('{{ images['10_mg_cbd.svg'] | image_url: width: 70 }}');\n    position: absolute;\n    bottom: 0;\n    left: 7rem;\n    z-index: 1;\n  }\n*/\n  @media screen and (max-width: 749px) {\n    #shopify-section-{{ section.id }} .product__media-wrapper::after {\n      content: none;\n      width: 100px;\n      height: 100px;\n    }\n    #shopify-section-{{ section.id }} .product__media-wrapper::before {\n      content: none;\n      width: 75px;\n      height: 75px;\n    }\n  }\n  </style>",
            "hide_size": "",
            "custom_css": ""
          }
        },
        "b731f803-b1a8-40bd-92d8-d90f0b4ab00a": {
          "type": "popup",
          "settings": {
            "text": "<strong>Supplement Facts</strong>",
            "text_liquid": "",
            "title": "{{ product.title }}",
            "page": "supplement-facts-downshift",
            "content_liquid": "",
            "color_scheme": "scheme-2",
            "hide_size": "",
            "liquid_before": "",
            "liquid_after": "",
            "custom_css": ""
          }
        }
      },
      "block_order": [
        "custom_liquid_cadKbN",
        "description_DpwENb",
        "custom_liquid_QkUVt9",
        "fake_variant_picker_EktwnD",
        "variant_picker",
        "buy_buttons",
        "text_tUjJtW",
        "custom_liquid_TRdWwr",
        "b731f803-b1a8-40bd-92d8-d90f0b4ab00a"
      ],
      "custom_css": [
        ".product-form__input .form__label {text-transform: uppercase; font-weight: 700; font-size: 1.6rem;}",
        ".product-popup-modal__opener {margin-top: -1.5rem;}",
        ".product {align-items: center;}",
        ".fake-variants .form__label {text-transform: uppercase; font-weight: 700; font-size: 1.6rem;}"
      ],
      "settings": {
        "enable_sticky_info": false,
        "color_scheme": "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba",
        "media_size": "small",
        "constrain_to_viewport": false,
        "media_fit": "contain",
        "gallery_layout": "thumbnail_slider",
        "media_position": "left",
        "image_zoom": "none",
        "mobile_thumbnails": "hide",
        "hide_variants": true,
        "enable_video_looping": false,
        "accordion_padding": 0.5,
        "accordion_title_size": "h6",
        "padding_top_mobile": 16,
        "padding_bottom_mobile": 16,
        "padding_top": 36,
        "padding_bottom": 36,
        "custom_liquid_column": "",
        "custom_column_placement": "first",
        "custom_liquid": "<style>\n.shopify_subscriptions_app_policy {\ndisplay: none;\n} \n\n.shopify_subscriptions_app_block_label:has(input:checked) {\ncolor: #D7DBC6;\nbackground: #2B2D20;\n}\n\n.shopify_subscriptions_app_container {\n    max-width: 47.5rem;\n  }\n  .shopify_subscriptions_app__title {\n    text-transform: uppercase;\n    font-weight: 700;\n    font-size: 1.6rem;\n  }\n  .shopify_subscriptions_app_block_label > .shopify_subscriptions_purchase_option_wrapper > label {\n    margin-bottom: 1rem;\n  }\n  .shopify_subscriptions_app_block_label ul {\n    padding-left: 0;\n  }\n  .shopify_subscriptions_app_container input[type=radio] {\n    appearance: none;\n    margin: 0 .5rem 0 0;\n    font: inherit;\n    color: currentColor;\n    width: 1.15em;\n    height: 1.15em;\n    border: 0.15em solid currentColor;\n    border-radius: 50%;\n    transform: translateY(-0.075em);\n    display: inline-grid;\n    place-content: center;\n  }\n  .shopify_subscriptions_app_container input[type=radio]::before {\n    content: \"\";\n    width: 0.65em;\n    height: 0.65em;\n    border-radius: 50%;\n    transform: scale(0);\n    transition: 120ms transform ease-in-out;\n    box-shadow: inset 1em 1em currentColor;\n    background-color: CanvasText;\n  }\n  .shopify_subscriptions_app_container input[type=radio]:checked::before {\n    transform: scale(1);\n  }\n  .shopify_subscriptions_app_block_label_children li:not(:last-of-type) {\n    margin-bottom: 0.7rem;\n  }\n  .shopify_subscriptions_app_block_label_children label:hover,\n  .shopify_subscriptions_purchase_option_wrapper > label:hover {\n    cursor: pointer;\n  }\n  \n\n.shopify_subscriptions_fieldset div {\noverflow: hidden;\n}\n  /* styles for new badges\n  .fake-variants li:not(:first-of-type) .fake-variants__label::after {\n    content: '';\n    width: 30px;\n    height: 30px;\n    background-image: url('https://cdn.shopify.com/s/files/1/0672/2732/0565/files/new_badge_light.svg?v=1714487319');\n    background-size: contain;\n    background-position: center;\n    background-repeat: no-repeat;\n    position: absolute;\n    top: -1.5rem;\n    right: -0.5rem;\n  }\n  .fake-variants li:not(:first-of-type) a.fake-variants__label::after {\n    background-image: url('https://cdn.shopify.com/s/files/1/0672/2732/0565/files/new_badge_dark.svg?v=1714487319');\n  }\n*/\n\n  /* fake swatch mobile adjustments */\n  @media screen and (max-width: 749px) {\n    .fake-variants .fake-variants__label {\n      padding: 1rem 1.2rem;\n    }\n  }\nbody .fake-variants .fake-variants__label {\noverflow: visible;\n}\n.product__media-wrapper .media {\n  background: #F7F7F3;\nborder-radius: 10px;\n}\n</style>\n{% comment %}\n<script>\n  window.addEventListener('DOMContentLoaded', () => {\n    let downShiftProd = document.querySelector('variant-radios[data-url=\"/products/downshift\"]');\n    \n    if (downShiftProd) {\n      let locationHref = location.href;\n      let radioVariant = downShiftProd.querySelectorAll('input[name=\"Quantity\"]');\n      \n      if (radioVariant && radioVariant.length > 1 && locationHref.indexOf('?variant=') < 0) {\n        radioVariant[1].click();\n      }\n    }\n  });\n\ndocument.addEventListener('DOMContentLoaded', function() {\n  // Select all the label elements with the radio buttons using a unique data attribute\n  var labels = document.querySelectorAll('label input[data-radio-type=\"selling_plan\"]');\n\n  // Loop through each label\n  labels.forEach(function(labelInput) {\n    // Get the parent label element\n    var labelElement = labelInput.parentElement;\n\n    // Get the label's child text nodes only, excluding the input element\n    var labelText = Array.from(labelElement.childNodes).find(node => node.nodeType === Node.TEXT_NODE);\n\n    if (labelText) {\n      // Use a regular expression to remove \", 10% off\"\n      labelText.textContent = labelText.textContent.replace(/,\\s*\\d+%\\s*off/, '');\n    }\n  });\n});\n</script>\n{% endcomment %}",
        "custom_css_class": "",
        "product__info_custom_css_class": ""
      }
    },
    "17504355728a71a977": {
      "type": "apps",
      "blocks": {
        "aftersell_by_rokt_pdp_upsells_CBd6gf": {
          "type": "shopify://apps/aftersell-by-rokt/blocks/pdp-upsells/509ec35d-0632-4dfc-a105-fa799cd9d149",
          "settings": {}
        }
      },
      "block_order": [
        "aftersell_by_rokt_pdp_upsells_CBd6gf"
      ],
      "settings": {
        "include_margins": true,
        "hide_size": "",
        "color_scheme": "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "longshore_multicolumn_78yif8": {
      "type": "longshore-multicolumn",
      "blocks": {
        "column_RmLnnk": {
          "type": "_column",
          "settings": {
            "custom_color_scheme": true,
            "color_scheme": "scheme-2",
            "column_span_amount": "grid__item--span-1",
            "column_height_mobile": "tw:max-sm:self-stretch",
            "column_gap_mobile": true,
            "column_span_amount_desktop": "grid__item--span-1-tablet",
            "column_height_desktop": "tw:md:self-stretch",
            "column_gap_desktop": true,
            "custom_classes": "tw:overflow-hidden"
          },
          "blocks": {
            "longshore_image_cxLLVF": {
              "type": "longshore-image",
              "settings": {
                "mobile_image": "shopify://shop_images/shift-naturals-asset-5551212.jpg",
                "desktop_image": "shopify://shop_images/shift-naturals-asset-5551212.jpg",
                "add_content": "",
                "add_content_classes": "tw:absolute tw:inset-0 tw:flex tw:justify-center tw:items-center",
                "mobile_image_wrapper_css": "",
                "desktop_image_wrapper_css": "",
                "custom_classes": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "longshore_image_cxLLVF"
          ]
        },
        "column_CtLrYP": {
          "type": "_column",
          "settings": {
            "custom_color_scheme": true,
            "color_scheme": "scheme-2",
            "column_span_amount": "grid__item--span-1",
            "column_height_mobile": "tw:max-sm:self-stretch",
            "column_gap_mobile": true,
            "column_span_amount_desktop": "grid__item--span-1-tablet",
            "column_height_desktop": "tw:md:self-stretch",
            "column_gap_desktop": true,
            "custom_classes": "tw:overflow-hidden"
          },
          "blocks": {
            "longshore_image_YFBfYF": {
              "type": "longshore-image",
              "settings": {
                "mobile_image": "shopify://shop_images/shift-naturals-asset-5591219.jpg",
                "desktop_image": "shopify://shop_images/shift-naturals-asset-5591219.jpg",
                "add_content": "",
                "add_content_classes": "tw:absolute tw:inset-0 tw:flex tw:justify-center tw:items-center",
                "mobile_image_wrapper_css": "",
                "desktop_image_wrapper_css": "",
                "custom_classes": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "longshore_image_YFBfYF"
          ]
        },
        "column_6hbtTV": {
          "type": "_column",
          "settings": {
            "custom_color_scheme": true,
            "color_scheme": "scheme-2",
            "column_span_amount": "grid__item--span-1",
            "column_height_mobile": "tw:max-sm:self-stretch",
            "column_gap_mobile": true,
            "column_span_amount_desktop": "grid__item--span-1-tablet",
            "column_height_desktop": "tw:md:self-stretch",
            "column_gap_desktop": true,
            "custom_classes": "tw:overflow-hidden"
          },
          "blocks": {
            "longshore_image_RtreYj": {
              "type": "longshore-image",
              "settings": {
                "mobile_image": "shopify://shop_images/Camp_Vibes.jpg",
                "desktop_image": "shopify://shop_images/Camp_Vibes.jpg",
                "add_content": "",
                "add_content_classes": "tw:absolute tw:inset-0 tw:flex tw:justify-center tw:items-center",
                "mobile_image_wrapper_css": "",
                "desktop_image_wrapper_css": "",
                "custom_classes": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "longshore_image_RtreYj"
          ]
        },
        "column_qPWXmG": {
          "type": "_column",
          "settings": {
            "custom_color_scheme": false,
            "color_scheme": "",
            "column_span_amount": "grid__item--span-1",
            "column_height_mobile": "tw:max-sm:self-stretch",
            "column_gap_mobile": true,
            "column_span_amount_desktop": "grid__item--span-3-tablet",
            "column_height_desktop": "tw:md:self-stretch",
            "column_gap_desktop": true,
            "custom_classes": ""
          },
          "blocks": {
            "longshore_flexbox_UH7YUi": {
              "type": "longshore-flexbox",
              "settings": {
                "mobile_justify": "tw:justify-start",
                "mobile_align": "tw:items-start",
                "mobile_text_align": "tw:text-left",
                "mobile_gap": "tw:gap-[4px]",
                "mobile_padding": "tw:p-[8px]",
                "desktop_justify": "tw:md:justify-start",
                "desktop_align": "tw:md:items-center",
                "desktop_text_align": "tw:md:text-center",
                "desktop_gap": "tw:md:gap-0",
                "desktop_padding": "tw:md:p-[16px]",
                "custom_classes": ""
              },
              "blocks": {
                "longshore_text_hHE4HP": {
                  "type": "longshore-text",
                  "settings": {
                    "text": "<p>DownShift is a terpene-infused sparkling water - so it alters your mood, but in an all-natural and healthy way. It’s your go-to beverage for stress-free moments and mindful refreshment.</p>",
                    "mobile_max_width": "tw:max-w-full",
                    "desktop_max_width": "tw:md:max-w-4xl",
                    "custom_css": ""
                  },
                  "blocks": {}
                },
                "longshore_buttons_NwUEi8": {
                  "type": "longshore-buttons",
                  "settings": {
                    "button_1_text": "Learn more",
                    "button_1_url": "shopify://pages/downshift-info",
                    "button_1_style": "button button--primary",
                    "button_1_css": "",
                    "button_1_attributes": "",
                    "button_2_text": "",
                    "button_2_url": "",
                    "button_2_style": "button button--primary",
                    "button_2_css": "",
                    "button_2_attributes": "",
                    "custom_css": ""
                  },
                  "blocks": {}
                }
              },
              "block_order": [
                "longshore_text_hHE4HP",
                "longshore_buttons_NwUEi8"
              ]
            }
          },
          "block_order": [
            "longshore_flexbox_UH7YUi"
          ]
        }
      },
      "block_order": [
        "column_RmLnnk",
        "column_CtLrYP",
        "column_6hbtTV",
        "column_qPWXmG"
      ],
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba",
        "color_scheme_slider": "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba",
        "mobile_page_width": true,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "mobile_slider_peek": false,
        "padding_top_mobile": 24,
        "padding_bottom_mobile": 48,
        "desktop_page_width": true,
        "section_width": "longshore-multicolumn-full",
        "columns_desktop": 3,
        "swipe_on_desktop": false,
        "desktop_slider_peek": false,
        "padding_top_desktop": 40,
        "padding_bottom_desktop": 40,
        "parent_div_custom_css": "",
        "slider_component_custom_css": "",
        "ul_custom_css": "",
        "slider_controls_css": "",
        "custom_liquid_before": "<div class=\"page-width tw:mb-6 small-hide\">\n<h2>Unwind from the grind</h2>\n</div>",
        "custom_liquid_after": ""
      }
    },
    "rich_text_kVedB9": {
      "type": "rich-text",
      "blocks": {
        "custom_liquid_z8YdCm": {
          "type": "custom_liquid",
          "settings": {
            "title": "Title",
            "custom_liquid": "<h2 style=\"color:#98DE59;\">The transparent deets</h2>"
          }
        }
      },
      "block_order": [
        "custom_liquid_z8YdCm"
      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "center",
        "color_scheme": "scheme-2",
        "padding_top_mobile": 40,
        "padding_top": 80,
        "padding_bottom_mobile": 0,
        "padding_bottom": 40,
        "page": "",
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "collapsible_content_gQQFqk": {
      "type": "collapsible-content",
      "blocks": {
        "collapsible_row_T8KAMd": {
          "type": "collapsible_row",
          "settings": {
            "heading": "Why should I try downshift?",
            "icon": "none",
            "row_content": "<p>DownShift is a terpene-infused sparkling water - so it alters your mood, but in an all-natural and healthy way. It’s your go-to beverage for stress-free moments and mindful refreshment. We wanted to create a product that wouldn’t get you “too high”, have a hangover, or ruin your sleep.</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "collapsible_row_nCEKUx": {
          "type": "collapsible_row",
          "settings": {
            "heading": "How does DownShift Work?",
            "icon": "none",
            "row_content": "<p>So in the human body there is something called the endocannabinoid system (ECS for short). Imagine the ECS as a network of tiny communication lines spreading throughout your body, especially in your brain and nervous system.</p><p>This system works with special messengers called endocannabinoids, which are like your body's homemade chill pills. They zip around your body, hooking up with specific receptors – think of these as tiny docking stations – named CB1 and CB2. CB1 is mainly hanging out in your brain and nerves, while CB2 is chilling in other parts of your body.</p><p>When you enjoy DownShift, you're introducing compounds similar to your body's endocannabinoids. They mingle with these CB1 and CB2 receptors, kind of like a key fitting into a lock, helping to regulate all sorts of things – from how you feel pain, to your appetite, mood, and even memory.</p><p>But wait, <span style=\"text-decoration:underline\"><strong>there's more</strong></span>! These little interactions also play a part in learning, motor skills, and how you handle stress. It's a bit like having a personal assistant in your body, helping to balance things out and keep you feeling good.</p><p>So, when you take a sip of DownShift, you're giving this system a little nudge, helping your body find its zen and keeping you relaxed and balanced. It's nature <em>and science</em> coming together for a chill you can feel!\"</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "collapsible_row_XUiMwz": {
          "type": "collapsible_row",
          "settings": {
            "heading": "What effects will I feel?",
            "icon": "none",
            "row_content": "<p>Our Founders wanted to create the ideal alcohol alternative for any adult social setting. You feel mellow’d out - but not high. We wanted a similar “buzz” as a beer or cocktail… but no hangover. </p><p>We also still wanted to be conversational and talkative - something that high-dose THC products impinge on (you get all “heady” and start thinking in loops).</p><p>Our hemp-infused drinks are designed to provide relaxation and wellness benefits without the intoxicating effects of alcohol and without feeling like you took a bong rip. It’s the perfect amount of chill. A great way to “unwind from the grind” (see what I did there? #tagline)</p><p>DownShift is designed as your “day-ender” at a summer barbeque with friends, or an afternoon on the winter slopes - or simply a hectic day in the office!</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "collapsible_row_mGMikg": {
          "type": "collapsible_row",
          "settings": {
            "heading": "How long does it last?",
            "icon": "none",
            "row_content": "<p>Every experience is unique to each individual, but usually you’ll feel the effects for a couple hours. It’s similar to drinking a beer - our founders notice the effects in 10-15 minutes - reaching a relaxation peak in about 45 minutes, ensuring a smooth period of unwinding.</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "collapsible_row_MXyKX7": {
          "type": "collapsible_row",
          "settings": {
            "heading": "What are Terpenes?",
            "icon": "none",
            "row_content": "<p>So glad you asked! We looooove nerding out on this stuff - so much so we created an entire section of our website to explore these amazing all-natural plant molecules!<a href=\"https://enjoyshift.com/pages/discover-our-terpenes\"><span style=\"text-decoration:underline\"> Check out our Terpene page</span></a>and tumble down the rabbit hole of natural sciences with us!</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "collapsible_row_B9WEqJ": {
          "type": "collapsible_row",
          "settings": {
            "heading": "What Terpenes are added?",
            "icon": "none",
            "row_content": "<p>DownShift is enriched with a proprietary blend of full-spectrum terpenes. What does this mean? Terpenes are magical in nature because it’s all about how they work together to create a balanced effect. Yo-yo Ma is an amazing cellist… but you can’t compare him to a full symphony playing Bethoven. Our “proprietary blend of full-spectrum terpenes” is the symphony… these terpenes create a full well-rounded experience - the way mother nature intended. Of this full-spectrum blend, we like to call attention to a few of the terpenes that are doing the heavy lifting; like:</p><p>Limonene is a citrus zest, shown to add an uplifting feeling</p><p>Pinene helps in reducing stress</p><p>Beta-Caryophyllene is effective in relieving anxiety</p><p>These three, together with many other supporting terpenes, create a symphony of tranquility in every can.</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "collapsible_row_xK8TNq": {
          "type": "collapsible_row",
          "settings": {
            "heading": "Is it legal throughout the USA?",
            "icon": "none",
            "row_content": "<p>Absolutely! Our products adhere to the 2018 Farm Bill regulations, ensuring they are both legal and safe for consumption. At DownShift, we prioritize the legality and quality of our products for our customers' peace of mind.</p><p>Basically, the 2018 Farm Bill (Federal Legislation) made it ok to create products with a small amount of CBD and THC - so long as it all comes from Hemp, and in tiny amounts (the figure you’ll see is: “0.3% by dry-weight volume”)</p>",
            "row_content_liquid": "",
            "page": ""
          }
        }
      },
      "block_order": [
        "collapsible_row_T8KAMd",
        "collapsible_row_nCEKUx",
        "collapsible_row_XUiMwz",
        "collapsible_row_mGMikg",
        "collapsible_row_MXyKX7",
        "collapsible_row_B9WEqJ",
        "collapsible_row_xK8TNq"
      ],
      "settings": {
        "caption": "",
        "heading": "",
        "heading_size": "h1",
        "heading_alignment": "center",
        "layout": "none",
        "container_color_scheme": "",
        "color_scheme": "scheme-2",
        "open_first_collapsible_row": true,
        "image_ratio": "adapt",
        "desktop_layout": "image_second",
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0
      }
    },
    "rich_text_XUDAU4": {
      "type": "rich-text",
      "blocks": {
        "button_KarcNN": {
          "type": "button",
          "settings": {
            "button_label": "Learn More",
            "button_link": "shopify://pages/downshift-info",
            "button_style": "button button--primary",
            "button_label_2": "",
            "button_link_2": "",
            "button_style_2": "button button--primary",
            "button_label_liquid": "",
            "button_label_2_liquid": ""
          }
        }
      },
      "block_order": [
        "button_KarcNN"
      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "center",
        "color_scheme": "scheme-2",
        "padding_top_mobile": 20,
        "padding_top": 20,
        "padding_bottom_mobile": 40,
        "padding_bottom": 80,
        "page": "",
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width page-width--narrow",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "left",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "longshore_multicolumn_rdXGNr": {
      "type": "longshore-multicolumn",
      "blocks": {
        "column_QcbkVA": {
          "type": "_column",
          "settings": {
            "custom_color_scheme": false,
            "color_scheme": "scheme-1",
            "column_span_amount": "grid__item--span-1",
            "column_height_mobile": "tw:max-sm:self-stretch",
            "column_gap_mobile": true,
            "column_span_amount_desktop": "grid__item--span-1-tablet",
            "column_height_desktop": "tw:md:self-stretch",
            "column_gap_desktop": true,
            "custom_classes": ""
          },
          "blocks": {
            "custom_product_box_p6pHhe": {
              "type": "custom-product-box",
              "settings": {
                "color_scheme": "scheme-2",
                "link": "shopify://products/downshift-gummy-variety-pack",
                "title_line_1": "Gummies",
                "title_line_2": "",
                "subtitle": "",
                "text": "<p>A delicious way to take the edge off without hitting the brakes, DownShift gummies help you mellow out, stay present, and enjoy your downtime.</p>",
                "image": "shopify://shop_images/downshift-asset-516125_7d0c2842-61e1-4d46-857b-aab8ab95a0c3.png",
                "custom_css": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "custom_product_box_p6pHhe"
          ]
        },
        "column_dyMQrD": {
          "type": "_column",
          "settings": {
            "custom_color_scheme": false,
            "color_scheme": "",
            "column_span_amount": "grid__item--span-1",
            "column_height_mobile": "tw:max-sm:self-stretch",
            "column_gap_mobile": true,
            "column_span_amount_desktop": "grid__item--span-1-tablet",
            "column_height_desktop": "tw:md:self-stretch",
            "column_gap_desktop": true,
            "custom_classes": ""
          },
          "blocks": {
            "custom_product_box_P6hJik": {
              "type": "custom-product-box",
              "settings": {
                "color_scheme": "",
                "link": "shopify://products/shift-variety-pack",
                "title_line_1": "Cocktails",
                "title_line_2": "",
                "subtitle": "",
                "text": "<p>Shift Cocktails deliver bold flavors with 12mg CBD + 4mg THC. Zero alcohol, no hangover—just smooth, euphoric vibes in every sip. Unwind from the grind.</p>",
                "image": "shopify://shop_images/shift-asset-4123411.png",
                "custom_css": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "custom_product_box_P6hJik"
          ]
        }
      },
      "block_order": [
        "column_QcbkVA",
        "column_dyMQrD"
      ],
      "disabled": true,
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba",
        "color_scheme_slider": "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba",
        "mobile_page_width": true,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "mobile_slider_peek": false,
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 36,
        "desktop_page_width": true,
        "section_width": "longshore-multicolumn-full",
        "columns_desktop": 2,
        "swipe_on_desktop": false,
        "desktop_slider_peek": false,
        "padding_top_desktop": 80,
        "padding_bottom_desktop": 80,
        "parent_div_custom_css": "",
        "slider_component_custom_css": "",
        "ul_custom_css": "",
        "slider_controls_css": "",
        "custom_liquid_before": "<div class=\"page-width tw:mb-6\">\n<h5>Sometimes we all need a little more</h5>\n<h2>You might also like</h2>\n</div>",
        "custom_liquid_after": ""
      }
    },
    "custom_liquid_Qbi4zH": {
      "type": "custom-liquid",
      "settings": {
        "description": "",
        "custom_liquid": "<div class=\"page-width color-scheme-9\">\n<hr style=\"padding: 0; margin: 0;\">\n</div>",
        "hide_size": "",
        "color_scheme": "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0
      }
    },
    "longshore_multicolumn_BUwzGH": {
      "type": "longshore-multicolumn",
      "blocks": {
        "column_dXzqaX": {
          "type": "_column",
          "settings": {
            "custom_color_scheme": false,
            "color_scheme": "",
            "column_span_amount": "grid__item--span-1",
            "column_height_mobile": "tw:max-sm:self-stretch",
            "column_gap_mobile": true,
            "column_span_amount_desktop": "grid__item--span-1-tablet",
            "column_height_desktop": "tw:md:self-stretch",
            "column_gap_desktop": true,
            "custom_classes": ""
          },
          "blocks": {
            "longshore_liquid_zzD9cH": {
              "type": "longshore-liquid",
              "settings": {
                "liquid": "<div class=\"color-scheme-2 tw:p-6 tw:rounded-3xl\">\n<a href=\"/products/downshift-gummy-variety-pack\" class=\"tw:no-underline\">\n<h3>DownShift Gummies</h3>\n<h6>For wherever life takes you.</h6>\n</a>\n<div class=\" tw:grid tw:grid-cols-3 tw:items-end tw:rounded-xl\">\n<div class=\"color-scheme-2 tw:col-span-2\">\n\n<p>A delicious way to take the edge off without hitting the brakes, DownShift gummies help you mellow out, stay present, and enjoy your downtime. <a href=\"/products/downshift-gummy-variety-pack\">Learn more</a></p>\n</div>\n<img src=\"https://cdn.shopify.com/s/files/1/0672/2732/0565/files/downshift-asset-516125.png?v=1740496999\" class=\"tw:w-full\">\n</div>",
                "custom_css": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "longshore_liquid_zzD9cH"
          ]
        }
      },
      "block_order": [
        "column_dXzqaX"
      ],
      "disabled": true,
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-1",
        "color_scheme_slider": "scheme-1",
        "mobile_page_width": true,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "mobile_slider_peek": false,
        "padding_top_mobile": 40,
        "padding_bottom_mobile": 40,
        "desktop_page_width": true,
        "section_width": "longshore-multicolumn-full",
        "columns_desktop": 2,
        "swipe_on_desktop": false,
        "desktop_slider_peek": false,
        "padding_top_desktop": 48,
        "padding_bottom_desktop": 48,
        "parent_div_custom_css": "",
        "slider_component_custom_css": "",
        "ul_custom_css": "",
        "slider_controls_css": "",
        "custom_liquid_before": "<div class=\"page-width tw:mb-6\">\n<h3>You might also like</h3>\n</div>",
        "custom_liquid_after": ""
      }
    },
    "related-products": {
      "type": "related-products",
      "disabled": true,
      "settings": {
        "hide_size": "",
        "color_scheme": "scheme-2",
        "heading": "You may also like",
        "heading_liquid": "",
        "heading_size": "h2",
        "columns_mobile": "2",
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 36,
        "columns_desktop": 4,
        "padding_top_desktop": 36,
        "padding_bottom_desktop": 36,
        "products_to_show": 4,
        "recommendation_type": "related",
        "recommendation_product_list": [],
        "recommendation_collection": "",
        "quick_add": "none",
        "quick_add_button_style": "button button--primary",
        "image_ratio": "",
        "image_shape": "",
        "show_secondary_image": "",
        "card_heading_font_size": "",
        "show_vendor": "",
        "show_rating": "",
        "quick_add_behavior": "",
        "show_alternative_title": "",
        "show_card_product_custom_field": ""
      }
    },
    "1728598940edd596e6": {
      "type": "apps",
      "blocks": {
        "junip_product_reviews_ugc_junip_product_review_NeQ3ER": {
          "type": "shopify://apps/junip/blocks/junip-product-review/dc14f5a8-ed15-41b1-ad08-cfba23f9789b",
          "settings": {
            "product": "{{product}}"
          }
        }
      },
      "block_order": [
        "junip_product_reviews_ugc_junip_product_review_NeQ3ER"
      ],
      "settings": {
        "include_margins": true,
        "hide_size": "",
        "color_scheme": "scheme-1",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 25,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 25,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    }
  },
  "order": [
    "rich_text_NVGRyP",
    "main",
    "17504355728a71a977",
    "longshore_multicolumn_78yif8",
    "rich_text_kVedB9",
    "collapsible_content_gQQFqk",
    "rich_text_XUDAU4",
    "longshore_multicolumn_rdXGNr",
    "custom_liquid_Qbi4zH",
    "longshore_multicolumn_BUwzGH",
    "related-products",
    "1728598940edd596e6"
  ]
}
