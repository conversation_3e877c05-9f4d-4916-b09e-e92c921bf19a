/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "custom_css": [

      ],
      "settings": {
        "heading_size": "h0",
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 36,
        "padding_top_desktop": 56,
        "padding_bottom_desktop": 56
      }
    },
    "d20e60d7-bda1-4955-8221-3b8b469283b3": {
      "type": "image-with-text",
      "blocks": {
        "f9006b4d-10cb-47bc-8d2b-3022d9e1febf": {
          "type": "image_placement_mobile",
          "disabled": true,
          "settings": {
          }
        },
        "template--17002874732789__d20e60d7-bda1-4955-8221-3b8b469283b3-heading-1": {
          "type": "heading",
          "settings": {
            "heading": "{{ page.metafields.custom.terpene.value.title.value }}",
            "heading_liquid": "",
            "heading_size": "h2"
          }
        },
        "c51eab2c-fa98-4ae7-8427-00f6d825683c": {
          "type": "custom_liquid",
          "settings": {
            "title": "marketing_subheaders",
            "custom_liquid": "{%- if page.metafields.custom.terpene.value.marketing_subheaders != blank %}\n<div class=\"bon-product-detail-list bon-product-detail-list__light-borders\">\n  <span class=\"h6\">{{ page.metafields.custom.terpene.value.marketing_subheaders.value | first }}<\/span>\n  <span style=\"padding: 0 2rem;\">\n    {{ images['sparkle_icon_f114c9bd-7984-48e2-9bce-5541f0b0312e.svg'] | image_url: width: 15 | image_tag }}\n  <\/span>\n  <span class=\"h6\" style=\"text-align: right;\">{{ page.metafields.custom.terpene.value.marketing_subheaders.value | last }}<\/span>\n<\/div>\n{%- endif -%}"
          }
        },
        "template--17002874732789__d20e60d7-bda1-4955-8221-3b8b469283b3-text-1": {
          "type": "text",
          "settings": {
            "text": "{{ page.metafields.custom.terpene.value.description | metafield_tag }}",
            "text_style": "body"
          }
        },
        "b01375ee-2577-4db7-908f-4319dcdad4f4": {
          "type": "custom_liquid",
          "settings": {
            "title": "scientific_image",
            "custom_liquid": "<div style=\"margin-top: 3rem;\">{{ page.metafields.custom.terpene.value.scientific_image.value | image_url: width: 115 | image_tag }}<\/div>"
          }
        },
        "43bf709f-0625-417a-975d-dfa82a1fae2b": {
          "type": "text",
          "settings": {
            "text": "<p><em><strong>{{ page.metafields.custom.terpene.value.scientific_name.value }}<\/strong><\/em><\/p>",
            "text_style": "body"
          }
        },
        "d2ca297f-bcc1-4487-897d-996dbbe627e1": {
          "type": "caption",
          "settings": {
            "caption": "",
            "caption_liquid": "<div class=\"subtitle--small\" style=\"margin-top: -1.5rem;\">{{ page.metafields.custom.terpene.value.commonly_found_in.value }}<\/div>",
            "text_style": "subtitle",
            "text_size": "small"
          }
        },
        "ba9724c1-8205-4b4e-aad2-3172dbe6b9b8": {
          "type": "custom_liquid",
          "settings": {
            "title": "CSS customizations",
            "custom_liquid": "<style>\n@media screen and (min-width: 990px) {\n#shopify-section-{{ section.id }} .image-with-text__content {\n  padding: 11rem 7rem;\n}\n}\n<\/style>"
          }
        }
      },
      "block_order": [
        "f9006b4d-10cb-47bc-8d2b-3022d9e1febf",
        "template--17002874732789__d20e60d7-bda1-4955-8221-3b8b469283b3-heading-1",
        "c51eab2c-fa98-4ae7-8427-00f6d825683c",
        "template--17002874732789__d20e60d7-bda1-4955-8221-3b8b469283b3-text-1",
        "b01375ee-2577-4db7-908f-4319dcdad4f4",
        "43bf709f-0625-417a-975d-dfa82a1fae2b",
        "d2ca297f-bcc1-4487-897d-996dbbe627e1",
        "ba9724c1-8205-4b4e-aad2-3172dbe6b9b8"
      ],
      "custom_css": [
        "ul {list-style: none; padding-left: 0; margin: 2.5rem 0; display: flex; flex-direction: column; gap: 1rem;}",
        "ul > li > strong {margin-right: 1rem;}",
        "strong {text-transform: uppercase;}"
      ],
      "settings": {
        "image": "{{ page.metafields.custom.terpene.value.image.value }}",
        "show_video_controls": false,
        "image_contain": false,
        "height": "large",
        "show_video_mobile_controls": false,
        "image_mobile_contain": false,
        "height_mobile": "small",
        "desktop_image_width": "medium",
        "layout": "image_first",
        "desktop_content_position": "middle",
        "desktop_content_alignment": "left",
        "content_layout": "no-overlap",
        "color_scheme_content": "scheme-1",
        "image_behavior": "none",
        "mobile_content_alignment": "left",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "hide_size": "",
        "color_scheme_section": "scheme-1",
        "content_width": "full-width",
        "content_position": "full-width",
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "content_padding_mobile": 1.5,
        "content_padding_desktop": 4,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "32525b6e-7fd9-40f6-8b93-43097da0b7f0": {
      "type": "rich-text",
      "blocks": {
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-heading-1": {
          "type": "heading",
          "settings": {
            "heading": "DISCOVER TERPENE KNOWLEDGE",
            "heading_size": "h3",
            "text_indent": ""
          }
        },
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-text-1": {
          "type": "text",
          "settings": {
            "text": "<p>Dig into some of the science of Shift. Terpene research shows the incredible benefits terpenes have on our body.<\/p>",
            "text_indent": ""
          }
        }
      },
      "block_order": [
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-heading-1",
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-text-1"
      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "left",
        "color_scheme": "scheme-5",
        "padding_top_mobile": 65,
        "padding_top": 75,
        "padding_bottom_mobile": 30,
        "padding_bottom": 10,
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "scheme-5",
        "content_width": "page-width",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "left",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": "<style>\n@media screen and (min-width: 750px) {\n#shopify-section-{{ section.id }} .rich-text__blocks {\n  display: grid;\n  grid-template-columns: 50% auto;\n  align-items: top;\n  justify-items: flex-start;\n}\n#shopify-section-{{ section.id }} .rich-text__blocks .rich-text__text {\n  margin-top: .5rem;\n}}\n<\/style>"
      }
    },
    "5d3a9773-a400-42cb-bc37-8e4fc09c0f14": {
      "type": "multicolumn",
      "blocks": {
        "template--16993423655157__5d3a9773-a400-42cb-bc37-8e4fc09c0f14-column-1": {
          "type": "column",
          "source": "{{ page.metafields.custom.terpene.value.article_1.value }}",
          "settings": {
            "image": "{{ page.metafields.custom.terpene.value.article_1.value.image.value }}",
            "title": "",
            "title_liquid": "<div class=\"subtitle--small\">\n{{ page.metafields.custom.terpene.value.article_1.value.subtitle.value }}\n<\/div>\n{{ page.metafields.custom.terpene.value.article_1.value.title.value }}",
            "text": "",
            "text_liquid": "",
            "link_label": "Read Article",
            "link_label_liquid": "",
            "link": "{{ page.metafields.custom.terpene.value.article_1.value.link.value }}",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "09442142-346d-4c66-bb3c-94f3ab2a7850": {
          "type": "column",
          "source": "{{ page.metafields.custom.terpene.value.article_2.value }}",
          "settings": {
            "image": "{{ page.metafields.custom.terpene.value.article_2.value.image.value }}",
            "title": "{{ page.metafields.custom.terpene.value.article_2.value.subtitle.value }}",
            "title_liquid": "<div class=\"subtitle--small\">\n{{ page.metafields.custom.terpene.value.article_2.value.subtitle.value }}\n<\/div>\n{{ page.metafields.custom.terpene.value.article_2.value.title.value }}",
            "text": "",
            "text_liquid": "",
            "link_label": "Read Article",
            "link_label_liquid": "",
            "link": "{{ page.metafields.custom.terpene.value.article_2.value.link.value }}",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "42238794-e859-4a31-827c-850d56116cbc": {
          "type": "column",
          "source": "{{ page.metafields.custom.terpene.value.article_3.value }}",
          "settings": {
            "image": "{{ page.metafields.custom.terpene.value.article_3.value.image.value }}",
            "title": "{{ page.metafields.custom.terpene.value.article_3.value.subtitle.value }}",
            "title_liquid": "<div class=\"subtitle--small\">\n{{ page.metafields.custom.terpene.value.article_3.value.subtitle.value }}\n<\/div>\n{{ page.metafields.custom.terpene.value.article_3.value.title.value }}",
            "text": "",
            "text_liquid": "",
            "link_label": "Read Article",
            "link_label_liquid": "",
            "link": "{{ page.metafields.custom.terpene.value.article_3.value.link.value }}",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "template--16993423655157__5d3a9773-a400-42cb-bc37-8e4fc09c0f14-column-1",
        "09442142-346d-4c66-bb3c-94f3ab2a7850",
        "42238794-e859-4a31-827c-850d56116cbc"
      ],
      "custom_css": [
        "slider-component {padding: 0;}",
        ".multicolumn-card__info {padding-left: 0; padding-right: 0;}",
        ".multicolumn-card__image-wrapper {margin-left: 0; margin-right: 0;}"
      ],
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-5",
        "color_scheme_content": "scheme-5",
        "background_style": "none",
        "content_width": "page-width",
        "content_position": "full-width",
        "columns_tablet": 2,
        "columns_desktop": 3,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "swipe_on_desktop": false,
        "image_width": "full",
        "image_ratio": "adapt",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h6",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 10,
        "padding_bottom_mobile": 55,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 110,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": true,
        "custom_css_class": "",
        "custom_liquid": "<style>\n#shopify-section-{{ section.id }} .multicolumn-card__image-wrapper > a:after {\n  content: \"\";\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  left: 0;\n  top: 0;\n}\n<\/style>\n<script>\n  var links = document.links;\n  for (let i = 0, linksLength = links.length; i < linksLength; i++) {\n    if (links[i].hostname !== window.location.hostname) {\n      links[i].target = '_blank';\n      links[i].rel = 'noreferrer noopener';\n    }\n  }\n<\/script>"
      }
    },
    "ed6cec4a-6370-4edf-891e-2558f9ee6801": {
      "type": "multicolumn",
      "blocks": {
        "0399c451-d347-443b-ac9c-a9bf7639f781": {
          "type": "column",
          "settings": {
            "title": "",
            "title_liquid": "",
            "text": "<p><br\/><br\/><\/p>",
            "text_liquid": "<div class=\"enjoy-shift-custom-multicolumn\">\n  {{ page.metafields.custom.terpene_link_1.value.image | image_url: width: 750 | image_tag: class: 'bon-image', loading: 'lazy' }}\n  <div class=\"enjoy-shift-custom-multicolumn-content\">\n    <a href=\"{{ page.metafields.custom.terpene_link_1.value.page_url.value }}\" style=\"text-decoration: none;\">\n      <div style=\"max-width: 115px;margin-bottom: 1rem;\">\n        {{ page.metafields.custom.terpene_link_1.value.scientific_image_light | image_url: width: 400 | image_tag: class: 'bon-image', loading: 'lazy' }}\n      <\/div>\n      <h3>{{ page.metafields.custom.terpene_link_1.value.title.value }}<\/h3>\n      <p class=\"h6 caption\" style=\"margin-bottom: 1rem;\">{{ page.metafields.custom.terpene_link_1.value.scientific_name.value }}<\/p>\n      <p>\n        {{ page.metafields.custom.terpene_link_1.value.short_description | metafield_tag }}\n      <\/p>\n    <\/a>\n    <a class=\"link\" href=\"{{ page.metafields.custom.terpene_link_1.value.page_url.value }}\"> Learn More <\/a>\n  <\/div>\n<\/div>",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "cd9e36be-0b24-4a1f-a1d8-777ac4eb2aaf": {
          "type": "column",
          "settings": {
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "<div class=\"enjoy-shift-custom-multicolumn\">\n  {{ page.metafields.custom.terpene_link_2.value.image | image_url: width: 750 | image_tag: class: 'bon-image', loading: 'lazy' }}\n  <div class=\"enjoy-shift-custom-multicolumn-content\">\n    <a href=\"{{ page.metafields.custom.terpene_link_2.value.page_url.value }}\" style=\"text-decoration: none;\">\n      <div style=\"max-width: 115px;margin-bottom: 1rem;\">\n        {{ page.metafields.custom.terpene_link_2.value.scientific_image_light | image_url: width: 400 | image_tag: class: 'bon-image', loading: 'lazy' }}\n      <\/div>\n      <h3>{{ page.metafields.custom.terpene_link_2.value.title.value }}<\/h3>\n      <p class=\"h6 caption\" style=\"margin-bottom: 1rem;\">{{ page.metafields.custom.terpene_link_2.value.scientific_name.value }}<\/p>\n      <p>\n        {{ page.metafields.custom.terpene_link_2.value.short_description | metafield_tag }}\n      <\/p>\n    <\/a>\n    <a class=\"link\" href=\"{{ page.metafields.custom.terpene_link_2.value.page_url.value }}\"> Learn More <\/a>\n  <\/div>\n<\/div>",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "0399c451-d347-443b-ac9c-a9bf7639f781",
        "cd9e36be-0b24-4a1f-a1d8-777ac4eb2aaf"
      ],
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-2",
        "color_scheme_content": "scheme-2",
        "background_style": "none",
        "content_width": "full-width",
        "content_position": "full-width",
        "columns_tablet": 2,
        "columns_desktop": 2,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "swipe_on_desktop": false,
        "image_width": "full",
        "image_ratio": "portrait",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h4",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": false,
        "custom_css_class": "section--multicolumn--terpine",
        "custom_liquid": ""
      }
    }
  },
  "order": [
    "main",
    "d20e60d7-bda1-4955-8221-3b8b469283b3",
    "32525b6e-7fd9-40f6-8b93-43097da0b7f0",
    "5d3a9773-a400-42cb-bc37-8e4fc09c0f14",
    "ed6cec4a-6370-4edf-891e-2558f9ee6801"
  ]
}
