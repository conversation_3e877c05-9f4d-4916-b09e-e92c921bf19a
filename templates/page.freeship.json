/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "heading_size": "h2",
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 36,
        "padding_top_desktop": 56,
        "padding_bottom_desktop": 56
      }
    },
    "image_banner_PEAjJM": {
      "type": "image-banner",
      "blocks": {
        "heading_CTPKNf": {
          "type": "heading",
          "settings": {
            "heading": "Try a 12 Pack and Gummies, Get Free Shipping",
            "heading_liquid": "",
            "heading_size": "h2"
          }
        }
      },
      "block_order": [
        "heading_CTPKNf"
      ],
      "name": "t:sections.image-banner.presets.name",
      "settings": {
        "hide_size": "",
        "image": "shopify://shop_images/things-to-do-in-Bend-Oregon.png",
        "background_video": "",
        "image_height_desktop": "small",
        "banner_minheight_desktop": 75,
        "banner_minheight_desktop_units": "vh",
        "desktop_content_position": "middle-center",
        "desktop_content_alignment": "center",
        "show_text_box": true,
        "buttons_bottom_desktop": false,
        "image_mobile": "shopify://shop_images/things-to-do-in-Bend-Oregon.png",
        "background_video_mobile": "",
        "image_behavior": "none",
        "image_height_mobile": "small",
        "banner_minheight_mobile": 75,
        "banner_minheight_mobile_units": "vh",
        "mobile_content_alignment": "center",
        "show_text_below": true,
        "reverse_text_placement_mobile": false,
        "buttons_bottom_mobile": false,
        "overlay_gradient": "",
        "image_overlay_opacity_mobile": 20,
        "image_overlay_opacity": 20,
        "color_scheme": "",
        "content_width": "full-width",
        "override_content_max_width": false,
        "content_max_width": 71,
        "content_max_width_desktop": 90,
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "color_scheme_section": "",
        "custom_css_class": "",
        "custom_liquid": "",
        "page": ""
      }
    },
    "featured_collection_WTV4nP": {
      "type": "featured-collection",
      "blocks": {
        "featured_collection_3AETQ3": {
          "type": "featured_collection",
          "settings": {
            "collection": "all",
            "product_list": [
              "downshift",
              "downshift-trail-berry",
              "downshift-wild-tangerine",
              "down-shift-variety-pack"
            ],
            "product_tags": "",
            "title": "DownShift",
            "title_liquid": "",
            "description": "",
            "show_description": false
          }
        }
      },
      "block_order": [
        "featured_collection_3AETQ3"
      ],
      "name": "t:sections.featured-collection.presets.name",
      "settings": {
        "hide_size": "",
        "color_scheme": "",
        "text_alignment_mobile": "left",
        "columns_mobile": "2",
        "swipe_on_mobile": false,
        "padding_top_mobile": 50,
        "text_alignment_desktop": "left",
        "columns_desktop": 4,
        "enable_desktop_slider": false,
        "full_width": false,
        "padding_top_desktop": 50,
        "padding_bottom_desktop": 0,
        "title": "DownShift",
        "heading_size": "h2",
        "description": "",
        "description_style": "body",
        "tab_link_size": "",
        "products_to_show": 8,
        "view_all_style": "no",
        "hide_cart_products": false,
        "quick_add": "standard",
        "quick_add_button_style": "button button--secondary",
        "image_ratio": "",
        "image_shape": "",
        "show_secondary_image": "",
        "card_heading_font_size": "",
        "show_vendor": "",
        "show_rating": "",
        "quick_add_behavior": "",
        "show_alternative_title": "",
        "show_card_product_custom_field": "",
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "featured_collection_xPmeNW": {
      "type": "featured-collection",
      "blocks": {
        "featured_collection_niV7Fc": {
          "type": "featured_collection",
          "settings": {
            "collection": "all",
            "product_list": [
              "shift-ginger-sour",
              "shift-margarita",
              "shift-paloma",
              "shift-variety-pack"
            ],
            "product_tags": "",
            "title": "DownShift",
            "title_liquid": "",
            "description": "",
            "show_description": false
          }
        }
      },
      "block_order": [
        "featured_collection_niV7Fc"
      ],
      "name": "t:sections.featured-collection.presets.name",
      "settings": {
        "hide_size": "",
        "color_scheme": "",
        "text_alignment_mobile": "left",
        "columns_mobile": "2",
        "swipe_on_mobile": false,
        "padding_top_mobile": 50,
        "text_alignment_desktop": "left",
        "columns_desktop": 4,
        "enable_desktop_slider": false,
        "full_width": false,
        "padding_top_desktop": 50,
        "padding_bottom_desktop": 0,
        "title": "Cocktails",
        "heading_size": "h2",
        "description": "",
        "description_style": "body",
        "tab_link_size": "",
        "products_to_show": 8,
        "view_all_style": "no",
        "hide_cart_products": false,
        "quick_add": "standard",
        "quick_add_button_style": "button button--secondary",
        "image_ratio": "",
        "image_shape": "",
        "show_secondary_image": "",
        "card_heading_font_size": "",
        "show_vendor": "",
        "show_rating": "",
        "quick_add_behavior": "",
        "show_alternative_title": "",
        "show_card_product_custom_field": "",
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "featured_collection_LNegX3": {
      "type": "featured-collection",
      "blocks": {
        "featured_collection_twtKpH": {
          "type": "featured_collection",
          "settings": {
            "collection": "all",
            "product_list": [
              "downshift-gummy-trail-berry-10ct-bag",
              "downshift-gummy-wild-tangerine-10ct-bag",
              "downshift-gummy-variety-pack"
            ],
            "product_tags": "",
            "title": "DownShift",
            "title_liquid": "",
            "description": "",
            "show_description": false
          }
        }
      },
      "block_order": [
        "featured_collection_twtKpH"
      ],
      "name": "t:sections.featured-collection.presets.name",
      "settings": {
        "hide_size": "",
        "color_scheme": "",
        "text_alignment_mobile": "left",
        "columns_mobile": "2",
        "swipe_on_mobile": false,
        "padding_top_mobile": 50,
        "padding_bottom_mobile": 50,
        "text_alignment_desktop": "left",
        "columns_desktop": 4,
        "enable_desktop_slider": false,
        "full_width": false,
        "padding_top_desktop": 50,
        "padding_bottom_desktop": 50,
        "title": "Gummies",
        "heading_size": "h2",
        "description": "",
        "description_style": "body",
        "tab_link_size": "",
        "products_to_show": 8,
        "view_all_style": "no",
        "hide_cart_products": false,
        "quick_add": "standard",
        "quick_add_button_style": "button button--secondary",
        "image_ratio": "",
        "image_shape": "",
        "show_secondary_image": "",
        "card_heading_font_size": "",
        "show_vendor": "",
        "show_rating": "",
        "quick_add_behavior": "",
        "show_alternative_title": "",
        "show_card_product_custom_field": "",
        "custom_css_class": "",
        "custom_liquid": ""
      }
    }
  },
  "order": [
    "main",
    "image_banner_PEAjJM",
    "featured_collection_WTV4nP",
    "featured_collection_xPmeNW",
    "featured_collection_LNegX3"
  ]
}
