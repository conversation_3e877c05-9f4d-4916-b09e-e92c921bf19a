/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "heading_size": "h0",
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 36,
        "padding_top_desktop": 36,
        "padding_bottom_desktop": 36
      }
    },
    "305524df-8a5f-40d8-a6c3-f16bd2730b42": {
      "type": "rich-text",
      "blocks": {
        "d624d76e-141c-423c-a164-615cc474c18b": {
          "type": "custom_liquid",
          "disabled": true,
          "settings": {
            "title": "My custom liquid",
            "custom_liquid": "<div class=\"h1\" style=\"display: flex;justify-content: space-between;align-items:center;\">\n<span>Get<\/span>\n{{ images['hemp_leaf_dark.svg'] \n  | image_url: width: 40\n  | image_tag\n}}\n<span>in<\/span>\n<span>touch<\/span>\n<\/div>"
          }
        },
        "e341c469-9f2f-4284-9256-d99f257a7fa1": {
          "type": "heading",
          "settings": {
            "heading": "Get In Touch",
            "heading_size": "h1",
            "text_indent": ""
          }
        }
      },
      "block_order": [
        "d624d76e-141c-423c-a164-615cc474c18b",
        "e341c469-9f2f-4284-9256-d99f257a7fa1"
      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "left",
        "color_scheme": "scheme-1",
        "padding_top_mobile": 45,
        "padding_top": 160,
        "padding_bottom_mobile": 25,
        "padding_bottom": 0,
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "",
        "padding_left_mobile": 0,
        "padding_left_desktop": 10,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "d62810e0-be76-4c2f-adbb-6beb53e4b69d": {
      "type": "multicolumn",
      "blocks": {
        "template--16978522603765__d62810e0-be76-4c2f-adbb-6beb53e4b69d-column-1": {
          "type": "column",
          "settings": {
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "<div>\n<p>\nWe're here to serve your needs. Whether you're a valued consumer with questions about Downshift products or a business ready to explore partnership opportunities, we've got you covered. To streamline your experience, please select the option below that best describes your inquiry:\n<\/p><br><p>\nConsumer with a Question: If you're a consumer seeking assistance or information about our products, please click on 'Customer Support.'\n<\/p><br><p>\nBusiness Interested in Partnership: If you're a distributor, retailer, convenience store, music festival, grocery, bar, restaurant, or brewery ready to collaborate with DownShift, please click on 'B2B Sales.'\n<\/p><br><p>\nYour journey with Downshift begins here, and we're excited to assist you every step of the way.\n<\/p>\n<\/div>",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-2-desktop"
          }
        },
        "template--16978522603765__d62810e0-be76-4c2f-adbb-6beb53e4b69d-column-2": {
          "type": "column",
          "settings": {
            "title": "Customer Support",
            "title_liquid": "",
            "text": "",
            "text_liquid": "<div class=\"bon-text-justify\">\n  <p>\n    Have a question or need assistance with Shift products? Our dedicated customer support team is here to help. Reach\n    out to us, and we'll ensure your Downshift experience is nothing short of exceptional.\n  <\/p>\n  <div style=\"display:flex;margin-top: 2rem;align-items:center;gap:2rem;flex-wrap: wrap;\">\n    <a class=\"button button-2\" style=\"margin-top: 0;min-width: 200px;\" href=\"mailto:<EMAIL>\">Contact Us<\/a><span><EMAIL><\/span>\n  <\/div>\n<\/div>",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "button button-1",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "7ffc3dbc-bc3b-410d-934c-debb28a5e019": {
          "type": "column",
          "settings": {
            "title": "B2B SALES",
            "title_liquid": "",
            "text": "",
            "text_liquid": "<div>\n  <p>\n    Ready to elevate your offerings with Shift? Connect with us today to explore partnership opportunities and bring the\n    future of premium beverages to your customers. Let's shape the future of relaxation and focus together.\n  <\/p>\n  <div style=\"display:flex;margin-top: 2rem;align-items:center;gap:2rem;flex-wrap: wrap;\">\n    <a class=\"button button-2\" style=\"margin-top: 0;min-width: 200px;\" href=\"mailto:<EMAIL>\">Contact Us<\/a><span><EMAIL><\/span>\n  <\/div>\n<\/div>",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "scheme-1",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "template--16978522603765__d62810e0-be76-4c2f-adbb-6beb53e4b69d-column-1",
        "template--16978522603765__d62810e0-be76-4c2f-adbb-6beb53e4b69d-column-2",
        "7ffc3dbc-bc3b-410d-934c-debb28a5e019"
      ],
      "custom_css": [
        "slider-component {padding: 0;}"
      ],
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-1",
        "color_scheme_content": "scheme-1",
        "background_style": "none",
        "content_width": "page-width",
        "content_position": "full-width",
        "columns_tablet": 2,
        "columns_desktop": 2,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "swipe_on_desktop": false,
        "image_width": "full",
        "image_ratio": "adapt",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h5",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 170,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": false,
        "custom_css_class": "",
        "custom_liquid": "<style>\n@media screen and (min-width: 990px) {\n  #shopify-section-{{ section.id }} h3.h5 {\n    font-family: var(--font-body-family);\n    font-weight: bold;\n    font-size: 2rem;\n  }\n  #shopify-section-{{ section.id }} .multicolumn-list__item:first-of-type:after {\n    content: '';\n    border-bottom: 1px solid;\n    position: absolute;\n    bottom: -1.5rem;\n    width: calc(100% - 3rem);\n    left: 1.5rem;\n  }\n}\n<\/style>"
      }
    },
    "form": {
      "type": "contact-form",
      "disabled": true,
      "settings": {
        "heading": "",
        "heading_liquid": "",
        "heading_size": "h1",
        "color_scheme": "scheme-2",
        "custom_css_class": "",
        "custom_liquid": "",
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 36,
        "padding_top_desktop": 36,
        "padding_bottom_desktop": 36,
        "hide_size": ""
      }
    },
    "8acf3398-1eb8-4b85-b22c-f120920719c0": {
      "type": "image-banner",
      "blocks": {
        "d840dfd7-eeb3-4ed0-8b8a-702427918adb": {
          "type": "custom_liquid",
          "settings": {
            "title": "CSS configs",
            "custom_liquid": "<style>\n  #shopify-section-{{ section.id }},\n  #shopify-section-{{ section.id }} .global-media-settings {\n    background: #D8DBC8;\n  }\n  #shopify-section-{{ section.id }} .banner__box {\n    padding: 1rem 1rem 0 0;\n    position: absolute;\n    left: 0;\n    bottom: 0rem;\n    line-height: 1.15;\n    letter-spacing: -0.3px;\n    min-width: auto;\n  }\n<\/style>\n<div style=\"text-align: left;\">\n  <div style=\"text-transform: uppercase;\">\n    {{ shop.address.company -}}\n    <br>\n    {{ shop.address.address1 | append: ', ' | append: shop.address.address2 -}}\n    <br>\n    {{ shop.address.city }}, {{ shop.address.province_code }}\n    {{ shop.address.zip -}}\n    <br>\n    {{ shop.phone }}\n  <\/div>\n<\/div>"
          }
        }
      },
      "block_order": [
        "d840dfd7-eeb3-4ed0-8b8a-702427918adb"
      ],
      "custom_css": [

      ],
      "settings": {
        "hide_size": "",
        "image": "shopify:\/\/shop_images\/addy_desktop.svg",
        "background_video": "",
        "image_height_desktop": "adapt",
        "banner_minheight_desktop": 75,
        "banner_minheight_desktop_units": "vh",
        "desktop_content_position": "bottom-left",
        "desktop_content_alignment": "center",
        "show_text_box": true,
        "buttons_bottom_desktop": false,
        "image_mobile": "shopify:\/\/shop_images\/addy_mobile.svg",
        "background_video_mobile": "",
        "image_behavior": "none",
        "image_height_mobile": "adapt",
        "banner_minheight_mobile": 75,
        "banner_minheight_mobile_units": "vh",
        "mobile_content_alignment": "center",
        "show_text_below": false,
        "reverse_text_placement_mobile": false,
        "buttons_bottom_mobile": false,
        "overlay_gradient": "",
        "image_overlay_opacity_mobile": 0,
        "image_overlay_opacity": 0,
        "color_scheme": "scheme-1",
        "content_width": "page-width",
        "override_content_max_width": false,
        "content_max_width": 71,
        "content_max_width_desktop": 90,
        "padding_top_mobile": 44,
        "padding_bottom_mobile": 60,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 104,
        "color_scheme_section": "scheme-1",
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "1167a9b0-6731-4392-b8d8-ef9885bab48d": {
      "type": "rich-text",
      "blocks": {
        "template--21493598748988__1167a9b0-6731-4392-b8d8-ef9885bab48d-heading-1": {
          "type": "heading",
          "settings": {
            "heading": "#SHIFTWITHNATURE",
            "heading_size": "h2",
            "text_indent": ""
          }
        }
      },
      "block_order": [
        "template--21493598748988__1167a9b0-6731-4392-b8d8-ef9885bab48d-heading-1"
      ],
      "custom_css": [
        ".rich-text__heading {color: #d7dbc6;}"
      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "center",
        "color_scheme": "scheme-3",
        "padding_top_mobile": 55,
        "padding_top": 100,
        "padding_bottom_mobile": 25,
        "padding_bottom": 40,
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "33674f5f-3169-4a69-99cf-d30dab64086f": {
      "type": "multicolumn",
      "blocks": {
        "template--21493598748988__33674f5f-3169-4a69-99cf-d30dab64086f-column-1": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_Death_to_stock_photography_wild_4.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "baa4221b-2fd0-4e2c-88ca-0a6dbff42879": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_NATEAPPEL_20231022_0600.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "a71a716c-6c93-4b8e-a465-8a5b939394a3": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_NATEAPPEL_20231022_0742.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "2eecbfdb-cfab-4fcb-b4ec-5a752b10d4a3": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_NATEAPPEL_20231022_9234.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "21d00bd2-929d-4aca-a87c-cc209907f410": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_NATEAPPEL_20231022_9902.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "cacef482-c675-4480-843a-5115bbafa66d": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_NATEAPPEL_20231022_0339.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "c7bbb8df-d315-4069-bfd5-53d99d359978": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_Death_to_Stock_Chasing_Sunrise_6.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "template--21493598748988__33674f5f-3169-4a69-99cf-d30dab64086f-column-1",
        "baa4221b-2fd0-4e2c-88ca-0a6dbff42879",
        "a71a716c-6c93-4b8e-a465-8a5b939394a3",
        "2eecbfdb-cfab-4fcb-b4ec-5a752b10d4a3",
        "21d00bd2-929d-4aca-a87c-cc209907f410",
        "cacef482-c675-4480-843a-5115bbafa66d",
        "c7bbb8df-d315-4069-bfd5-53d99d359978"
      ],
      "custom_css": [

      ],
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-3",
        "color_scheme_content": "",
        "background_style": "primary",
        "content_width": "full-width",
        "content_position": "full-width",
        "columns_tablet": 2,
        "columns_desktop": 6,
        "columns_mobile": "1",
        "swipe_on_mobile": true,
        "swipe_on_desktop": true,
        "image_width": "full",
        "image_ratio": "square",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h4",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 55,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 75,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": false,
        "custom_css_class": "section--multicolumn--ig-slider",
        "custom_liquid": ""
      }
    }
  },
  "order": [
    "main",
    "305524df-8a5f-40d8-a6c3-f16bd2730b42",
    "d62810e0-be76-4c2f-adbb-6beb53e4b69d",
    "form",
    "8acf3398-1eb8-4b85-b22c-f120920719c0",
    "1167a9b0-6731-4392-b8d8-ef9885bab48d",
    "33674f5f-3169-4a69-99cf-d30dab64086f"
  ]
}
