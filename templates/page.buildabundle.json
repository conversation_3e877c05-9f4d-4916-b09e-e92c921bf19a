/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "heading_size": "h2",
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 16,
        "padding_top_desktop": 56,
        "padding_bottom_desktop": 16
      }
    },
    "image_banner_fcyQJt": {
      "type": "image-banner",
      "blocks": {
        "heading_KEHBUe": {
          "type": "heading",
          "settings": {
            "heading": "Build Your Own Subscription",
            "heading_liquid": "",
            "heading_size": "h2"
          }
        }
      },
      "block_order": [
        "heading_KEHBUe"
      ],
      "name": "t:sections.image-banner.presets.name",
      "settings": {
        "hide_size": "",
        "image": "shopify://shop_images/ig_NATEAPPEL_20231022_0742.jpg",
        "background_video": "",
        "image_height_desktop": "large",
        "banner_minheight_desktop": 75,
        "banner_minheight_desktop_units": "vh",
        "desktop_content_position": "bottom-center",
        "desktop_content_alignment": "center",
        "show_text_box": true,
        "buttons_bottom_desktop": false,
        "background_video_mobile": "",
        "image_behavior": "none",
        "image_height_mobile": "small",
        "banner_minheight_mobile": 75,
        "banner_minheight_mobile_units": "vh",
        "mobile_content_alignment": "center",
        "show_text_below": true,
        "reverse_text_placement_mobile": false,
        "buttons_bottom_mobile": false,
        "overlay_gradient": "",
        "image_overlay_opacity_mobile": 0,
        "image_overlay_opacity": 0,
        "color_scheme": "",
        "content_width": "full-width",
        "override_content_max_width": false,
        "content_max_width": 71,
        "content_max_width_desktop": 90,
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "color_scheme_section": "",
        "custom_css_class": "",
        "custom_liquid": "",
        "page": ""
      }
    },
    "image_with_text_qyYBpq": {
      "type": "image-with-text",
      "blocks": {
        "heading_YHGHTn": {
          "type": "heading",
          "settings": {
            "heading": "Save XX%",
            "heading_liquid": "",
            "heading_size": "h3"
          }
        },
        "text_xM8HRV": {
          "type": "text",
          "settings": {
            "text": "<p><strong>How it works</strong></p><ol><li>Select your delivery frequency</li><li>Pick your favorite items</li><li>Sip back and enjoy!</li></ol><p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</p>",
            "text_style": "body"
          }
        }
      },
      "block_order": [
        "heading_YHGHTn",
        "text_xM8HRV"
      ],
      "name": "t:sections.image-with-text.presets.name",
      "settings": {
        "image": "shopify://shop_images/ShiftBundleTransparent.png",
        "show_video_controls": false,
        "image_contain": false,
        "height": "adapt",
        "show_video_mobile_controls": false,
        "image_mobile_contain": false,
        "height_mobile": "adapt",
        "desktop_image_width": "medium",
        "layout": "image_first",
        "desktop_content_position": "middle",
        "desktop_content_alignment": "left",
        "content_layout": "no-overlap",
        "color_scheme_content": "",
        "image_behavior": "none",
        "mobile_content_alignment": "left",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 30,
        "page": "",
        "hide_size": "",
        "color_scheme_section": "",
        "content_width": "page-width",
        "content_position": "full-width",
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "content_padding_mobile": 1.5,
        "content_padding_desktop": 4,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "multicolumn_iy8kxR": {
      "type": "multicolumn",
      "blocks": {
        "column_6BKNab": {
          "type": "column",
          "settings": {
            "title": "Benefit 1",
            "title_liquid": "",
            "text": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.<br/></p>",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "column_tLQemc": {
          "type": "column",
          "settings": {
            "title": "Benefit 2",
            "title_liquid": "",
            "text": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "column_hLzAtz": {
          "type": "column",
          "disabled": true,
          "settings": {
            "title": "Column",
            "title_liquid": "",
            "text": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "column_6BKNab",
        "column_tLQemc",
        "column_hLzAtz"
      ],
      "custom_css": [
        ".button {display: none;}"
      ],
      "name": "t:sections.multicolumn.presets.name",
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-1",
        "color_scheme_content": "scheme-2",
        "background_style": "primary",
        "content_width": "page-width",
        "content_position": "full-width",
        "columns_tablet": 2,
        "columns_desktop": 2,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "swipe_on_desktop": false,
        "image_width": "full",
        "image_ratio": "adapt",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h4",
        "button_label": "Button label",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 50,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 30,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": false,
        "page": "",
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "rich_text_MDatVN": {
      "type": "rich-text",
      "blocks": {
        "heading_7ytejW": {
          "type": "heading",
          "settings": {
            "heading": "Start Building",
            "heading_size": "h1",
            "text_indent": ""
          }
        },
        "text_AHyf8P": {
          "type": "text",
          "settings": {
            "text": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>",
            "text_indent": ""
          }
        }
      },
      "block_order": [
        "heading_7ytejW",
        "text_AHyf8P"
      ],
      "name": "t:sections.rich-text.presets.name",
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "center",
        "color_scheme": "",
        "padding_top_mobile": 0,
        "padding_top": 0,
        "padding_bottom_mobile": 30,
        "padding_bottom": 30,
        "page": "",
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "multicolumn_V3NjWj": {
      "type": "multicolumn",
      "blocks": {
        "column_RtNYd9": {
          "type": "column",
          "settings": {
            "image": "shopify://shop_images/SHIFT_Cocktails_Trio_1.png",
            "title": "Starter Bundle",
            "title_liquid": "",
            "text": "<ul><li>4 cans DownShift Lime Citrus</li><li>4 cans DownShift Trail Berry</li><li>4 cans Shift Margarita</li><li>10ct Bag DownShift Gummies Trail Berry</li></ul>",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "Add to Cart\n\n<script type=\"application/json\" id=\"buildabundle-addtocart-starter\">\n{\n    \"items\": [{\n        \"id\": \"46874711949557\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874728005877\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874723713269\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874722599157\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        }    \n    ]\n}\n</script>",
            "link": "https://enjoyshift.com/tools/bundle-subscriptions/bundle/9254644154613?product=shift-society-build-a-custom-bundle",
            "button_style": "button button-2",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "scheme-1",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "column_BLKYjn": {
          "type": "column",
          "settings": {
            "image": "shopify://shop_images/downshift-asset-516125_7d0c2842-61e1-4d46-857b-aab8ab95a0c3.png",
            "title": "Best Sellers Bundle",
            "title_liquid": "",
            "text": "<ul><li>4 cans DownShift Lime Citrus</li><li>4 cans DownShift Trail Berry</li><li>4 cans Shift Margarita</li><li>4 cans Shift Paloma</li><li>10ct Bag DownShift Gummies Trail Berry</li><li>10ct Bag DownShift Gummies Wild Tangerine</li></ul>",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "Add to Cart\n\n<script type=\"application/json\" id=\"buildabundle-addtocart-bestsellers\">\n{\n    \"items\": [{\n        \"id\": \"46874706772213\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874711949557\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874728136949\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874728005877\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874723713269\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874722599157\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        }    \n    ]\n}\n</script>",
            "link": "https://enjoyshift.com/tools/bundle-subscriptions/bundle/9254644154613?product=shift-society-build-a-custom-bundle",
            "button_style": "button button-2",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "column_3CfcDa": {
          "type": "column",
          "settings": {
            "image": "shopify://shop_images/ShiftBundleTransparent.png",
            "title": "All In Bundle",
            "title_liquid": "",
            "text": "<ul><li>4 cans DownShift Lime Citrus</li><li>4 cans DownShift Trail Berry</li><li>4 cans DownShift Wild Tangerine</li><li>4 cans Shift Margarita</li><li>4 cans Shift Paloma</li><li>4 cans Shift Ginger Sour</li><li>10ct Bag DownShift Gummies Trail Berry</li><li>10ct Bag DownShift Gummies Wild Tangerine</li></ul>",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "Add to Cart\n\n<script type=\"application/json\" id=\"buildabundle-addtocart-allin\">\n{\n    \"items\": [{\n        \"id\": \"46874706772213\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874711949557\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874727842037\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874728136949\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874728005877\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874726760693\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874723713269\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874722599157\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        }\n    ]\n}\n</script>",
            "link": "https://enjoyshift.com/tools/bundle-subscriptions/bundle/9254644154613?product=shift-society-build-a-custom-bundle",
            "button_style": "button button-2",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "column_RtNYd9",
        "column_BLKYjn",
        "column_3CfcDa"
      ],
      "name": "t:sections.multicolumn.presets.name",
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-6",
        "color_scheme_content": "scheme-1",
        "background_style": "primary",
        "content_width": "page-width",
        "content_position": "full-width",
        "columns_tablet": 3,
        "columns_desktop": 3,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "swipe_on_desktop": false,
        "image_width": "full",
        "image_ratio": "adapt",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h4",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 30,
        "padding_bottom_mobile": 30,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 60,
        "padding_bottom_desktop": 60,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": false,
        "page": "",
        "custom_css_class": "buildabundle-addtocart",
        "custom_liquid": "<script>\n    // handle dynamic Add to Cart functionality\n    document.addEventListener('DOMContentLoaded', () => {\n        const bundleATCbtns = document.querySelectorAll('.buildabundle-addtocart .button');  \n        bundleATCbtns.forEach(thisButton => {\n            thisButton.addEventListener('click', function(event) {\n                event.preventDefault();\n                // find child <script> tag with ID starting with 'buildabundle-addtocart'\n                const bundleContents = thisButton.querySelector('script[id^=\"buildabundle-addtocart\"]');\n                if(bundleContents){\n                    const bundleJSON = JSON.parse(bundleContents.textContent);\n\n                    // add bundle to cart\n                    fetch(window.Shopify.routes.root + 'cart/add.js', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify(bundleJSON)\n                    })\n                    .then(response => {\n                        window.location.href = '/cart'\n                        return response.json();\n                    })\n                    .catch((error) => {\n                        console.error('Error:', error);\n                    });\n                }\n            });\n        });\n    });\n</script>"
      }
    },
    "multirow_pVnr7E": {
      "type": "multirow",
      "blocks": {
        "row_EzbmzB": {
          "type": "row",
          "settings": {
            "image": "shopify://shop_images/6-packCocktails_DownShift.png",
            "caption": "",
            "heading": "Bundle #1",
            "text": "<p>Build your own box of 6-packs. Lorem ipsum dolor sit amet.</p>",
            "button_label": "GET STARTED",
            "button_link": "https://enjoyshift.com/tools/bundle-subscriptions/bundle/9206364864757"
          }
        },
        "row_JtiH8V": {
          "type": "row",
          "settings": {
            "image": "shopify://shop_images/downshift-asset-516125.png",
            "caption": "",
            "heading": "Bundle #2",
            "text": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>",
            "button_label": "GET STARTED",
            "button_link": "https://enjoyshift.com/tools/bundle-subscriptions/bundle/9206364864757"
          }
        }
      },
      "block_order": [
        "row_EzbmzB",
        "row_JtiH8V"
      ],
      "disabled": true,
      "name": "t:sections.multirow.presets.name",
      "settings": {
        "image_height": "medium",
        "desktop_image_width": "medium",
        "heading_size": "h1",
        "text_style": "body",
        "button_style": "button button--primary",
        "desktop_content_position": "middle",
        "desktop_content_alignment": "left",
        "image_layout": "alternate-left",
        "section_color_scheme": "",
        "row_color_scheme": "",
        "mobile_content_alignment": "left",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 75,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "content_width": "page-width",
        "content_position": "full-width",
        "hide_size": "",
        "custom_css_class": "",
        "custom_liquid": "",
        "page": ""
      }
    },
    "collapsible_content_LAcCJh": {
      "type": "collapsible-content",
      "blocks": {
        "collapsible_row_pVAHEV": {
          "type": "collapsible_row",
          "settings": {
            "heading": "Lorem Ipsum 1",
            "icon": "none",
            "row_content": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "collapsible_row_VYNqJL": {
          "type": "collapsible_row",
          "settings": {
            "heading": "Lorem Ipsum 2",
            "icon": "none",
            "row_content": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "collapsible_row_U8aRwt": {
          "type": "collapsible_row",
          "settings": {
            "heading": "Lorem Ipsum 3",
            "icon": "none",
            "row_content": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "collapsible_row_bVHwfw": {
          "type": "collapsible_row",
          "settings": {
            "heading": "Lorem Ipsum 4",
            "icon": "none",
            "row_content": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</p>",
            "row_content_liquid": "",
            "page": ""
          }
        }
      },
      "block_order": [
        "collapsible_row_pVAHEV",
        "collapsible_row_VYNqJL",
        "collapsible_row_U8aRwt",
        "collapsible_row_bVHwfw"
      ],
      "name": "t:sections.collapsible_content.presets.name",
      "settings": {
        "caption": "",
        "heading": "Frequently Asked Questions",
        "heading_size": "h2",
        "heading_alignment": "center",
        "layout": "row",
        "container_color_scheme": "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba",
        "color_scheme": "scheme-1",
        "open_first_collapsible_row": false,
        "image_ratio": "adapt",
        "desktop_layout": "image_second",
        "padding_top_desktop": 32,
        "padding_bottom_desktop": 0,
        "padding_top_mobile": 32,
        "padding_bottom_mobile": 0
      }
    },
    "17525410526b1bbc1c": {
      "type": "apps",
      "blocks": {
        "junip_junip_review_carousel_YC3zRe": {
          "type": "shopify://apps/junip/blocks/junip-review-carousel/dc14f5a8-ed15-41b1-ad08-cfba23f9789b",
          "settings": {
            "reviewsType": "product_reviews",
            "sortOrder": "mostHelpful",
            "product": "",
            "reviewTag": "",
            "showSummary": true,
            "title": "Reviews",
            "paddingTop": 48,
            "paddingBottom": 48,
            "containerClass": ""
          }
        }
      },
      "block_order": [
        "junip_junip_review_carousel_YC3zRe"
      ],
      "settings": {
        "include_margins": true,
        "hide_size": "",
        "color_scheme": "",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    }
  },
  "order": [
    "main",
    "image_banner_fcyQJt",
    "image_with_text_qyYBpq",
    "multicolumn_iy8kxR",
    "rich_text_MDatVN",
    "multicolumn_V3NjWj",
    "multirow_pVnr7E",
    "collapsible_content_LAcCJh",
    "17525410526b1bbc1c"
  ]
}
