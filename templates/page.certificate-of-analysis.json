/*
* ------------------------------------------------------------
* IMPORTANT: The contents of this file are auto-generated.
*
* This file may be updated by the Shopify admin theme editor
* or related systems. Please exercise caution as any changes
* made to this file may be overwritten.
* ------------------------------------------------------------
*/
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "heading_size": "h0",
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 36,
        "padding_top_desktop": 56,
        "padding_bottom_desktop": 56
      }
    },
    "206f7885-25cc-48c2-99f7-c3fcca4e026b": {
      "type": "rich-text",
      "blocks": {
        "template--16993423655157__206f7885-25cc-48c2-99f7-c3fcca4e026b-heading-1": {
          "type": "heading",
          "settings": {
            "heading": "CERTIFICATE OF ANALYSIS LOCATOR",
            "heading_size": "h2",
            "text_indent": ""
          }
        },
        "template--16993423655157__206f7885-25cc-48c2-99f7-c3fcca4e026b-text-1": {
          "type": "text",
          "settings": {
            "text": "<p>Enter the 6-digit LOT number found on the bottom of the can to access the Certificate of Analysis.<\/p>",
            "text_indent": ""
          }
        },
        "de1a4ae2-ac44-4ffa-bdbb-2ea22e00fe32": {
          "type": "custom_liquid",
          "settings": {
            "title": "My custom liquid",
            "custom_liquid": "<div class=\"contact page-width page-width--narrow\">\n  <div\n    style=\"max-width: 48rem; margin:auto\"\n    class=\"contact__fields\"\n  >\n    <div class=\"field\">\n      <input\n        class=\"field__input\"\n        autocomplete=\"name\"\n        type=\"text\"\n        id=\"ContactForm-name\"\n        name=\"contact[coa_number]\"\n        value=\"\"\n        placeholder=\"Enter LOT #\"\n        required\n      >\n      <label class=\"field__label\" for=\"ContactForm-name\">Enter LOT #<\/label>\n    <\/div>\n  <\/div>\n<\/div>"
          }
        },
        "0ad5c0e2-5e6b-4c23-8c49-647d8b0e2a9d": {
          "type": "popup",
          "settings": {
            "text": "Submit for COA",
            "text_liquid": "",
            "title": "",
            "page": "",
            "content_liquid": "<style>\n    .product-popup-modal__content-info,\n    .bon-embed,\n    .bon-embed-container {\n      align-items: center;\n      height: 100%;\n      justify-content: center;\n      flex-wrap: nowrap;\n      flex-direction: column;\n      text-align: center;\n      gap: 2rem;\n    }\n  <\/style>\n  {%- assign coa_list = shop.metaobjects.coa_entry_list['coa-master-list'].items -%}\n  {%- if coa_list != blank -%}\n    {%- assign coa_list_numbers = coa_list.value | map: 'number' -%}\n    {%- for item in coa_list.value %}\n      <div id=\"{{ item.number }}\" class=\"bon-embed-container\">\n        <div>\n          <h2>Certificate of analysis<\/h2>\n          <p>The certificate of analysis for lot {{ item.number }} is available.<\/p>\n          <a class=\"button button--secondary\" style=\"margin: 1rem 0 0 1rem;\" href=\"{{ item.file  | file_url }}\" download>Download<\/a>\n        <\/div>\n        <div style=\"background: white; width: 100%; height: 100%;\">\n          <embed class=\"bon-embed\" src=\"{{ item.file  | file_url }}\" style=\"width: 100%; height: 100%;\" width=\"794\" height=\"1123\">\n        <\/div>\n      <\/div>\n    {%- endfor %}\n  {%- endif -%}\n\n  {% assign popup_blocks = section.blocks | where: 'type', 'popup' %}\n  {% assign popup_blocks_first_id = popup_blocks.first.id %}\n\n  <script>\n    window.addEventListener('DOMContentLoaded', () => {\n      let coaList = {{ coa_list_numbers | json }};\n      let coaInput = document.querySelector('#ContactForm-name');\n      let coaSubmit = document.querySelector('#ProductPopup-{{ popup_blocks_first_id }}');\n      let coaEmbeds = document.querySelectorAll('.bon-embed-container');\n\n      coaSubmit.addEventListener('click', buttonCheck);\n      coaInput.addEventListener('input', buttonCheck);\n\n      function buttonCheck() {\n        if (coaInput.value != '' && coaList.includes(coaInput.value)) {\n          coaSubmit.setAttribute('aria-disabled', '');\n          coaSubmit.style.pointerEvents = '';\n\n          coaEmbeds.forEach(embed => {\n            embed.id.includes(coaInput.value) ?\n              embed.style.display = \"flex\":\n              embed.style.display = \"none\";\n          });\n        } else {\n          coaSubmit.setAttribute('aria-disabled', 'true');\n          coaSubmit.style.pointerEvents = 'none';\n        }\n      }\n      buttonCheck();\n    });\n  <\/script>",
            "button_style": "button button--primary",
            "color_scheme": "scheme-2"
          }
        }
      },
      "block_order": [
        "template--16993423655157__206f7885-25cc-48c2-99f7-c3fcca4e026b-heading-1",
        "template--16993423655157__206f7885-25cc-48c2-99f7-c3fcca4e026b-text-1",
        "de1a4ae2-ac44-4ffa-bdbb-2ea22e00fe32",
        "0ad5c0e2-5e6b-4c23-8c49-647d8b0e2a9d"
      ],
      "custom_css": [

      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "center",
        "color_scheme": "scheme-1",
        "padding_top_mobile": 20,
        "padding_top": 115,
        "padding_bottom_mobile": 20,
        "padding_bottom": 105,
        "page": "",
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width page-width--narrow",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "29c55a61-24e7-44e4-8d27-fef87f1f2ee2": {
      "type": "rich-text",
      "blocks": {
        "template--21493598748988__1167a9b0-6731-4392-b8d8-ef9885bab48d-heading-1": {
          "type": "heading",
          "settings": {
            "heading": "DISCOVER THE WORLD OF DOWNSHIFT",
            "heading_size": "h2",
            "text_indent": ""
          }
        },
        "39a3f374-6b4e-4ad3-99eb-e93a415ee51d": {
          "type": "custom_liquid",
          "settings": {
            "title": "Social icons",
            "custom_liquid": "<div style=\"display:flex;justify-content:center;\">\n<ul class=\"list-social list-unstyled\" role=\"list\">\n    <li class=\"list-social__item\">\n      <a target=\"_blank\" href=\"https:\/\/www.instagram.com\/enjoyshift\/\" class=\"link list-social__link\">\n        <span class=\"svg-wrapper\">\n          {{- 'icon-instagram.svg' | inline_asset_content -}}\n        <\/span>\n        <span class=\"visually-hidden\">Instagram<\/span>\n      <\/a>\n    <\/li>\n    <li class=\"list-social__item\">\n      <a target=\"_blank\" href=\"https:\/\/www.youtube.com\/@EnjoyShift\" class=\"link list-social__link\">\n        <span class=\"svg-wrapper\">\n          {{- 'icon-youtube.svg' | inline_asset_content -}}\n        <\/span>\n        <span class=\"visually-hidden\">YouTube<\/span>\n      <\/a>\n    <\/li>\n    <li class=\"list-social__item\">\n      <a target=\"_blank\" href=\"https:\/\/www.tiktok.com\/@enjoyshift\" class=\"link list-social__link\">\n        <span class=\"svg-wrapper\">\n          {{- 'icon-tiktok.svg' | inline_asset_content -}}\n        <\/span>\n        <span class=\"visually-hidden\">TikTok<\/span>\n      <\/a>\n    <\/li>\n  <\/ul>\n<\/div>"
          }
        }
      },
      "block_order": [
        "template--21493598748988__1167a9b0-6731-4392-b8d8-ef9885bab48d-heading-1",
        "39a3f374-6b4e-4ad3-99eb-e93a415ee51d"
      ],
      "custom_css": [
        ".rich-text__heading {color: #d7dbc6;}",
        ".list-social__item .icon {height: 3rem; width: 3rem;}"
      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "center",
        "color_scheme": "scheme-2",
        "padding_top_mobile": 55,
        "padding_top": 100,
        "padding_bottom_mobile": 25,
        "padding_bottom": 40,
        "page": "",
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width page-width--narrow",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "0d02edaa-fb75-4711-bcc2-1ca5ae04a849": {
      "type": "multicolumn",
      "blocks": {
        "template--21493598748988__33674f5f-3169-4a69-99cf-d30dab64086f-column-1": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_Death_to_stock_photography_wild_4.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "baa4221b-2fd0-4e2c-88ca-0a6dbff42879": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_NATEAPPEL_20231022_0600.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "a71a716c-6c93-4b8e-a465-8a5b939394a3": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_NATEAPPEL_20231022_0742.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "2eecbfdb-cfab-4fcb-b4ec-5a752b10d4a3": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_NATEAPPEL_20231022_9234.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "21d00bd2-929d-4aca-a87c-cc209907f410": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_NATEAPPEL_20231022_9902.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "cacef482-c675-4480-843a-5115bbafa66d": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_NATEAPPEL_20231022_0339.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "c7bbb8df-d315-4069-bfd5-53d99d359978": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/ig_Death_to_Stock_Chasing_Sunrise_6.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "template--21493598748988__33674f5f-3169-4a69-99cf-d30dab64086f-column-1",
        "baa4221b-2fd0-4e2c-88ca-0a6dbff42879",
        "a71a716c-6c93-4b8e-a465-8a5b939394a3",
        "2eecbfdb-cfab-4fcb-b4ec-5a752b10d4a3",
        "21d00bd2-929d-4aca-a87c-cc209907f410",
        "cacef482-c675-4480-843a-5115bbafa66d",
        "c7bbb8df-d315-4069-bfd5-53d99d359978"
      ],
      "custom_css": [
        ".multicolumn-card__info,.slider-counter {display: none;}",
        ".slider-button {position: absolute; top: 50%; transform: translateY(-50%); color: #98de59; background: #2b2d1f; border-radius: 50px;}",
        ".slider-button[disabled] .icon {color: #98de59; opacity: 0.7;}",
        ".slider-button .icon {height: 1rem;}",
        ".slider-button--next {right: 2.5rem;}",
        ".slider-button--prev {left: 2.5rem;}"
      ],
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-2",
        "color_scheme_content": "scheme-2",
        "background_style": "primary",
        "content_width": "full-width",
        "content_position": "full-width",
        "columns_tablet": 2,
        "columns_desktop": 6,
        "columns_mobile": "1",
        "swipe_on_mobile": true,
        "swipe_on_desktop": true,
        "image_width": "full",
        "image_ratio": "square",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h4",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 25,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 55,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": false,
        "page": "",
        "custom_css_class": "",
        "custom_liquid": "<style>\n.multicolumn slider-component:not(.page-width-desktop) {\n  overflow-y: none;\n}\n<\/style>"
      }
    },
    "88e09cbd-8324-44ba-84fc-79898b85b888": {
      "type": "rich-text",
      "blocks": {
        "template--21493598748988__587cc6c5-d463-4067-b266-9a505f46b66b-heading-1": {
          "type": "heading",
          "settings": {
            "heading": "DISCOVER OUR TERPINE INFUSIONS",
            "heading_size": "h3",
            "text_indent": ""
          }
        },
        "a52dadb3-1f60-4c36-95eb-7f03d109e22b": {
          "type": "heading",
          "settings": {
            "heading": "#SHIFTWITHNATURE",
            "heading_size": "h3",
            "text_indent": ""
          }
        },
        "10fa5aad-a0eb-432d-9181-a65892bbc0d5": {
          "type": "custom_liquid",
          "settings": {
            "title": "CSS custommizations",
            "custom_liquid": "<style>\n  #shopify-section-{{ section.id }} .rich-text__blocks .rich-text__heading.rte:last-of-type {\n    -webkit-text-fill-color: transparent;\n    -webkit-text-stroke: .1rem;\n  }\n\n  @media screen and (min-width: 990px) {\n    #shopify-section-{{ section.id }} .rich-text__blocks {\n      display: grid;\n      justify-content: space-between;\n      grid-template-columns: auto auto;\n    }\n    #shopify-section-{{ section.id }} .rich-text__blocks .rich-text__heading.rte {\n      margin: 0 0 auto;\n      text-align: left;\n    }\n    #shopify-section-{{ section.id }} .rich-text__blocks .rich-text__heading.rte:last-of-type {\n      text-align: right;\n    }\n  }\n<\/style>"
          }
        }
      },
      "block_order": [
        "template--21493598748988__587cc6c5-d463-4067-b266-9a505f46b66b-heading-1",
        "a52dadb3-1f60-4c36-95eb-7f03d109e22b",
        "10fa5aad-a0eb-432d-9181-a65892bbc0d5"
      ],
      "custom_css": [

      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "center",
        "color_scheme": "scheme-2",
        "padding_top_mobile": 40,
        "padding_top": 40,
        "padding_bottom_mobile": 45,
        "padding_bottom": 40,
        "page": "",
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "left",
        "padding_left_mobile": 5,
        "padding_left_desktop": 0,
        "padding_right_mobile": 5,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "e220512c-a571-4671-8a37-1717300712dd": {
      "type": "multicolumn",
      "blocks": {
        "6a490206-34e9-41c8-ab9a-b8ed772c9420": {
          "type": "column",
          "settings": {
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "{% assign terpene = shop.metaobjects.terpene_list.terpene-list-nlapbmjf.terpene_1 %}\n<div class=\"enjoy-shift-custom-multicolumn\">\n  {{ terpene.value.image | image_url: width: 750 | image_tag: class: 'bon-image', loading: 'lazy' }}\n  <div class=\"enjoy-shift-custom-multicolumn-content\">\n    <a href=\"{{ terpene.value.page_url.value }}\" style=\"text-decoration: none;\">\n      <div style=\"max-width: 115px;margin-bottom: 1rem;\">\n        {{ terpene.value.scientific_image_light | image_url: width: 400 | image_tag: class: 'bon-image', loading: 'lazy' }}\n      <\/div>\n      <h3>{{ terpene.value.title.value }}<\/h3>\n      <p class=\"h6 caption\" style=\"margin-bottom: 1rem;\">{{ terpene.value.scientific_name.value }}<\/p>\n      <p>\n        {{ terpene.value.short_description | metafield_tag }}\n      <\/p>\n    <\/a>\n    <a class=\"link\" href=\"{{ terpene.value.page_url.value }}\"> Learn More <\/a>\n  <\/div>\n<\/div>",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "limonene",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "e2ef1905-af4c-4831-9690-afe7ff559a0b": {
          "type": "column",
          "settings": {
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "{% assign terpene = shop.metaobjects.terpene_list.terpene-list-nlapbmjf.terpene_2 %}\n<div class=\"enjoy-shift-custom-multicolumn\">\n  {{ terpene.value.image | image_url: width: 750 | image_tag: class: 'bon-image', loading: 'lazy' }}\n  <div class=\"enjoy-shift-custom-multicolumn-content\">\n    <a href=\"{{ terpene.value.page_url.value }}\" style=\"text-decoration: none;\">\n      <div style=\"max-width: 115px;margin-bottom: 1rem;\">\n        {{ terpene.value.scientific_image_light | image_url: width: 400 | image_tag: class: 'bon-image', loading: 'lazy' }}\n      <\/div>\n      <h3>{{ terpene.value.title.value }}<\/h3>\n      <p class=\"h6 caption\" style=\"margin-bottom: 1rem;\">{{ terpene.value.scientific_name.value }}<\/p>\n      <p>\n        {{ terpene.value.short_description | metafield_tag }}\n      <\/p>\n    <\/a>\n    <a class=\"link\" href=\"{{ terpene.value.page_url.value }}\"> Learn More <\/a>\n  <\/div>\n<\/div>",
            "link_label": "",
            "link_label_liquid": "",
            "link": "{{ block.settings.page_popup.metafields.custom.terpene.value.page_url.value }}",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "pinene",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "cd97f109-d968-4393-baf4-9ee73ee5cf1d": {
          "type": "column",
          "settings": {
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "{% assign terpene = shop.metaobjects.terpene_list.terpene-list-nlapbmjf.terpene_3 %}\n<div class=\"enjoy-shift-custom-multicolumn\">\n  {{ terpene.value.image | image_url: width: 750 | image_tag: class: 'bon-image', loading: 'lazy' }}\n  <div class=\"enjoy-shift-custom-multicolumn-content\">\n    <a href=\"{{ terpene.value.page_url.value }}\" style=\"text-decoration: none;\">\n      <div style=\"max-width: 115px;margin-bottom: 1rem;\">\n        {{ terpene.value.scientific_image_light | image_url: width: 400 | image_tag: class: 'bon-image', loading: 'lazy' }}\n      <\/div>\n      <h3>{{ terpene.value.title.value }}<\/h3>\n      <p class=\"h6 caption\" style=\"margin-bottom: 1rem;\">{{ terpene.value.scientific_name.value }}<\/p>\n      <p>\n        {{ terpene.value.short_description | metafield_tag }}\n      <\/p>\n    <\/a>\n    <a class=\"link\" href=\"{{ terpene.value.page_url.value }}\"> Learn More <\/a>\n  <\/div>\n<\/div>",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "beta-caryophyllene",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "6a490206-34e9-41c8-ab9a-b8ed772c9420",
        "e2ef1905-af4c-4831-9690-afe7ff559a0b",
        "cd97f109-d968-4393-baf4-9ee73ee5cf1d"
      ],
      "custom_css": [

      ],
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-2",
        "color_scheme_content": "scheme-2",
        "background_style": "none",
        "content_width": "full-width",
        "content_position": "full-width",
        "columns_tablet": 2,
        "columns_desktop": 3,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "swipe_on_desktop": false,
        "image_width": "full",
        "image_ratio": "portrait",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h4",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": true,
        "page": "",
        "custom_css_class": "section--multicolumn--terpine",
        "custom_liquid": ""
      }
    },
    "32525b6e-7fd9-40f6-8b93-43097da0b7f0": {
      "type": "rich-text",
      "blocks": {
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-heading-1": {
          "type": "heading",
          "settings": {
            "heading": "LEARN MORE ABOUT TERPENES",
            "heading_size": "h4",
            "text_indent": ""
          }
        },
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-text-1": {
          "type": "text",
          "settings": {
            "text": "<p>Terpene research is still in its infancy, but researchers are quickly realizing the incredible benefits terpenes have on our body and mind. Dig into some of the science of shift.<\/p>",
            "text_indent": ""
          }
        },
        "385e7f3f-7223-4bf1-94dd-9fa7366a4fe3": {
          "type": "custom_liquid",
          "settings": {
            "title": "CSS Customizations",
            "custom_liquid": "<style>\n@media screen and (min-width: 750px) {\n#shopify-section-{{ section.id }} .rich-text__blocks {\n  display: grid;\n  grid-template-columns: 40% auto;\n  align-items: top;\n  justify-items: flex-start;\n}\n#shopify-section-{{ section.id }} .rich-text__blocks .rich-text__text {\n  margin-top: .5rem;\n}}\n<\/style>"
          }
        }
      },
      "block_order": [
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-heading-1",
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-text-1",
        "385e7f3f-7223-4bf1-94dd-9fa7366a4fe3"
      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "left",
        "color_scheme": "scheme-1",
        "padding_top_mobile": 55,
        "padding_top": 115,
        "padding_bottom_mobile": 30,
        "padding_bottom": 10,
        "page": "",
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "left",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "5d3a9773-a400-42cb-bc37-8e4fc09c0f14": {
      "type": "multicolumn",
      "blocks": {
        "template--16993423655157__5d3a9773-a400-42cb-bc37-8e4fc09c0f14-column-1": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/DTS_Daniel_Faro_Everyday_Objects_030_8ba325f7-ceae-407f-82d9-652701e46f93.jpg",
            "title": "",
            "title_liquid": "<div class=\"subtitle--small\">\nYoutube | The Cannabis Doc<\/div>\n<div class=\"bon-line-height-125\">\nLIMONENE, AKA THE HAPPY TERP<\/div>",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": true,
            "text_popup": "Watch",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "LIMONENE, AKA THE HAPPY TERP",
            "content_liquid_popup": "<iframe class=\"youtube-player\" width=\"1656\" height=\"869\" src=\"https:\/\/www.youtube.com\/embed\/c6EQ8SuRpPU?enablejsapi=1\" title=\"Limonene AKA The Happy Terp!\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" allowfullscreen><\/iframe>",
            "color_scheme_popup": "scheme-2",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "b8916090-4709-4c20-9299-e9c925639bc8": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/DTSyellowstone-3.jpg",
            "title": "",
            "title_liquid": "<div class=\"subtitle--small\">\nYoutube | DeBacco University\n<\/div>\n<div class=\"bon-line-height-125\">\nTerpene Guide to Pinene<\/div>",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": true,
            "text_popup": "Watch",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "TERPENE GUIDE TO PINENE",
            "content_liquid_popup": "<iframe class=\"youtube-player\" width=\"1656\" height=\"869\" src=\"https:\/\/www.youtube.com\/embed\/TgCoqaUDXRo&enablejsapi=1\" title=\"Terpene Guide to Pinene\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" allowfullscreen><\/iframe>",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "a6dd7275-6cf9-40d3-8b32-59db26309e46": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/cloves.jpg",
            "title": "",
            "title_liquid": "<div class=\"subtitle--small\">\nYoutube | Pepper Hernandez\n<\/div>\n<div class=\"bon-line-height-125\">\nCaryophyllene I Terpene<\/div>",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": true,
            "text_popup": "Watch",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "CARYOPHYLLENE I TERPENE",
            "content_liquid_popup": "<iframe class=\"youtube-player\" width=\"1656\" height=\"869\" src=\"https:\/\/www.youtube.com\/embed\/LyQX4O4AaFc?enablejsapi=1\" title=\"Caryophyllene I Terpene I Dr. Pepper Hernandez\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" allowfullscreen><\/iframe>",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "template--16993423655157__5d3a9773-a400-42cb-bc37-8e4fc09c0f14-column-1",
        "b8916090-4709-4c20-9299-e9c925639bc8",
        "a6dd7275-6cf9-40d3-8b32-59db26309e46"
      ],
      "custom_css": [
        "slider-component {padding: 0;}",
        ".multicolumn-card__info {padding-left: 0; padding-right: 0;}",
        ".multicolumn-card__image-wrapper {margin-left: 0; margin-right: 0;}"
      ],
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-1",
        "color_scheme_content": "scheme-1",
        "background_style": "none",
        "content_width": "page-width",
        "content_position": "full-width",
        "columns_tablet": 2,
        "columns_desktop": 3,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "swipe_on_desktop": false,
        "image_width": "full",
        "image_ratio": "adapt",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h6",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 10,
        "padding_bottom_mobile": 55,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 110,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": true,
        "page": "",
        "custom_css_class": "",
        "custom_liquid": "<script>\n  function pauseVideos() {\n    const iframeVideos = document.querySelectorAll('iframe');\n    for (const iframe of iframeVideos) {\n      if (iframe.src.includes('youtube.com')) {\n        iframe.contentWindow.postMessage('{\"event\":\"command\",\"func\":\"pauseVideo\",\"args\":\"\"}', '*');\n      }\n    }\n  }\n\n  \/\/ Attach click event listeners to elements with class product-popup-modal__toggle\n  const toggleElements = document.querySelectorAll('.product-popup-modal__toggle');\n  toggleElements.forEach(function (element) {\n    element.addEventListener('click', function () {\n      console.log('clicked button')\n      pauseVideos(); \/\/ Call pauseVideos function when any toggle element is clicked\n    });\n  });\n<\/script>"
      }
    },
    "5385453c-d8c3-4745-a7ff-532403151be1": {
      "type": "multicolumn",
      "blocks": {
        "template--16978522276085__43567a01-8cea-442e-a280-c73b09ba4a3b-column-2": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/NATEAPPEL_20231022_9839_1.jpg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "template--16978522276085__43567a01-8cea-442e-a280-c73b09ba4a3b-column-3": {
          "type": "column",
          "settings": {
            "image": "shopify:\/\/shop_images\/infused_sparkling_water_graphic.svg",
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "template--16978522276085__43567a01-8cea-442e-a280-c73b09ba4a3b-column-2",
        "template--16978522276085__43567a01-8cea-442e-a280-c73b09ba4a3b-column-3"
      ],
      "custom_css": [
        ".multicolumn-list__item,.multicolumn-card__image-wrapper {margin: 0;}",
        ".multicolumn-list {gap: 0;}",
        ".multicolumn-card__image {width: 100%; object-fit: cover;}",
        ".multicolumn-list:not(.slider),div.background-none .multicolumn-card__image-wrapper + .multicolumn-card__info {padding: 0;}"
      ],
      "settings": {
        "hide_size": "",
        "color_scheme_section": "",
        "color_scheme_content": "",
        "background_style": "none",
        "content_width": "full-width",
        "content_position": "full-width",
        "columns_tablet": 2,
        "columns_desktop": 2,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "swipe_on_desktop": false,
        "image_width": "full",
        "image_ratio": "adapt",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h4",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": false,
        "page": "",
        "custom_css_class": "",
        "custom_liquid": ""
      }
    }
  },
  "order": [
    "main",
    "206f7885-25cc-48c2-99f7-c3fcca4e026b",
    "29c55a61-24e7-44e4-8d27-fef87f1f2ee2",
    "0d02edaa-fb75-4711-bcc2-1ca5ae04a849",
    "88e09cbd-8324-44ba-84fc-79898b85b888",
    "e220512c-a571-4671-8a37-1717300712dd",
    "32525b6e-7fd9-40f6-8b93-43097da0b7f0",
    "5d3a9773-a400-42cb-bc37-8e4fc09c0f14",
    "5385453c-d8c3-4745-a7ff-532403151be1"
  ]
}
