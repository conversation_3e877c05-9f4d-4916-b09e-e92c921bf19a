{"sections": {"banner": {"type": "main-collection-banner", "disabled": true, "settings": {"show_collection_description": true, "show_collection_image": false, "color_scheme": "scheme-2", "content_alignment": "left", "mobile_content_alignment": "", "heading_size": "", "header_custom": "", "description_custom": ""}}, "product-grid": {"type": "main-collection-product-grid", "disabled": true, "settings": {"products_per_page": 16, "columns_desktop": 4, "color_scheme": "", "card_heading_font_size": "h5", "image_ratio": "", "image_shape": "", "show_secondary_image": "", "show_vendor": "", "show_rating": "", "quick_add": "none", "quick_add_behavior": "", "quick_add_button_style": "button button--primary", "show_color_swatches": "", "show_alternative_title": "", "show_card_product_custom_field": "", "enable_filtering": true, "filter_type": "horizontal", "enable_sorting": true, "show_count": true, "columns_mobile": "2"}}, "bfadc7f2-10d7-419e-92a4-90b8c79a84b9": {"type": "image-banner", "blocks": {"999a4904-71e6-4627-a3af-d2b14b851ddd": {"type": "heading", "settings": {"heading": "", "heading_liquid": "<span class=\"h4\">DownShift</span>", "heading_size": "h2"}}, "89185503-37c2-4255-b680-ddd4dd3309e4": {"type": "text", "settings": {"text": "Terpene infused sparkling water that leaves you feeling ready for tomorrow", "text_liquid": "", "text_style": "body"}}, "141fe001-8732-4c1f-b1e5-60b062df6519": {"type": "buttons", "settings": {"button_label_1": "DISCOVER", "button_label_1_liquid": "", "button_link_1": "shopify://products/downshift", "button_style": "button button--primary", "button_label_2": "", "button_label_2_liquid": "", "button_link_2": "", "button_style_2": "button button--primary"}}}, "block_order": ["999a4904-71e6-4627-a3af-d2b14b851ddd", "89185503-37c2-4255-b680-ddd4dd3309e4", "141fe001-8732-4c1f-b1e5-60b062df6519"], "custom_css": [], "settings": {"hide_size": "", "image": "shopify://shop_images/products_hero.png", "background_video": "", "image_height_desktop": "custom", "banner_minheight_desktop": 55, "banner_minheight_desktop_units": "vh", "desktop_content_position": "bottom-left", "desktop_content_alignment": "left", "show_text_box": false, "buttons_bottom_desktop": false, "background_video_mobile": "", "image_behavior": "none", "image_height_mobile": "large", "banner_minheight_mobile": 75, "banner_minheight_mobile_units": "vh", "mobile_content_alignment": "left", "show_text_below": false, "reverse_text_placement_mobile": false, "buttons_bottom_mobile": false, "overlay_gradient": "", "image_overlay_opacity_mobile": 30, "image_overlay_opacity": 0, "color_scheme": "scheme-2", "content_width": "full-width", "override_content_max_width": false, "content_max_width": 71, "content_max_width_desktop": 90, "padding_top_mobile": 0, "padding_bottom_mobile": 0, "padding_top_desktop": 0, "padding_bottom_desktop": 0, "color_scheme_section": "scheme-2", "custom_css_class": "", "custom_liquid": "<style>\n  #shopify-section-{{ section.id }} .banner__text {\n    max-width: 275px;\n  }\n  @media screen and (min-width: 990px) {\n    #shopify-section-{{ section.id }} .banner__text {\n      max-width: 350px;\n    }\n  }\n  .banner-container > div {\n    overflow: hidden;\n  }\n  .banner-container .banner__media img {\n    --zoom-in-ratio: 1;\n    transition: scale var(--duration-long) var(--ease-out-slow);\n  }\n  .banner-container > div:hover .banner__media img {\n    --zoom-in-ratio: 1.15;\n    scale: var(--zoom-in-ratio);\n  }\n  @media screen and (min-width: 750px) {\n    #shopify-section-{{ section.id }} .banner__box {\n      max-width: 275px;\n      padding: 0;\n    }\n  }\n  @media screen and (max-width: 749px) {\n    #shopify-section-{{ section.id }} .banner__box {\n      margin-top: auto;\n    }\n  }\n</style>", "page": ""}}, "cf8563f0-f93d-4142-a390-c58d7edc1817": {"type": "image-banner", "blocks": {"72da0720-4a1f-4320-a85e-17796942e68a": {"type": "heading", "settings": {"heading": "UPSHIFT", "heading_liquid": "<span class=\"h4\">UPSHIFT</span>", "heading_size": "h2"}}, "970b3dce-08c2-455a-ae32-0455bc8587c0": {"type": "text", "settings": {"text": "Terpene infused sparkling water that energizes you for today", "text_liquid": "", "text_style": "body"}}, "5ef9ddc2-f86d-4650-968b-a2b2d2ee2587": {"type": "buttons", "settings": {"button_label_1": "Coming Soon", "button_label_1_liquid": "", "button_link_1": "", "button_style": "button button--secondary", "button_label_2": "", "button_label_2_liquid": "", "button_link_2": "", "button_style_2": "button button--primary"}}}, "block_order": ["72da0720-4a1f-4320-a85e-17796942e68a", "970b3dce-08c2-455a-ae32-0455bc8587c0", "5ef9ddc2-f86d-4650-968b-a2b2d2ee2587"], "disabled": true, "settings": {"hide_size": "", "image": "shopify://shop_images/upshift_hero.png", "background_video": "", "image_height_desktop": "custom", "banner_minheight_desktop": 55, "banner_minheight_desktop_units": "vh", "desktop_content_position": "bottom-left", "desktop_content_alignment": "left", "show_text_box": false, "buttons_bottom_desktop": false, "background_video_mobile": "", "image_behavior": "none", "image_height_mobile": "large", "banner_minheight_mobile": 75, "banner_minheight_mobile_units": "vh", "mobile_content_alignment": "left", "show_text_below": false, "reverse_text_placement_mobile": false, "buttons_bottom_mobile": false, "overlay_gradient": "", "image_overlay_opacity_mobile": 20, "image_overlay_opacity": 0, "color_scheme": "scheme-1", "content_width": "full-width", "override_content_max_width": false, "content_max_width": 71, "content_max_width_desktop": 90, "padding_top_mobile": 0, "padding_bottom_mobile": 0, "padding_top_desktop": 0, "padding_bottom_desktop": 0, "color_scheme_section": "scheme-1", "custom_css_class": "", "custom_liquid": "<style>\n  #shopify-section-{{ section.id }} .banner__text {\n    max-width: 275px;\n  }\n  @media screen and (min-width: 990px) {\n    #shopify-section-{{ section.id }} .banner__text {\n      max-width: 350px;\n    }\n  }\n  @media screen and (min-width: 750px) {\n    #shopify-section-{{ section.id }} {\n      display: inline-block;\n      width: 50%;\n    }\n    #shopify-section-{{ section.id }} .banner-container.page-width {\n      max-width: calc(var(--page-width) / 2);\n      padding: 0;\n      margin-right: 0;\n    }\n    #shopify-section-{{ section.id }} .banner__box {\n      padding: 0;\n    }\n  }\n@media screen and (max-width: 749px) {\n  #shopify-section-{{ section.id }} .banner__box {\n    margin-top: auto;\n  }\n}\n</style>", "page": ""}}, "bd6aacb2-2132-487f-9369-0a2e3767366c": {"type": "image-banner", "blocks": {"50e2dd39-5ce2-46c5-90a2-ea1843ee5fe7": {"type": "heading", "settings": {"heading": "", "heading_liquid": "<span class=\"h4\">DREAMSHIFT</span>", "heading_size": "h2"}}, "87c5be86-7f74-443c-9cff-17b6dc564c15": {"type": "text", "settings": {"text": "Terpene infused sparkling water that leaves you feeling ready for tomorrow", "text_liquid": "", "text_style": "body"}}, "2e98abe9-aba7-4f84-bbd1-050924a495b8": {"type": "buttons", "settings": {"button_label_1": "Coming Soon", "button_label_1_liquid": "", "button_link_1": "", "button_style": "button button--secondary", "button_label_2": "", "button_label_2_liquid": "", "button_link_2": "", "button_style_2": "button button--primary"}}}, "block_order": ["50e2dd39-5ce2-46c5-90a2-ea1843ee5fe7", "87c5be86-7f74-443c-9cff-17b6dc564c15", "2e98abe9-aba7-4f84-bbd1-050924a495b8"], "disabled": true, "settings": {"hide_size": "", "image": "shopify://shop_images/dreamshift_hero.png", "background_video": "", "image_height_desktop": "custom", "banner_minheight_desktop": 55, "banner_minheight_desktop_units": "vh", "desktop_content_position": "bottom-left", "desktop_content_alignment": "left", "show_text_box": false, "buttons_bottom_desktop": false, "background_video_mobile": "", "image_behavior": "none", "image_height_mobile": "large", "banner_minheight_mobile": 75, "banner_minheight_mobile_units": "vh", "mobile_content_alignment": "left", "show_text_below": false, "reverse_text_placement_mobile": false, "buttons_bottom_mobile": false, "overlay_gradient": "", "image_overlay_opacity_mobile": 20, "image_overlay_opacity": 0, "color_scheme": "scheme-2", "content_width": "full-width", "override_content_max_width": false, "content_max_width": 71, "content_max_width_desktop": 90, "padding_top_mobile": 0, "padding_bottom_mobile": 0, "padding_top_desktop": 0, "padding_bottom_desktop": 0, "color_scheme_section": "scheme-1", "custom_css_class": "", "custom_liquid": "<style>\n  #shopify-section-{{ section.id }} .banner__text {\n    max-width: 275px;\n  }\n  @media screen and (min-width: 990px) {\n    #shopify-section-{{ section.id }} .banner__text {\n      max-width: 350px;\n    }\n  }\n  @media screen and (min-width: 750px) {\n    #shopify-section-{{ section.id }} {\n      display: inline-block;\n      width: 50%;\n    }\n    #shopify-section-{{ section.id }} .banner-container.page-width {\n      max-width: calc(var(--page-width) / 2);\n      padding: 0;\n      margin-left: 0;\n    }\n    #shopify-section-{{ section.id }} .banner__box {\n      padding: 0;\n    }\n  }\n  @media screen and (max-width: 749px) {\n    #shopify-section-{{ section.id }} .banner__box {\n      margin-top: auto;\n    }\n  }\n</style>", "page": ""}}, "83ebb0de-8fbb-4796-9c0b-392b774a3c1d": {"type": "rich-text", "blocks": {"d3ac2634-b36f-4152-a893-504aec6fd89d": {"type": "custom_liquid", "settings": {"title": "subtitle", "custom_liquid": "<p class=\"caption bon-highlight-color--light bon-text-justify bon-line-height-125\" style=\"margin: auto;text-align: center;\">\n<span>\nSUBSCRIBE & SAVE\n</span>\n</p>"}}, "3c22fbd1-d301-4c6d-a86c-accc1e88fee7": {"type": "heading", "settings": {"heading": "RELAXATION DELIVERED TO YOUR DOORSTEP", "heading_size": "h3", "text_indent": ""}}}, "block_order": ["d3ac2634-b36f-4152-a893-504aec6fd89d", "3c22fbd1-d301-4c6d-a86c-accc1e88fee7"], "settings": {"desktop_content_position": "full-width", "content_alignment": "center", "color_scheme": "scheme-2", "padding_top_mobile": 65, "padding_top": 155, "padding_bottom_mobile": 35, "padding_bottom": 35, "page": "", "hide_size": "", "show_color_scheme_content": false, "color_scheme_content": "", "content_width": "page-width custom-width", "content_max_width_tablet": 900, "content_max_width_desktop": 550, "mobile_content_alignment": "", "padding_left_mobile": 0, "padding_left_desktop": 0, "padding_right_mobile": 0, "padding_right_desktop": 0, "custom_css_class": "", "custom_liquid": ""}}, "ae6e773b-fcc0-4e3d-8a94-8f95456d4b7e": {"type": "multicolumn", "blocks": {"template--21493599568188__ae6e773b-fcc0-4e3d-8a94-8f95456d4b7e-column-1": {"type": "column", "settings": {"image": "shopify://shop_images/ten-percent_2c4eff29-4b8c-49d2-85f5-b8336d9b7767.svg", "title": "10% OFF EVERY ORDER", "title_liquid": "", "text": "<p>Unlock exclusive savings with our Subscribe & Save program: Enjoy a 10% discount on every 12-pack and 24-pack of DownShift, ensuring you never miss a moment to unwind.</p>", "text_liquid": "", "link_label": "", "link_label_liquid": "", "link": "", "button_style": "link", "show_popup": false, "text_popup": "Link label", "text_liquid_popup": "", "button_style_popup": "link underlined-link", "page_popup": "", "title_popup": "", "content_liquid_popup": "", "color_scheme_popup": "", "colomn_span_amount": "grid__item--span-1", "colomn_span_amount_tablet": "grid__item--span-1-tablet", "colomn_span_amount_desktop": "grid__item--span-1-desktop"}}, "template--21493599568188__ae6e773b-fcc0-4e3d-8a94-8f95456d4b7e-column-2": {"type": "column", "settings": {"image": "shopify://shop_images/shipping.svg", "title": "SAVE ON SHIPPING", "title_liquid": "", "text": "<p>Experience the ultimate convenience as a subscriber: Enjoy free shipping on every DownShift order, making your path to natural vitality more accessible and affordable.</p>", "text_liquid": "", "link_label": "", "link_label_liquid": "", "link": "", "button_style": "link", "show_popup": false, "text_popup": "Link label", "text_liquid_popup": "", "button_style_popup": "link underlined-link", "page_popup": "", "title_popup": "", "content_liquid_popup": "", "color_scheme_popup": "", "colomn_span_amount": "grid__item--span-1", "colomn_span_amount_tablet": "grid__item--span-1-tablet", "colomn_span_amount_desktop": "grid__item--span-1-desktop"}}, "template--21493599568188__ae6e773b-fcc0-4e3d-8a94-8f95456d4b7e-column-3": {"type": "column", "settings": {"image": "shopify://shop_images/skip.svg", "title": "SKIP/CANCEL ANY TIME", "title_liquid": "", "text": "<p>Flexibility at your fingertips: With our subscription, you have the freedom to Shift- skip or cancel your orders at any time, ensuring our service aligns perfectly with your lifestyle and needs.</p>", "text_liquid": "", "link_label": "", "link_label_liquid": "", "link": "", "button_style": "link", "show_popup": false, "text_popup": "Link label", "text_liquid_popup": "", "button_style_popup": "link underlined-link", "page_popup": "", "title_popup": "", "content_liquid_popup": "", "color_scheme_popup": "", "colomn_span_amount": "grid__item--span-1", "colomn_span_amount_tablet": "grid__item--span-1-tablet", "colomn_span_amount_desktop": "grid__item--span-1-desktop"}}}, "block_order": ["template--21493599568188__ae6e773b-fcc0-4e3d-8a94-8f95456d4b7e-column-1", "template--21493599568188__ae6e773b-fcc0-4e3d-8a94-8f95456d4b7e-column-2", "template--21493599568188__ae6e773b-fcc0-4e3d-8a94-8f95456d4b7e-column-3"], "settings": {"hide_size": "", "color_scheme_section": "scheme-2", "color_scheme_content": "scheme-2", "background_style": "none", "content_width": "page-width", "content_position": "full-width", "columns_tablet": 2, "columns_desktop": 3, "columns_mobile": "1", "swipe_on_mobile": false, "swipe_on_desktop": false, "image_width": "two-third", "image_ratio": "adapt", "column_alignment_mobile": "center", "column_alignment_desktop": "center", "heading_size": "h5", "button_label": "Subscribe", "button_label_liquid": "", "button_link": "shopify://products/downshift", "button_style": "button button--primary", "padding_top_mobile": 0, "padding_bottom_mobile": 65, "padding_left_mobile": 10, "padding_right_mobile": 10, "padding_top_desktop": 0, "padding_bottom_desktop": 155, "padding_left_desktop": 0, "padding_right_desktop": 0, "link_image": false, "page": "", "custom_css_class": "", "custom_liquid": ""}}, "d8666e20-a71f-40e1-b978-644d8ec4f4b7": {"type": "rich-text", "blocks": {"template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-heading-1": {"type": "heading", "settings": {"heading": "Find us", "heading_size": "h1", "text_indent": ""}}, "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-text-1": {"type": "text", "settings": {"text": "<p>All products are subject to availability. Please contact your retailer to ensure products are in stock. Downshift is typically sold with refrigerated beverages.</p>", "text_indent": ""}}}, "block_order": ["template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-heading-1", "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-text-1"], "settings": {"desktop_content_position": "full-width", "content_alignment": "left", "color_scheme": "scheme-1", "padding_top_mobile": 55, "padding_top": 40, "padding_bottom_mobile": 20, "padding_bottom": 40, "page": "", "hide_size": "", "show_color_scheme_content": false, "color_scheme_content": "", "content_width": "page-width", "content_max_width_tablet": 900, "content_max_width_desktop": 1600, "mobile_content_alignment": "center", "padding_left_mobile": 0, "padding_left_desktop": 0, "padding_right_mobile": 0, "padding_right_desktop": 0, "custom_css_class": "", "custom_liquid": "<style>\n@media screen and (min-width: 750px) {\n#shopify-section-{{ section.id }} .rich-text__blocks {\n  display: grid;\n  grid-template-columns: 40% auto;\n  align-items: top;\n  justify-items: flex-start;\n}\n#shopify-section-{{ section.id }} .rich-text__blocks .rich-text__text {\n  margin-top: .5rem;\n}}\n</style>"}}, "72e84780-8f4c-429b-a8e7-442b2137b76d": {"type": "custom-liquid", "settings": {"description": "Map embed", "custom_liquid": "<div class=\"page-width\">\n  <!-- Start Stockist.co widget -->\n<div data-stockist-widget-tag=\"u20018\">Loading store locator\nfrom <a href=\"https://stockist.co\">Stockist store locator</a>...</div>\n<script>\n  (function(s,t,o,c,k){c=s.createElement(t);c.src=o;c.async=1;\n  k=s.getElementsByTagName(t)[0];k.parentNode.insertBefore(c,k);\n  })(document,'script','//stockist.co/embed/v1/widget.min.js');\n</script>\n<!-- End Stockist.co widget -->\n</div>", "hide_size": "", "color_scheme": "scheme-1", "padding_top_mobile": 10, "padding_bottom_mobile": 55, "padding_top_desktop": 55, "padding_bottom_desktop": 115}}, "be72e2d3-6326-4363-9f16-7191aad62fa9": {"type": "image-banner", "blocks": {"f9143142-ec55-41cb-bf50-5711aa57ca96": {"type": "custom_liquid", "disabled": true, "settings": {"title": "My custom liquid", "custom_liquid": "<style>\n  #shopify-section-{{ section.id }} .banner {\n   overflow: hidden;\n  }\n  .bon-collection-text-row__container {\n    display: grid;\n    justify-items: center;\n  }\n  .bon-collection-text-row {\n    color: #2B2D1F;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    gap: 5rem;\n    position: absolute;\n    overflow: hidden;\n    top: -110%;\n    text-transform: uppercase;\n  }\n  .bon-collection-text-row > * {\n    text-wrap: nowrap;\n    display: inline;\n  }\n\n  @media screen and (min-width: 750px) {\n    .bon-collection-text-row {\n      top: -10%;\n    }\n  }\n</style>\n\n<div class=\"page-width\">\n  <div class=\"bon-collection-text-row__container\">\n    <div class=\"bon-collection-text-row\">\n      <div>\n        10MG TERPENES\n      </div>\n      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"15\" height=\"15\" viewBox=\"0 0 15 15\" fill=\"none\">\n        <g clip-path=\"url(#clip0_174_2033)\">\n          <path d=\"M0 0L7.47 5.5L14.94 0L9.44 7.47L14.94 14.94L7.47 9.44L0 14.94L5.5 7.47L0 0Z\" fill=\"#2B2D1F\"/>\n        </g>\n        <defs>\n          <clipPath id=\"clip0_174_2033\">\n            <rect width=\"14.94\" height=\"14.94\" fill=\"white\"/>\n          </clipPath>\n        </defs>\n      </svg>\n      <div>\n        10 MG CBD\n      </div>\n      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"15\" height=\"15\" viewBox=\"0 0 15 15\" fill=\"none\">\n        <g clip-path=\"url(#clip0_174_2033)\">\n          <path d=\"M0 0L7.47 5.5L14.94 0L9.44 7.47L14.94 14.94L7.47 9.44L0 14.94L5.5 7.47L0 0Z\" fill=\"#2B2D1F\"/>\n        </g>\n        <defs>\n          <clipPath id=\"clip0_174_2033\">\n            <rect width=\"14.94\" height=\"14.94\" fill=\"white\"/>\n          </clipPath>\n        </defs>\n      </svg>\n      <div>\n        2MG THC\n      </div>\n      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"15\" height=\"15\" viewBox=\"0 0 15 15\" fill=\"none\">\n        <g clip-path=\"url(#clip0_174_2033)\">\n          <path d=\"M0 0L7.47 5.5L14.94 0L9.44 7.47L14.94 14.94L7.47 9.44L0 14.94L5.5 7.47L0 0Z\" fill=\"#2B2D1F\"/>\n        </g>\n        <defs>\n          <clipPath id=\"clip0_174_2033\">\n            <rect width=\"14.94\" height=\"14.94\" fill=\"white\"/>\n          </clipPath>\n        </defs>\n      </svg>\n      <div>\n        SUGAR FREE\n      </div>\n      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"15\" height=\"15\" viewBox=\"0 0 15 15\" fill=\"none\">\n        <g clip-path=\"url(#clip0_174_2033)\">\n          <path d=\"M0 0L7.47 5.5L14.94 0L9.44 7.47L14.94 14.94L7.47 9.44L0 14.94L5.5 7.47L0 0Z\" fill=\"#2B2D1F\"/>\n        </g>\n        <defs>\n          <clipPath id=\"clip0_174_2033\">\n            <rect width=\"14.94\" height=\"14.94\" fill=\"white\"/>\n          </clipPath>\n        </defs>\n      </svg>\n      <div>\n        VEGAN\n      </div>\n    </div>\n  </div>\n</div>"}}, "faf6be0b-99f7-4f5d-916a-5356d172d517": {"type": "text", "settings": {"text": "", "text_liquid": "<div class=\"h4\" style=\"color: rgb(var(--bon-highlight-color--light));\">\n#SHIFTWITHNATURE\n</div>", "text_style": "subtitle"}}, "36129f3e-fa45-4680-bb3a-6220f9e35e48": {"type": "heading", "settings": {"heading": "UNWIND FROM THE GRIND", "heading_liquid": "", "heading_size": "h1"}}, "dec4f7e0-acad-44b3-b5e8-46f4dce5c573": {"type": "buttons", "settings": {"button_label_1": "Buy & Unwind", "button_label_1_liquid": "", "button_link_1": "shopify://products/downshift", "button_style": "button button--primary", "button_label_2": "", "button_label_2_liquid": "", "button_link_2": "", "button_style_2": "button button--primary"}}}, "block_order": ["f9143142-ec55-41cb-bf50-5711aa57ca96", "faf6be0b-99f7-4f5d-916a-5356d172d517", "36129f3e-fa45-4680-bb3a-6220f9e35e48", "dec4f7e0-acad-44b3-b5e8-46f4dce5c573"], "custom_css": [".banner:after {background: linear-gradient( 180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100% ); opacity: 1;}"], "settings": {"hide_size": "", "image": "shopify://shop_images/NATEAPPEL_20231022_8983.jpg", "background_video": "", "image_height_desktop": "custom", "banner_minheight_desktop": 85, "banner_minheight_desktop_units": "vh", "desktop_content_position": "middle-center", "desktop_content_alignment": "center", "show_text_box": false, "buttons_bottom_desktop": false, "background_video_mobile": "", "image_behavior": "none", "image_height_mobile": "medium", "banner_minheight_mobile": 75, "banner_minheight_mobile_units": "vh", "mobile_content_alignment": "center", "show_text_below": false, "reverse_text_placement_mobile": false, "buttons_bottom_mobile": false, "overlay_gradient": "", "image_overlay_opacity_mobile": 0, "image_overlay_opacity": 0, "color_scheme": "scheme-2", "content_width": "full-width", "override_content_max_width": false, "content_max_width": 71, "content_max_width_desktop": 90, "padding_top_mobile": 0, "padding_bottom_mobile": 0, "padding_top_desktop": 0, "padding_bottom_desktop": 0, "color_scheme_section": "scheme-1", "custom_css_class": "", "custom_liquid": "<style>\n#shopify-section-{{ section.id }} .banner-container > div:hover .banner__media img {\n transition: none;\n scale: 1;\n}\n</style>", "page": ""}}}, "order": ["banner", "product-grid", "bfadc7f2-10d7-419e-92a4-90b8c79a84b9", "cf8563f0-f93d-4142-a390-c58d7edc1817", "bd6aacb2-2132-487f-9369-0a2e3767366c", "83ebb0de-8fbb-4796-9c0b-392b774a3c1d", "ae6e773b-fcc0-4e3d-8a94-8f95456d4b7e", "d8666e20-a71f-40e1-b978-644d8ec4f4b7", "72e84780-8f4c-429b-a8e7-442b2137b76d", "be72e2d3-6326-4363-9f16-7191aad62fa9"]}