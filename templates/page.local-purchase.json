{"sections": {"main": {"type": "main-page", "disabled": true, "settings": {"heading_size": "h2", "padding_top_mobile": 36, "padding_bottom_mobile": 36, "padding_top_desktop": 56, "padding_bottom_desktop": 56}}, "image_banner_77HUne": {"type": "image-banner", "blocks": {"heading_nDBqjV": {"type": "heading", "settings": {"heading": "For Locals, By Locals.", "heading_liquid": "", "heading_size": "h1"}}, "text_eTceTn": {"type": "text", "settings": {"text": "", "text_liquid": "<span class=\"h6\" style=\"max-width: 580px; margin: auto; padding-left: 1px;\">A special offer for our neighbors. build your perfect Shift Bundle and save with local pickup.</span>", "text_style": "body"}}, "buttons_pGTbhc": {"type": "buttons", "settings": {"button_label_1": "Build Your Bundle", "button_label_1_liquid": "", "button_link_1": "#build", "button_style": "button button--primary", "button_label_2": "", "button_label_2_liquid": "", "button_link_2": "", "button_style_2": "button button--primary"}}}, "block_order": ["heading_nDBqjV", "text_eTceTn", "buttons_pGTbhc"], "name": "t:sections.image-banner.presets.name", "settings": {"hide_size": "", "image": "shopify://shop_images/OLD_MILL_MEDIUM_RES_JONATHON_CHANDLER_4-medium.jpg", "background_video": "", "native_background_video": "shopify://files/videos/Bend Background.mp4", "image_height_desktop": "medium", "banner_minheight_desktop": 75, "banner_minheight_desktop_units": "vh", "desktop_content_position": "middle-left", "desktop_content_alignment": "left", "show_text_box": true, "buttons_bottom_desktop": false, "image_mobile": "shopify://shop_images/OLD_MILL_MEDIUM_RES_JONATHON_CHANDLER_4-medium.jpg", "background_video_mobile": "", "image_behavior": "none", "image_height_mobile": "medium", "banner_minheight_mobile": 75, "banner_minheight_mobile_units": "vh", "mobile_content_alignment": "center", "show_text_below": true, "reverse_text_placement_mobile": false, "buttons_bottom_mobile": false, "overlay_gradient": "", "image_overlay_opacity_mobile": 0, "image_overlay_opacity": 0, "color_scheme": "scheme-2", "content_width": "full-width", "override_content_max_width": false, "content_max_width": 71, "content_max_width_desktop": 90, "padding_top_mobile": 0, "padding_bottom_mobile": 0, "padding_top_desktop": 0, "padding_bottom_desktop": 0, "color_scheme_section": "", "custom_css_class": "", "custom_liquid": "", "page": ""}}, "rich_text_WkMcEH": {"type": "rich-text", "blocks": {"heading_HK4wGK": {"type": "heading", "settings": {"heading": "How it works", "heading_size": "h1", "text_indent": ""}}, "text_XJfDD8": {"type": "text", "settings": {"text": "<p>Proud to be made in Bend, and we want to share the perfect way to unwind with everyone.</p>", "text_indent": ""}}}, "block_order": ["heading_HK4wGK", "text_XJfDD8"], "name": "t:sections.rich-text.presets.name", "settings": {"desktop_content_position": "full-width", "content_alignment": "center", "color_scheme": "", "padding_top_mobile": 20, "padding_top": 40, "padding_bottom_mobile": 20, "padding_bottom": 40, "page": "", "hide_size": "", "show_color_scheme_content": false, "color_scheme_content": "", "content_width": "page-width", "content_max_width_tablet": 900, "content_max_width_desktop": 1600, "mobile_content_alignment": "", "padding_left_mobile": 0, "padding_left_desktop": 0, "padding_right_mobile": 0, "padding_right_desktop": 0, "custom_css_class": "", "custom_liquid": ""}}, "multicolumn_iKQYgW": {"type": "multicolumn", "blocks": {"column_PPBWiQ": {"type": "column", "settings": {"title": "Build Your Order", "title_liquid": "", "text": "<p>Pick from our signature DownShift, our new Cocktails, or a combination of both in your favorite flavors.</p>", "text_liquid": "", "link_label": "", "link_label_liquid": "", "link": "", "button_style": "link", "show_popup": false, "text_popup": "Link label", "text_liquid_popup": "", "button_style_popup": "link underlined-link", "page_popup": "", "title_popup": "", "content_liquid_popup": "", "color_scheme_popup": "", "colomn_span_amount": "grid__item--span-1", "colomn_span_amount_tablet": "grid__item--span-1-tablet", "colomn_span_amount_desktop": "grid__item--span-1-desktop"}}, "column_MGnpDK": {"type": "column", "settings": {"title": "Confirm Your Sizes", "title_liquid": "", "text": "<p>Make sure you select the number of cans of each product you want before you complete your purchase.</p>", "text_liquid": "", "link_label": "", "link_label_liquid": "", "link": "", "button_style": "link", "show_popup": false, "text_popup": "Link label", "text_liquid_popup": "", "button_style_popup": "link underlined-link", "page_popup": "", "title_popup": "", "content_liquid_popup": "", "color_scheme_popup": "", "colomn_span_amount": "grid__item--span-1", "colomn_span_amount_tablet": "grid__item--span-1-tablet", "colomn_span_amount_desktop": "grid__item--span-1-desktop"}}, "column_qihPQd": {"type": "column", "settings": {"title": "Pickup Your Order", "title_liquid": "", "text": "<p>We've set up locally at the <a href=\"https://nwxfarmersmarket.com/\" target=\"_blank\" title=\"https://nwxfarmersmarket.com/\">NorthWest Crossing Saturday Farmers Market</a> and will have your order ready for pickup! The perfect way to unwind this Summer.</p>", "text_liquid": "", "link_label": "", "link_label_liquid": "", "link": "", "button_style": "link", "show_popup": false, "text_popup": "Link label", "text_liquid_popup": "", "button_style_popup": "link underlined-link", "page_popup": "", "title_popup": "", "content_liquid_popup": "", "color_scheme_popup": "", "colomn_span_amount": "grid__item--span-1", "colomn_span_amount_tablet": "grid__item--span-1-tablet", "colomn_span_amount_desktop": "grid__item--span-1-desktop"}}}, "block_order": ["column_PPBWiQ", "column_MGnpDK", "column_qihPQd"], "name": "t:sections.multicolumn.presets.name", "settings": {"hide_size": "", "color_scheme_section": "", "color_scheme_content": "", "background_style": "primary", "content_width": "page-width", "content_position": "full-width", "columns_tablet": 2, "columns_desktop": 3, "columns_mobile": "1", "swipe_on_mobile": false, "swipe_on_desktop": false, "image_width": "full", "image_ratio": "adapt", "column_alignment_mobile": "left", "column_alignment_desktop": "left", "heading_size": "h4", "button_label": "Start My Order", "button_label_liquid": "", "button_link": "#build", "button_style": "button button--primary", "padding_top_mobile": 0, "padding_bottom_mobile": 0, "padding_left_mobile": 0, "padding_right_mobile": 0, "padding_top_desktop": 0, "padding_bottom_desktop": 50, "padding_left_desktop": 0, "padding_right_desktop": 0, "link_image": false, "page": "", "custom_css_class": "", "custom_liquid": "<div id=\"build\">\n  <!-- empty -->\n</div>"}}, "featured_collection_wBLYKa": {"type": "featured-collection", "blocks": {"featured_collection_a7gdCH": {"type": "featured_collection", "settings": {"collection": "", "product_list": ["downshift-lime-citrus-local-pickup", "downshift-trail-berry-local-pickup", "downshift-wild-tangerine-local-pickup", "shift-ginger-sour-local-pickup", "shift-margarita-local-pickup", "shift-paloma-local-pickup"], "product_tags": "", "title": "Featured collection", "title_liquid": "", "description": "", "show_description": false}}}, "block_order": ["featured_collection_a7gdCH"], "custom_css": [], "name": "t:sections.featured-collection.presets.name", "settings": {"hide_size": "", "color_scheme": "", "text_alignment_mobile": "left", "columns_mobile": "2", "swipe_on_mobile": false, "padding_bottom_mobile": 50, "text_alignment_desktop": "left", "columns_desktop": 3, "enable_desktop_slider": false, "full_width": false, "padding_bottom_desktop": 100, "title": "", "heading_size": "h1", "description": "", "description_style": "body", "tab_link_size": "", "products_to_show": 8, "view_all_style": "no", "hide_cart_products": false, "quick_add": "none", "quick_add_button_style": "button button--primary", "image_ratio": "", "image_shape": "", "show_secondary_image": "", "card_heading_font_size": "", "show_vendor": "", "show_rating": "", "quick_add_behavior": "", "show_alternative_title": "", "show_card_product_custom_field": "", "custom_css_class": "", "custom_liquid": ""}}, "rich_text_GgLWAn": {"type": "rich-text", "blocks": {"heading_36P3RU": {"type": "heading", "settings": {"heading": "Where to find us", "heading_size": "h1", "text_indent": ""}}, "text_7VMwFR": {"type": "text", "settings": {"text": "<p>Find us at the <a href=\"https://nwxfarmersmarket.com/\" target=\"_blank\" title=\"https://nwxfarmersmarket.com/\">NorthWest Crossing Saturday Farmers Market</a> to pick up your order.</p><p><strong>Need help?</strong> Contact us at <a href=\"mailto:<EMAIL>\" title=\"mailto:<EMAIL>\"><EMAIL></a></p>", "text_indent": ""}}}, "block_order": ["heading_36P3RU", "text_7VMwFR"], "name": "t:sections.rich-text.presets.name", "settings": {"desktop_content_position": "full-width", "content_alignment": "center", "color_scheme": "", "padding_top_mobile": 20, "padding_top": 40, "padding_bottom_mobile": 20, "padding_bottom": 40, "page": "", "hide_size": "", "show_color_scheme_content": false, "color_scheme_content": "", "content_width": "page-width", "content_max_width_tablet": 900, "content_max_width_desktop": 1600, "mobile_content_alignment": "", "padding_left_mobile": 0, "padding_left_desktop": 0, "padding_right_mobile": 0, "padding_right_desktop": 0, "custom_css_class": "", "custom_liquid": ""}}, "custom_liquid_tBaRXy": {"type": "custom-liquid", "name": "t:sections.custom-liquid.presets.name", "settings": {"description": "Google Map", "custom_liquid": "<div style=\"margin: 0 auto;text-align: center;\">\n<iframe class=\"gmap\" style=\"width: 100%\" src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1433.5956032918016!2d-121.35698624304273!3d44.05876019274127!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x54b8c86ea7d8fe49%3A0x30677f78434c1751!2sNorthwest%20Crossing%20Saturday%20Farmers%20Market!5e0!3m2!1sen!2sus!4v1748575536005!5m2!1sen!2sus\" height=\"650\" style=\"border:0;\" allowfullscreen=\"\" loading=\"lazy\" referrerpolicy=\"no-referrer-when-downgrade\"></iframe>\n</div>\n\n<style>\n.gmap {\n    -webkit-filter: grayscale(100%);\n       -moz-filter: grayscale(100%);\n        -ms-filter: grayscale(100%);\n         -o-filter: grayscale(100%);\n            filter: grayscale(100%);\n}\n</style>", "hide_size": "", "color_scheme": "", "padding_top_mobile": 0, "padding_bottom_mobile": 0, "padding_top_desktop": 0, "padding_bottom_desktop": 0}}}, "order": ["main", "image_banner_77HUne", "rich_text_WkMcEH", "multicolumn_iKQYgW", "featured_collection_wBLYKa", "rich_text_GgLWAn", "custom_liquid_tBaRXy"]}