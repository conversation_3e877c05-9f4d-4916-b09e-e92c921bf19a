{%- liquid
  assign make_collapsible_row_mobile = false
  if block.type == 'link_list' and block.settings.make_collapsible_row and block.settings.heading_liquid != blank or block.settings.heading != blank
    assign make_collapsible_row_mobile = true
  endif
-%}

<div id="FooterBlock-{{ block.id }}-{{ section.id }}" 
  class="footer-block grid__item {{ block.settings.colomn_span_amount }} {{ block.settings.colomn_span_amount_tablet }} {{ block.settings.colomn_span_amount_desktop }} {% if make_collapsible_row_mobile %} footer-block--menu{% endif %} content--mobile-{{ block.settings.content_alignment_mobile }} content--desktop-{{ block.settings.content_alignment_desktop }} {{ block.settings.hide_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %} footer-block--{{ block.type | handleize }}"
  {{ block.shopify_attributes }}
  {% if settings.animations_reveal_on_scroll %}
    data-cascade
    style="--animation-order: {{ forloop.index }};"
  {% endif %}
>
  {%- if block.settings.heading_liquid != blank or block.settings.heading != blank -%}
    <div class="footer-block__heading inline-richtext {% if make_collapsible_row_mobile %}small-hide medium-hide{% endif %} h2">
      {{- block.settings.heading_liquid | default: block.settings.heading -}}
    </div>
  {%- endif -%}

  {%- case block.type -%}
    {%- when '@app' -%}
      {% render block %}

    {%- when 'custom_liquid' -%}
      {{ block.settings.custom_liquid }}

    {%- when 'text' -%}
      <div class="footer-block__details-content rte content--mobile-{{ block.settings.content_alignment_mobile }} content--desktop-{{ block.settings.content_alignment_desktop }}">
        {{ block.settings.subtext_liquid | default: block.settings.subtext }}
      </div>

    {%- when 'link_list' -%}
      <ul class="footer-block__details-content list-unstyled {% if make_collapsible_row_mobile %}small-hide medium-hide{% endif %}">
        {%- capture menu_items -%}
          {%- for link in block.settings.menu.links -%}
            <li>
              <a
                href="{{ link.url }}"
                class="link link--text list-menu__item list-menu__item--link{% if link.active %} list-menu__item--active{% endif %}"
              >
                {{ link.title | escape }}
              </a>
            </li>
          {%- endfor -%}
        {%- endcapture -%}
        {{- menu_items -}}
      </ul>
      
      {%- if make_collapsible_row_mobile -%}
        {{ 'component-accordion.css' | asset_url | stylesheet_tag }}
        {{ 'collapsible-content.css' | asset_url | stylesheet_tag }}

        <div class="accordion large-up-hide">
          <details id="Details-{{ block.id }}-{{ section.id }}">
            <summary id="Summary-{{ block.id }}-{{ section.id }}">
              <div class="accordion__title inline-richtext footer-block__heading h3">
                  {{- block.settings.heading_liquid | default: block.settings.heading -}}
              </div>
              {{- 'icon-caret.svg' | inline_asset_content -}}
            </summary>
            <div
              class="accordion__content rte"
              id="CollapsibleAccordion-{{ block.id }}-{{ section.id }}"
              role="region"
              aria-labelledby="Summary-{{ block.id }}-{{ section.id }}"
            >
              {{- menu_items -}}
            </div>
          </details>
        </div>
      {%- endif -%}
    {%- when 'brand_information' -%}
      <div class="footer-block__brand-info content--mobile-{{ block.settings.content_alignment_mobile }} content--desktop-{{ block.settings.content_alignment_desktop }}">
        {%- if settings.brand_image != blank -%}
          {%- assign brand_image_height = settings.brand_image_width
            | divided_by: settings.brand_image.aspect_ratio
          -%}
          <div
            class="footer-block__image-wrapper"
            style="max-width: min(100%, {{ settings.brand_image_width }}px);"
          >
            {{
              settings.brand_image
              | image_url: width: 1100
              | image_tag:
              loading: 'lazy',
              widths: '50, 100, 150, 200, 300, 400, 550, 800, 1100',
              height: brand_image_height,
              width: settings.brand_image_width
            }}
          </div>
        {%- endif -%}
        {%- if settings.brand_headline != blank -%}
          <div class="footer-block__heading rte h2">{{ settings.brand_headline }}</div>
        {%- endif -%}
        {%- if settings.brand_description != blank -%}
          <div class="rte">{{ settings.brand_description }}</div>
        {%- endif -%}
        {%- if block.settings.show_social -%}
          {%- render 'social-icons', class: 'footer__list-social', block: block -%}
        {%- endif -%}
      </div>
    {%- when 'image' -%}
      <div class="footer-block__details-content footer-block-image {{ block.settings.alignment }}">
        {%- if block.settings.image != blank -%}
          {%- assign image_size_2x = block.settings.image_width | times: 2 | at_most: 5760 -%}
            <div
              class="footer-block__image-wrapper"
              style="max-width: min(100%, {{ block.settings.image_width }}px);"
            >
              <img
                srcset= "{{ block.settings.image | image_url: width: block.settings.image_width }}, {{ block.settings.image | image_url: width: image_size_2x }} 2x"
                src="{{ block.settings.image | image_url: width: 760 }}"
                alt="{{ block.settings.image.alt | escape }}"
                loading="lazy"
                width="{{ block.settings.image.width }}"
                height="{{ block.settings.image.height }}"
              >
            </div>
        {%- else -%}
          {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
        {%- endif -%}
      </div>

    {%- when 'newsletter' -%}
      <div class="footer-block__newsletter{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
        {% if settings.animations_reveal_on_scroll %}
          data-cascade
        {% endif %}
      >
        {%- form 'customer', id: 'ContactFooter', class: 'footer__newsletter newsletter-form' -%}
          <input type="hidden" name="contact[tags]" value="newsletter">
          <div class="newsletter-form__field-wrapper">
            <div class="field">
              <input
                id="NewsletterForm--{{ section.id }}"
                type="email"
                name="contact[email]"
                class="field__input"
                value="{{ form.email }}"
                aria-required="true"
                autocorrect="off"
                autocapitalize="off"
                autocomplete="email"
                {% if form.errors %}
                  autofocus
                  aria-invalid="true"
                  aria-describedby="ContactFooter-error"
                {% elsif form.posted_successfully? %}
                  aria-describedby="ContactFooter-success"
                {% endif %}
                placeholder="{{ 'newsletter.label' | t }}"
                required
              >
              <label class="field__label" for="NewsletterForm--{{ section.id }}">
                {{ 'newsletter.label' | t }}
              </label>
              <button
                type="submit"
                class="newsletter-form__button field__button"
                name="commit"
                id="Subscribe"
                aria-label="{{ 'newsletter.button_label' | t }}"
              >
                <span class="svg-wrapper">
                  {{- 'icon-arrow.svg' | inline_asset_content -}}
                </span>
              </button>
            </div>
            {%- if form.errors -%}
              <small class="newsletter-form__message form__message" id="ContactFooter-error">
                <span class="svg-wrapper">
                  {{- 'icon-error.svg' | inline_asset_content -}}
                </span>
                {{- form.errors.translated_fields.email | capitalize }}
                {{ form.errors.messages.email -}}
              </small>
            {%- endif -%}
          </div>
          {%- if form.posted_successfully? -%}
            <div
              class="newsletter-form__message newsletter-form__message--success form__message h3"
              id="ContactFooter-success"
              tabindex="-1"
              autofocus
            >
              <span class="svg-wrapper">
                {{- 'icon-success.svg' | inline_asset_content -}}
              </span>
              {{- 'newsletter.success' | t }}
            </div>
          {%- endif -%}
        {%- endform -%}
      </div>

    {%- when 'follow_on_shop' -%}
      {%- if shop.features.follow_on_shop? -%}
        <div class="footer__follow-on-shop content--mobile-{{ block.settings.content_alignment_mobile }} content--desktop-{{ block.settings.content_alignment_desktop }}">
          {{ shop | login_button: action: 'follow' }}
        </div>
      {%- endif -%}

    {%- when 'social' -%}
      {%- render 'social-icons', class: 'footer__list-social', block: block -%}
      
    {%- when 'country_language' -%}
      {%- if block.settings.enable_country_selector or block.settings.enable_language_selector -%}
        <div class="footer__column footer__localization isolate content--mobile-{{ block.settings.content_alignment_mobile }} content--desktop-{{ block.settings.content_alignment_desktop }}">
          {%- if block.settings.enable_country_selector and localization.available_countries.size > 1 -%}
            <localization-form>
              {%- form 'localization', id: 'FooterCountryForm', class: 'localization-form' -%}
                <div>
                  <div class="caption-large text-body h2" id="FooterCountryLabel">{{ 'localization.country_label' | t }}</div>
                  {%- render 'country-localization', localPosition: 'FooterCountry' -%}
                </div>
              {%- endform -%}
            </localization-form>
          {%- endif -%}
  
          {%- if block.settings.enable_language_selector and localization.available_languages.size > 1 -%}
            <localization-form>
              {%- form 'localization', id: 'FooterLanguageForm', class: 'localization-form' -%}
                <div>
                  <div class="caption-large text-body h2" id="FooterLanguageLabel">
                    {{ 'localization.language_label' | t }}
                  </div>
                  {%- render 'language-localization', localPosition: 'FooterLanguage' -%}
                </div>
              {%- endform -%}
            </localization-form>
          {%- endif -%}
        </div>
      {%- endif -%}

    {%- when 'copyright_payments' -%}
      <div class="footer__column--info content--mobile-{{ block.settings.content_alignment_mobile }} content--desktop-{{ block.settings.content_alignment_desktop }}">
        {%- if block.settings.payment_enable -%}
          <div class="footer__payment">
            <span class="visually-hidden">{{ 'sections.footer.payment' | t }}</span>
            <ul class="list list-payment content--mobile-{{ block.settings.content_alignment_mobile }} content--desktop-{{ block.settings.content_alignment_desktop }}" role="list">
              {%- for type in shop.enabled_payment_types -%}
                <li class="list-payment__item">
                  {{ type | payment_type_svg_tag: class: 'icon icon--full-color' }}
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}
        <div class="footer__copyright caption rte">
          <small class="copyright__content">&copy; {{ 'now' | date: "%Y" }}, {{ shop.name | link_to: routes.root_url }}</small>
          <small class="copyright__content">|</small>
          <small class="copyright__content">
            Powered by <a target="_blank" class="link" rel="nofollow" href="https://longshore.com">Longshore</a> + <a target="_blank" class="link" rel="nofollow" href="https://shopify.com">Shopify</a>
          </small>
          {%- if block.settings.show_policy -%}
            {%- capture additional_policy_menu %}
              {%- for policy in block.settings.additional_policy_menu.links -%}
                <li>
                  <small class="copyright__content"
                    ><a href="{{ policy.url }}">{{ policy.title | escape }}</a></small
                  >
                </li>
              {%- endfor -%}
            {%- endcapture -%}

            <ul class="policies list-unstyled">
              {%- if block.settings.additional_policy_menu_order == 'before' %}
                {{ additional_policy_menu }}
              {%- endif -%}
              {%- for policy in shop.policies -%}
                {%- if policy != blank -%}
                  <li>
                    <small class="copyright__content"
                      ><a href="{{ policy.url }}">{{ policy.title | escape }}</a></small
                    >
                  </li>
                {%- endif -%}
              {%- endfor -%}
              {%- if block.settings.additional_policy_menu_order == 'after' %}
                {{ additional_policy_menu }}
              {%- endif -%}
            </ul>
          {%- endif -%}
        </div>
      </div>
  {%- endcase -%}
</div>