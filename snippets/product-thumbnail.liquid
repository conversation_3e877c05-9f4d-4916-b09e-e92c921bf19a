{% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
{% comment %}
  Renders a product thumbnail with a modal-opener

  Accepts:
  - media: {Object} Product Media object
  - media_count: {Number} Number of media objects
  - position: {String} Position of the media. Used for accessible label.
  - desktop_layout: {String} Layout of the media for desktop.
  - mobile_layout: {String} Layout of the media for mobile.
  - loop: {Boolean} Enable video looping (optional)
  - modal_id: {String} The product modal that will be shown by clicking the thumbnail
  - xr_button: {<PERSON><PERSON><PERSON>} Renders the "View in your space" button (shopify-xr-button) if the media is a 3D Model
  - constrain_to_viewport: {<PERSON>olean} Force media height to fit within viewport
  - media_fit: {String} Method ("contain" or "cover") to fit image into container
  - media_width: {Float} The width percentage that the media column occupies on desktop.
  - lazy_load: {<PERSON>olean} Image should be lazy loaded. Default: true (optional)

  Usage:
  {% render 'product-thumbnail',
    media: media,
    position: forloop.index,
    loop: section.settings.enable_video_looping,
    modal_id: section.id
  %}
{% endcomment %}

{%- liquid
  unless lazy_load == false
    assign lazy = 'lazy'
  endunless

  assign desktop_columns = 1
  if desktop_layout == 'columns' and media_count > 1
    assign desktop_columns = 2
  endif

  assign mobile_columns = 1
  if mobile_layout == 'columns' and media_count > 1
    assign mobile_columns = 2
  endif

  if media.media_type == 'image'
    assign image_class = 'image-magnify-' | append: section.settings.image_zoom
  endif
-%}

{%- capture sizes -%}
  (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | times: media_width | divided_by: desktop_columns | round }}px, (min-width: 990px) calc({{ media_width | times: 100 | divided_by: desktop_columns }}vw - 10rem), (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw / {{ mobile_columns }} - 4rem)
{%- endcapture -%}

<div
  class="product-media-container media-type-{{ media.media_type }} media-fit-{{ media_fit }} global-media-settings gradient{% if constrain_to_viewport %} constrain-height{% endif %}"
  style="--ratio: {{ media.aspect_ratio | default: 1.0 }}; --preview-ratio: {{ media.preview_image.aspect_ratio | default: 1.0 }};"
>
  <modal-opener
    class="product__modal-opener product__modal-opener--{{ media.media_type }}"
    data-modal="#ProductModal-{{ modal_id }}"
  >
    <span
      class="product__media-icon motion-reduce quick-add-hidden{% if media.media_type == 'image' %} product__media-icon--{{ section.settings.image_zoom }}{% endif %}"
      aria-hidden="true"
    >
      {% case media.media_type %}
        {% when 'video', 'external_video' %}
          <span class="svg-wrapper">
            {{- 'icon-play.svg' | inline_asset_content -}}
          </span>
        {% when 'model' %}
          <span class="svg-wrapper">
            {{- 'icon-3d-model.svg' | inline_asset_content -}}
          </span>
        {% else %}
          <span class="svg-wrapper">
            {{- 'icon-zoom.svg' | inline_asset_content -}}
          </span>
      {% endcase %}
    </span>
    {%- render 'loading-spinner' -%}
    <div class="product__media media media--transparent">
      {{
        media.preview_image
        | image_url: width: 1946
        | image_tag:
          class: image_class,
          loading: lazy,
          sizes: sizes,
          widths: '246, 493, 600, 713, 823, 990, 1100, 1206, 1346, 1426, 1646, 1946'
      }}
    </div>

    {%- if media.alt contains '#' -%}
      {%- assign media_caption = media.alt | split: '#' | last -%}
      <div class="card__badge {{ settings.product_image_badge_position }}">
        <span class="badge badge--bottom-left color-{{ settings.product_image_badge_color_scheme }} gradient">
          {{ media_caption }}
        </span>
      </div>
    {%- endif %}

    <button
      class="product__media-toggle quick-add-hidden product__media-zoom-{{ section.settings.image_zoom }}"
      type="button"
      aria-haspopup="dialog"
      data-media-id="{{ media.id }}"
    >
      <span class="visually-hidden">
        {{ 'products.product.media.open_media' | t: index: position }}
      </span>
    </button>
  </modal-opener>

  {%- if media.media_type != 'image' -%}
    {%- if media.media_type == 'model' -%}
      <product-model class="deferred-media media media--transparent" data-media-id="{{ media.id }}">
    {%- else -%}
      <deferred-media class="deferred-media media media--transparent" data-media-id="{{ media.id }}">
    {%- endif -%}
    <button id="Deferred-Poster-Modal-{{ media.id }}" class="deferred-media__poster" type="button">
      <span class="deferred-media__poster-button motion-reduce">
        {%- if media.media_type == 'model' -%}
          <span class="visually-hidden">{{ 'products.product.media.play_model' | t }}</span>
          <span class="svg-wrapper">
            {{- 'icon-3d-model.svg' | inline_asset_content -}}
          </span>
        {%- else -%}
          <span class="visually-hidden">{{ 'products.product.media.play_video' | t }}</span>
          <span class="svg-wrapper">
            {{- 'icon-play.svg' | inline_asset_content -}}
          </span>
        {%- endif -%}
      </span>
      {{
        media.preview_image
        | image_url: width: 1946
        | image_tag:
          loading: lazy,
          sizes: sizes,
          widths: '246, 493, 600, 713, 823, 990, 1100, 1206, 1346, 1426, 1646, 1946'
      }}
    </button>
    <template>
      {%- liquid
        case media.media_type
          when 'external_video'
            assign video_class = 'js-' | append: media.host
            if media.host == 'youtube'
              echo media | external_video_url: autoplay: true, loop: loop, playlist: media.external_id | external_video_tag: class: video_class, loading: 'lazy'
            else
              echo media | external_video_url: autoplay: true, loop: loop | external_video_tag: class: video_class, loading: 'lazy'
            endif
          when 'video'
            echo media | media_tag: image_size: '2048x', autoplay: true, loop: loop, controls: true, preload: 'none'
          when 'model'
            echo media | media_tag: image_size: '2048x', toggleable: true
        endcase
      -%}
    </template>

    {%- if media.media_type == 'model' -%}
      </product-model>
      {%- if xr_button -%}
        <button
          class="button button--full-width product__xr-button"
          type="button"
          aria-label="{{ 'products.product.xr_button_label' | t }}"
          data-shopify-xr
          data-shopify-model3d-id="{{ media.id }}"
          data-shopify-title="{{ product.title | escape }}"
          data-shopify-xr-hidden
        >
          <span class="svg-wrapper">
            {{- 'icon-3d-model.svg' | inline_asset_content -}}
          </span>
          {{ 'products.product.xr_button' | t }}
        </button>
      {%- endif -%}
    {%- else -%}
      </deferred-media>
    {%- endif -%}
  {%- endif -%}
</div>