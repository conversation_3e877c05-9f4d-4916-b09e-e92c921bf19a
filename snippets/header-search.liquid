{% comment %}
  bs-add
  - support for custom search options
{% endcomment %}

{% comment %}
  Renders a header search modal. Should be used with 'header.liquid'

  Accepts:
  - input_id: {String} Id for the search input element (required)

  Usage:
  {% render 'header-search', input_id: 'My-Id'%}
{% endcomment %}

<details-modal class="header__search header__icon__{{ settings.search_display }}">
  <details>
    <summary
      class="header__icon header__icon--search header__icon--summary link focus-inset modal__toggle"
      aria-haspopup="dialog"
      aria-label="{{ 'general.search.search' | t }}"
    >
      <span>
        {%- if settings.search_display == 'icon' -%}
          <span class="svg-wrapper">
            {{- 'icon-search.svg' | inline_asset_content -}}
          </span>
          <span class="svg-wrapper header__icon-close">
            {{- 'icon-close.svg' | inline_asset_content -}}
          </span>
        {%- else -%}
          <span class="" aria-hidden="true" focusable="false">{{ 'general.search.search' | t }}</span>
        {%- endif -%}
      </span>
    </summary>
    <div
      class="search-modal modal__content gradient"
      role="dialog"
      aria-modal="true"
      aria-label="{{ 'general.search.search' | t }}"
    >
      <div class="modal-overlay"></div>
      <div
        class="search-modal__content{% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_vertical_offset < 0 %} search-modal__content-top{% else %} search-modal__content-bottom{% endif %}"
        tabindex="-1"
      >
        {%- if settings.predictive_search_enabled -%}
          <predictive-search class="search-modal__form" data-loading-text="{{ 'accessibility.loading' | t }}">
        {%- else -%}
          <search-form class="search-modal__form">
        {%- endif -%}
        <form action="{{ routes.search_url }}" method="get" role="search" class="search search-modal__form">
          <div class="field">
            <input
              class="search__input field__input"
              id="{{ input_id }}"
              type="search"
              name="q"
              value="{{ search.terms | escape }}"
              placeholder="{{ 'general.search.search' | t }}"
              {%- if settings.predictive_search_enabled -%}
                data-search-options="{{ settings.predictive_search_liquid }}"
                role="combobox"
                aria-expanded="false"
                aria-owns="predictive-search-results"
                aria-controls="predictive-search-results"
                aria-haspopup="listbox"
                aria-autocomplete="list"
                autocorrect="off"
                autocomplete="off"
                autocapitalize="off"
                spellcheck="false"
              {%- endif -%}
            >
            <label class="field__label" for="{{ input_id }}">{{ 'general.search.search' | t }}</label>
            <input type="hidden" name="options[prefix]" value="last">
            {{ settings.search_liquid }}
            <button
              type="reset"
              class="reset__button field__button{% if search.terms == blank %} hidden{% endif %}"
              aria-label="{{ 'general.search.reset' | t }}"
            >
              <span class="svg-wrapper">
                {{- 'icon-reset.svg' | inline_asset_content -}}
              </span>
            </button>
            <button class="search__button field__button" aria-label="{{ 'general.search.search' | t }}">
              <span class="svg-wrapper">
                {{- 'icon-search.svg' | inline_asset_content -}}
              </span>
            </button>
          </div>

          {%- if settings.predictive_search_enabled -%}
            <div class="predictive-search predictive-search--header" tabindex="-1" data-predictive-search>
              {%- render 'loading-spinner', class: 'predictive-search__loading-state' -%}
            </div>

            <span class="predictive-search-status visually-hidden" role="status" aria-hidden="true"></span>
          {%- endif -%}
        </form>
        {%- if settings.predictive_search_enabled -%}
          </predictive-search>
        {%- else -%}
          </search-form>
        {%- endif -%}
        <button
          type="button"
          class="search-modal__close-button modal__close-button link link--text focus-inset"
          aria-label="{{ 'accessibility.close' | t }}"
        >
          <span class="svg-wrapper">
            {{- 'icon-close.svg' | inline_asset_content -}}
          </span>
        </button>
      </div>
    </div>
  </details>
</details-modal>