{% comment %}
    Renders a image link card

    Accepts:
    - link_url: {Object} Link liquid object (required)
    - link_label: {Object} Text liquid object (required)
    - media_image: {Object} Image liquid object (required)
    - media_aspect_ratio: {String} Size of the product image card. Values are "square" and "portrait". Default is "square" (optional)
    - columns: {Number} 
    - extend_height: {<PERSON>olean} Card height extends to available container space. Default: false (optional)
    - wrapper_class: {String} Wrapper class for card (optional)

    Usage:
    {% render 'card-image-link',
      link_url: block.settings.link,
      link_label: block.settings.link_label,
      media_image: block.settings.image
    %}
{% endcomment %}

{%- liquid
  assign ratio = 1
  if media_image and media_aspect_ratio == 'portrait'
    assign ratio = 0.8
  elsif media_image and media_aspect_ratio == 'adapt'
    assign ratio = media_image.aspect_ratio
  endif
  if ratio == 0 or ratio == null
    assign ratio = 1
  endif
  assign card_color_scheme = settings.card_color_scheme
  assign card_style = settings.card_style
  if wrapper_class == null or wrapper_class == 'none'
    assign card_color_scheme = settings.collection_card_color_scheme
    assign card_style = settings.collection_card_style
  endif 
-%}

<div class="card-wrapper animate-arrow {% if wrapper_class and wrapper_class != 'none' %}{{ wrapper_class }}{% else %}collection-card-wrapper{% endif %}">
  <div 
    class="
      card
      card--{{ card_style }}
      {% if media_image %} card--media{% else %} card--text{% endif %}
      {% if card_style == 'card' %} color-{{ card_color_scheme }} gradient{% endif %}
      {% if extend_height %} card--extend-height{% endif %}
      {% if media_image == nil and card_style == 'card' %} ratio{% endif %}
    "
    style="--ratio-percent: {{ 1 | divided_by: ratio | times: 100 }}%;"
  >
    <div 
      class="card__inner {% if card_style == 'standard' %}color-{{ card_color_scheme }} gradient{% endif %}{% if media_image or card_style == 'standard' %} ratio{% endif %}" 
      style="--ratio-percent: {{ 1 | divided_by: ratio | times: 100 }}%;"
    >
      {%- if media_image -%}
        <div class="card__media">
          <div class="media media--transparent media--hover-effect">
            <img
              srcset="
                {%- if media_image.width >= 165 -%}{{ media_image | image_url: width: 165 }} 165w,{%- endif -%}
                {%- if media_image.width >= 330 -%}{{ media_image | image_url: width: 330 }} 330w,{%- endif -%}
                {%- if media_image.width >= 535 -%}{{ media_image | image_url: width: 535 }} 535w,{%- endif -%}
                {%- if media_image.width >= 750 -%}{{ media_image | image_url: width: 750 }} 750w,{%- endif -%}
                {%- if media_image.width >= 1000 -%}{{ media_image | image_url: width: 1000 }} 1000w,{%- endif -%}
                {%- if media_image.width >= 1500 -%}{{ media_image | image_url: width: 1500 }} 1500w,{%- endif -%}
                {%- if media_image.width >= 3000 -%}{{ media_image | image_url: width: 3000 }} 3000w,{%- endif -%}
                {{ media_image | image_url }} {{ media_image.width }}w
              "
              src="{{ media_image | image_url: width: 1500 }}"
              sizes="
                (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: columns }}px,
                (min-width: 750px) {% if columns > 1 %}calc((100vw - 10rem) / 2){% else %}calc(100vw - 10rem){% endif %},
                calc(100vw - 3rem)
              "
              alt=""
              height="{{ media_image.height }}"
              width="{{ media_image.width }}"
              loading="lazy"
              class="motion-reduce"
            >
          </div>
        </div>
      {%- endif -%}
      <div class="card__content">
        <div class="card__information">
          <h3 class="card__heading">
            <a
              {% if link_url == blank %}
                role="link" aria-disabled="true"
              {% else %}
                href="{{ link_url }}"
              {% endif %}
              class="full-unstyled-link"
            >
              {%- if link_label != blank -%}
                {{- link_label | escape -}}
              {%- else -%}
                {{ 'onboarding.collection_title' | t }}
              {%- endif -%}
            </a>
          </h3>
        </div>   
      </div>
    </div>
    {% if card_style == 'card' or media_image and link_url != blank %} 
      <div class="card__content">
        <div class="card__information">
          <h3 class="card__heading">
            <a
              href="{{ link_url }}"
              class="full-unstyled-link"
            >
              {%- if link_label != blank -%}
                {{- link_label | escape -}}
              {%- else -%}
                {{ 'onboarding.collection_title' | t }}
              {%- endif -%}
              {%- if media_image -%}
                <span class="icon-wrap">
                  {{- 'icon-arrow.svg' | inline_asset_content -}}
                </span>
              {%- endif %}
            </a>
          </h3>
        </div>
      </div>
    {% endif %} 
  </div>
</div>
