{% comment %}
DO NOT EDIT THIS FILE!
IT IS MANAGED AUTOMATICALLY BY THE AGE CHECKER SHOPIFY APP!
CHANGES MADE TO THIS FILE WILL BE OVERWRITTEN DURING APP UPDATES!
LAST UPDATE Thu Nov 23 2023 12:35:04 GMT+0000
{% endcomment %}

{% unless age_checker_snippet_has_ran %}

        {% capture show_checker_on %}{{ shop.metafields.agecheckerVsARddsz9.show_checker_on }}{% endcapture %}

    {% if content_for_header contains 'mG35tH2eGxRu7PQW' %}

        {% assign age_checker_installed = true %}

    {% else %}

        {% assign age_checker_installed = false %}

    {% endif %}

    {% if age_checker_installed != true %}

        {% assign age_checker_could_show = false %}

    {% elsif shop.metafields.agecheckerVsARddsz9.active != 'QFw7fWSBpVZDmWX9U7' %}

        {% assign age_checker_could_show = false %}

    
    {% elsif show_checker_on != 'certain_pages' %}

        {% assign age_checker_could_show = true %}

    {% elsif shop.metafields.agecheckerVsARddsz9.groups %}

        
        {% assign age_checker_could_show = true %}

        {% assign use_page_rules = true %}

    {% elsif template.name == 'index' %}

        
        {% assign age_checker_could_show = true %}

    {% else %}

        {% assign age_checker_could_show = false %}

    {% endif %}

    
            {% if shop.metafields.agecheckerVsARddsz9.logo_image.type == 'json' %}
          {% assign logo_image_end = shop.metafields.agecheckerVsARddsz9.logo_image.value.url | split: '/' | last %}
        {% else %}
          {% assign logo_image_end = shop.metafields.agecheckerVsARddsz9.logo_image.url | split: '/' | last %}
        {% endif %}

        {% if logo_image_end != blank %}
            {% assign logo_image_asset = logo_image_end | split: '?' | first %}
            {% assign logo_image_url = logo_image_asset | asset_img_url: '300x' %}
        {% endif %}
            {% if shop.metafields.agecheckerVsARddsz9.modal_image.type == 'json' %}
          {% assign modal_image_end = shop.metafields.agecheckerVsARddsz9.modal_image.value.url | split: '/' | last %}
        {% else %}
          {% assign modal_image_end = shop.metafields.agecheckerVsARddsz9.modal_image.url | split: '/' | last %}
        {% endif %}

        {% if modal_image_end != blank %}
            {% assign modal_image_asset = modal_image_end | split: '?' | first %}
            {% assign modal_image_url = modal_image_asset | asset_img_url: '800x' %}
        {% endif %}
            {% if shop.metafields.agecheckerVsARddsz9.background_image.type == 'json' %}
          {% assign background_image_end = shop.metafields.agecheckerVsARddsz9.background_image.value.url | split: '/' | last %}
        {% else %}
          {% assign background_image_end = shop.metafields.agecheckerVsARddsz9.background_image.url | split: '/' | last %}
        {% endif %}

        {% if background_image_end != blank %}
            {% assign background_image_asset = background_image_end | split: '?' | first %}
            {% assign background_image_url = background_image_asset | asset_img_url: '2000x' %}
        {% endif %}
            {% if shop.metafields.agecheckerVsARddsz9.cm_logo_image.type == 'json' %}
          {% assign cm_logo_image_end = shop.metafields.agecheckerVsARddsz9.cm_logo_image.value.url | split: '/' | last %}
        {% else %}
          {% assign cm_logo_image_end = shop.metafields.agecheckerVsARddsz9.cm_logo_image.url | split: '/' | last %}
        {% endif %}

        {% if cm_logo_image_end != blank %}
            {% assign cm_logo_image_asset = cm_logo_image_end | split: '?' | first %}
            {% assign cm_logo_image_url = cm_logo_image_asset | asset_img_url: '300x' %}
        {% endif %}
            {% if shop.metafields.agecheckerVsARddsz9.cm_modal_image.type == 'json' %}
          {% assign cm_modal_image_end = shop.metafields.agecheckerVsARddsz9.cm_modal_image.value.url | split: '/' | last %}
        {% else %}
          {% assign cm_modal_image_end = shop.metafields.agecheckerVsARddsz9.cm_modal_image.url | split: '/' | last %}
        {% endif %}

        {% if cm_modal_image_end != blank %}
            {% assign cm_modal_image_asset = cm_modal_image_end | split: '?' | first %}
            {% assign cm_modal_image_url = cm_modal_image_asset | asset_img_url: '800x' %}
        {% endif %}
            {% if shop.metafields.agecheckerVsARddsz9.cm_background_image.type == 'json' %}
          {% assign cm_background_image_end = shop.metafields.agecheckerVsARddsz9.cm_background_image.value.url | split: '/' | last %}
        {% else %}
          {% assign cm_background_image_end = shop.metafields.agecheckerVsARddsz9.cm_background_image.url | split: '/' | last %}
        {% endif %}

        {% if cm_background_image_end != blank %}
            {% assign cm_background_image_asset = cm_background_image_end | split: '?' | first %}
            {% assign cm_background_image_url = cm_background_image_asset | asset_img_url: '2000x' %}
        {% endif %}
    
    {% if shop.metafields.agecheckerVsARddsz9.no_peek_mode == 'QFw7fWSBpVZDmWX9U7' %}

        {% assign background_color = shop.metafields.agecheckerVsARddsz9.background_color %}
        <div id="agp__noPeekScreen"
            {% if background_color != blank %}
                style="background: {{ background_color }};"
            {% endif %}
        ></div>

        <style>
            #agp__noPeekScreen {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: #3d4246;
                z-index: 100000000;
                display: block;
            }
        </style>

    {% endif %}

    <div id="agp__root"></div>

    <script>
        window.agecheckerVsARddsz9 = {
            fields: {
                {% for definition in shop.metafields.agecheckerVsARddsz9 %}
                    {% if definition.last.value == blank %}
                        {{ definition.first }}: {{ definition.last | json }},
                    {% else %}
                        {{ definition.first }}: {{ definition.last.value | json }},
                    {% endif %}
                {% endfor %}
            },
            images: {
                logo_image: '{{ logo_image_url }}',
                modal_image: '{{ modal_image_url }}',
                background_image: '{{ background_image_url }}',
                cm_logo_image: '{{ cm_logo_image_url }}',
                cm_modal_image: '{{ cm_modal_image_url }}',
                cm_background_image: '{{ cm_background_image_url }}',
            },
            callbacks: {},
            cartUrl: '{{ routes.cart_url }}',
            {% if product.id != blank %}
                product: {{ product | json }},
            {% endif %}
            {% if use_page_rules != blank %}
                templateData: {
                    full: {{ template | json }},
                    directory: {{ template.directory | json }},
                    name: {{ template.name | json }},
                    suffix: {{ template.suffix | json }},
                    {% if product.id != blank %}
                        productId: {{ product.id }},
                        tags: {{ product.tags | json }},
                    {% endif %}
                    {% if collection.id != blank %}
                        collectionId: {{ collection.id }},
                    {% endif %}
                    {% if collection.handle != blank %}
                        collectionHandle: {{ collection.handle | json }},
                    {% endif %}
                    {% if page.id != blank %}
                        pageId: {{ page.id }},
                    {% endif %}
                    {% if article.id != blank %}
                        articleId: {{ article.id }},
                    {% endif %}
                }
            {% endif %}
        };

        window.agecheckerVsARddsz9.removeNoPeekScreen = function() {
            var el = document.getElementById('agp__noPeekScreen');
            el && el.remove();
        };

        window.agecheckerVsARddsz9.getCookie = function(name) {
            var nameEQ = name + '=';
            var ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        };

        window.agecheckerVsARddsz9.getHistory = function() {
            if (window.agecheckerVsARddsz9.fields.session_type === 'session') {
                return sessionStorage.getItem('__age_checker-history');
            }
            return window.agecheckerVsARddsz9.getCookie('__age_checker-history');
        };

        window.agecheckerVsARddsz9.isUrlMatch = function(value) {
            var currentPath = window.location.pathname;
            var currentUrlParts = window.location.href.split('?');
            var currentQueryString = currentUrlParts[1] || null;

            var ruleUrlParts = value.split('?');
            var ruleQueryString = ruleUrlParts[1] || null;
            var el = document.createElement('a');
            el.href = value;
            var rulePath = el.pathname;

            var currentPathParts = currentPath.replace(/^\/|\/$/g, '').split('/');
            var rulePathParts = rulePath.replace(/^\/|\/$/g, '').split('/');

            if (currentPathParts.length !== rulePathParts.length) {
                return false;
            }

            for (var i = 0; i < currentPathParts.length; i++) {
                if (currentPathParts[i] !== rulePathParts[i] && rulePathParts[i] !== '*') {
                    return false;
                }
            }

            return !ruleQueryString || currentQueryString === ruleQueryString;
        };

        window.agecheckerVsARddsz9.checkPageRule = function(rule) {
            var templateData = window.agecheckerVsARddsz9.templateData;
            var check;
            switch (rule.type) {
                case 'template_type':
                    check = (
                        (rule.value === 'homepage' && templateData.full === 'index')
                        || (rule.value === templateData.name)
                    );
                    break;
                case 'template':
                    check = rule.value === templateData.full;
                    break;
                case 'product':
                    check = parseInt(rule.value, 10) === parseInt(templateData.productId, 10);
                    break;
                case 'page':
                    check = parseInt(rule.value, 10) === parseInt(templateData.pageId, 10);
                    break;
                case 'article':
                    check = parseInt(rule.value, 10) === parseInt(templateData.articleId, 10);
                    break;
                case 'collection':
                    check = (
                        (parseInt(rule.value, 10) === parseInt(templateData.collectionId, 10))
                        || (rule.value === templateData.collectionHandle)
                    );
                    break;
                case 'tag':
                    check = templateData.tags && templateData.tags.indexOf(rule.value) !== -1;
                    break;
                case 'url':
                    check = window.agecheckerVsARddsz9.isUrlMatch(rule.value);
                    break;
            }

            if (rule.logic === 'not_equal') {
                check = !check;
            }

            return check;
        };

        window.agecheckerVsARddsz9.matchesPageRules = function() {
            var groups = window.agecheckerVsARddsz9.fields.groups;
            var arr1 = Object.keys(groups).map((k1) => {
                return Object.keys(groups[k1]).map(k2 => groups[k1][k2]);
            });

            return arr1.some((arr2) => arr2.every((rule) => {
                return window.agecheckerVsARddsz9.checkPageRule(rule);
            }));
        };

        window.agechecker_developer_api = {
            on: function(eventName, callback) {
                if (!window.agecheckerVsARddsz9.callbacks[eventName]) {
                    window.agecheckerVsARddsz9.callbacks[eventName] = [];
                }
                window.agecheckerVsARddsz9.callbacks[eventName].push(callback);
            },
        };
    </script>

    {% if age_checker_could_show == true %}

        <script>
            window.agecheckerVsARddsz9.isPageMatch = true;

            if (window.agecheckerVsARddsz9.getHistory() === 'pass') {
                window.agecheckerVsARddsz9.isPageMatch = false;
            } else {
                                {% if use_page_rules != blank %}
                    if (window.agecheckerVsARddsz9.isPageMatch) {
                        window.agecheckerVsARddsz9.isPageMatch = window.agecheckerVsARddsz9.matchesPageRules();
                    }
                {% endif %}
            }
        </script>

    {% else %}

        <script>
            window.agecheckerVsARddsz9.isPageMatch = false;
        </script>

    {% endif %}

    <script>
        if (window.location.pathname === '/age-checker-preview') {
            window.agecheckerVsARddsz9.isPageMatch = true;
        }

        if (!window.agecheckerVsARddsz9.isPageMatch) {
            window.agecheckerVsARddsz9.removeNoPeekScreen();
        }
    </script>

    {% assign age_checker_snippet_has_ran = true %}

{% endunless %}
