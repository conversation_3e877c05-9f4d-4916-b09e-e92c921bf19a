{% comment %}
  Renders icon with text block

  Accepts:
  - block: {Object} passes in the block information.


  Usage:
  {% render 'icon-with-text',
    block: block
  %}
{% endcomment %}
{%- liquid
  assign heading_1_empty = false
  assign heading_2_empty = false
  assign heading_3_empty = false
  assign text_only_all_items = true

  if block.settings.heading_1 == empty and block.settings.heading_1_liquid == blank
    assign heading_1_empty = true
  endif

  if block.settings.heading_2 == empty and block.settings.heading_2_liquid == blank
    assign heading_2_empty = true
  endif

  if block.settings.heading_3 == empty and block.settings.heading_3_liquid == blank
    assign heading_3_empty = true
  endif

  if heading_1_empty == false and block.settings.icon_1 != 'none' or block.settings.image_1 != null
    assign text_only_all_items = false
  elsif heading_2_empty == false and block.settings.icon_2 != 'none' or block.settings.image_2 != null
    assign text_only_all_items = false
  elsif heading_3_empty == false and block.settings.icon_3 != 'none' or block.settings.image_3 != null
    assign text_only_all_items = false
  endif
-%}
<ul
  class="icon-with-text icon-with-text--{{ block.settings.layout }} list-unstyled{% if text_only_all_items %} icon-with-text--text-only{% endif %}"
  {{ block.shopify_attributes }}
>
  {%- unless heading_1_empty -%}
    <li class="icon-with-text__item">
      {%- if block.settings.image_1 == null -%}
        {%- render 'icon-accordion', icon: block.settings.icon_1 -%}
      {%- else -%}
        <img
          src="{{ block.settings.image_1 | image_url }}"
          {% if block.settings.image_1.alt != blank %}
            alt="{{ block.settings.image_1.alt | escape }}"
          {% else %}
            role="presentation"
          {% endif %}
          height="auto"
          width="auto"
          loading="lazy"
        >
      {%- endif -%}
      <span class="{{ block.settings.heading_title_size }} inline-richtext">
        {{- block.settings.heading_1_liquid | default: block.settings.heading_1 -}}
      </span>
    </li>
  {%- endunless -%}
  {%- unless heading_2_empty -%}
    <li class="icon-with-text__item">
      {%- if block.settings.image_2 == null -%}
        {% render 'icon-accordion', icon: block.settings.icon_2 -%}
      {%- else -%}
        <img
          src="{{ block.settings.image_2 | image_url }}"
          {% if block.settings.image_2.alt != blank %}
            alt="{{ block.settings.image_2.alt | escape }}"
          {% else %}
            role="presentation"
          {% endif %}
          height="auto"
          width="auto"
          loading="lazy"
        >
      {%- endif -%}
      <span class="{{ block.settings.heading_title_size }} inline-richtext">
        {{- block.settings.heading_2_liquid | default: block.settings.heading_2 -}}
      </span>
    </li>
  {%- endunless -%}
  {%- unless heading_3_empty -%}
    <li class="icon-with-text__item">
      {%- if block.settings.image_3 == null -%}
        {% render 'icon-accordion', icon: block.settings.icon_3 -%}
      {%- else -%}
        <img
          src="{{ block.settings.image_3 | image_url }}"
          {% if block.settings.image_3.alt != blank %}
            alt="{{ block.settings.image_3.alt | escape }}"
          {% else %}
            role="presentation"
          {% endif %}
          height="auto"
          width="auto"
          loading="lazy"
        >
      {%- endif -%}
      <span class="{{ block.settings.heading_title_size }} inline-richtext">
        {{- block.settings.heading_3_liquid | default: block.settings.heading_3 -}}
      </span>
    </li>
  {%- endunless -%}
</ul>
