{% comment %} Awtomic Subscriptions {% endcomment %}
<script>
  var TRANSLATIONS = {
    en: {
      translation: {
        titles: {
          return_to_account: "{{ 'awtomatic.customer_portal.titles.return_to_account' | t }}",
          subscription: "{{ 'awtomatic.customer_portal.titles.subscription' | t }}",
          subscriptions: "{{ 'awtomatic.customer_portal.titles.subscriptions' | t }}",
          loading: "{{ 'awtomatic.customer_portal.titles.loading' | t }}",
          cancel: "{{ 'awtomatic.customer_portal.titles.cancel' | t }}",
          save: "{{ 'awtomatic.customer_portal.titles.save' | t }}",
          continue: "{{ 'awtomatic.customer_portal.titles.continue' | t }}",
          pause: "{{ 'awtomatic.customer_portal.titles.pause' | t }}",
          edit: "{{ 'awtomatic.customer_portal.titles.edit' | t }}",
          my_subscriptions: "{{ 'awtomatic.customer_portal.titles.my_subscriptions' | t }}",
          frequency: "{{ 'awtomatic.customer_portal.titles.frequency' | t }}",
          next_order: "{{ 'awtomatic.customer_portal.titles.next_order' | t }}",
          confirm: "{{ 'awtomatic.customer_portal.titles.confirm' | t }}",
          remove: "{{ 'awtomatic.customer_portal.titles.remove' | t }}",
          description: "{{ 'awtomatic.customer_portal.titles.description' | t }}",
          add: "{{ 'awtomatic.customer_portal.titles.add' | t }}",
        },
        email_gate: {
          no_subs: "{{ 'awtomatic.customer_portal.email_gate.no_subs' | t }}",
          check_email: "{{ 'awtomatic.customer_portal.email_gate.check_email' | t }}",
          cta_access_link: "{{ 'awtomatic.customer_portal.email_gate.cta_access_link' | t }}",
        },
        subscriptions: {
          error: "{{ 'awtomatic.customer_portal.subscriptions.error' | t }}",
          no_subs: "{{ 'awtomatic.customer_portal.subscriptions.no_subs' | t }}",
          link_to_account: "{{ 'awtomatic.customer_portal.subscriptions.link_to_account' | t }}",
        },
        subscription: {
          fetch_error: "{{ 'awtomatic.customer_portal.subscription.fetch_error' | t }}",
          auth_error: "{{ 'awtomatic.customer_portal.subscription.auth_error' | t }}",
          plan: "{{ 'awtomatic.customer_portal.subscription.plan' | t }}",
          active: "{{ 'awtomatic.customer_portal.subscription.active' | t }}",
          activate: "{{ 'awtomatic.customer_portal.subscription.activate' | t }}",
          pause: "{{ 'awtomatic.customer_portal.subscription.pause' | t }}",
          paused: "{{ 'awtomatic.customer_portal.subscription.paused' | t }}",
          cancel: "{{ 'awtomatic.customer_portal.subscription.cancel' | t }}",
          cancelled: "{{ 'awtomatic.customer_portal.subscription.cancelled' | t }}",
          shipment_info: "{{ 'awtomatic.customer_portal.subscription.shipment_info' | t }}",
          update_shipping: "{{ 'awtomatic.customer_portal.subscription.update_shipping' | t }}",
          payment_method: "{{ 'awtomatic.customer_portal.subscription.payment_method' | t }}",
          pay_pal_payment_method: "{{ 'awtomatic.customer_portal.subscription.pay_pal_payment_method' | t }}",
          ending_in: "{{ 'awtomatic.customer_portal.subscription.ending_in' | t }}",
          expires: "{{ 'awtomatic.customer_portal.subscription.expires' | t }}",
          add_phone: "{{ 'awtomatic.customer_portal.subscription.add_phone' | t }}",
          add_name: "{{ 'awtomatic.customer_portal.subscription.add_name' | t }}",
          add_lastName: "{{ 'awtomatic.customer_portal.subscription.add_lastName' | t }}",
          billing_attepts: "{{ 'awtomatic.customer_portal.subscription.billing_attepts' | t }}",
          percentage_dynamic_discount: "{{ 'awtomatic.customer_portal.subscription.percentage_dynamic_discount' | t }}",
          fixed_amount_dynamic_discount:
            "{{ 'awtomatic.customer_portal.subscription.fixed_amount_dynamic_discount' | t }}",
          then_dynamic_discount: "{{ 'awtomatic.customer_portal.subscription.then_dynamic_discount' | t }}",
          orders_dynamic_discount: "{{ 'awtomatic.customer_portal.subscription.orders_dynamic_discount' | t }}",
          billing_attepts_empty: "{{ 'awtomatic.customer_portal.subscription.billing_attepts_empty' | t }}",
          billing_attepts_error: "{{ 'awtomatic.customer_portal.subscription.billing_attepts_error' | t }}",
          subs_details: "{{ 'awtomatic.customer_portal.subscription.subs_details' | t }}",
          update_payment_btn: "{{ 'awtomatic.customer_portal.subscription.update_payment_btn' | t }}",
          cancel_prompt_title: "{{ 'awtomatic.customer_portal.subscription.cancel_prompt_title' | t }}",
          cancel_error: "{{ 'awtomatic.customer_portal.subscription.cancel_error' | t }}",
          no_payment_method_error: "{{ 'awtomatic.customer_portal.subscription.no_payment_method_error' | t }}",
          no_payment_method_error_add: "{{ 'awtomatic.customer_portal.subscription.no_payment_method_error_add' | t }}",
          new_payment_method_success: "{{ 'awtomatic.customer_portal.subscription.new_payment_method_success' | t }}",
          new_payment_method_error: "{{ 'awtomatic.customer_portal.subscription.new_payment_method_error' | t }}",
          remove_line_error: "{{ 'awtomatic.customer_portal.subscription.remove_line_error' | t }}",
          remove_line_success: "{{ 'awtomatic.customer_portal.subscription.remove_line_success' | t }}",
          ships_to: "{{ 'awtomatic.customer_portal.subscription.ships_to' | t }}",
          update_payment_success: "{{ 'awtomatic.customer_portal.subscription.update_payment_success' | t }}",
          update_payment_success_dunning:
            "{{ 'awtomatic.customer_portal.subscription.update_payment_success_dunning' | t }}",
          update_payment_success_payment_error:
            "{{ 'awtomatic.customer_portal.subscription.update_payment_success_payment_error' | t }}",
          update_payment_error: "{{ 'awtomatic.customer_portal.subscription.update_payment_error' | t }}",
          sales_tax: "{{ 'awtomatic.customer_portal.subscription.sales_tax' | t }}",
          skip_billing_interval_prompt:
            "{{ 'awtomatic.customer_portal.subscription.skip_billing_interval_prompt' | t }}",
          skip_billing_interval_success:
            "{{ 'awtomatic.customer_portal.subscription.skip_billing_interval_success' | t }}",
          skip_billing_interval_error: "{{ 'awtomatic.customer_portal.subscription.skip_billing_interval_error' | t }}",
          skip_until: "{{ 'awtomatic.customer_portal.subscription.skip_until' | t }}",
          skip_next: "{{ 'awtomatic.customer_portal.subscription.skip_next' | t }}",
          order_now: "{{ 'awtomatic.customer_portal.subscription.order_now' | t }}",
          next_order_success: "{{ 'awtomatic.customer_portal.subscription.next_order_success' | t }}",
          next_order_error: "{{ 'awtomatic.customer_portal.subscription.next_order_error' | t }}",
          add_new_address: "{{ 'awtomatic.customer_portal.subscription.add_new_address' | t }}",
          address_update_success: "{{ 'awtomatic.customer_portal.subscription.address_update_success' | t }}",
          address_update_failed: "{{ 'awtomatic.customer_portal.subscription.address_update_failed' | t }}",
          pause_subscription: "{{ 'awtomatic.customer_portal.subscription.pause_subscription' | t }}",
          cancel_subscription: "{{ 'awtomatic.customer_portal.subscription.cancel_subscription' | t }}",
          pause_disclaimer_date: "{{ 'awtomatic.customer_portal.subscription.pause_disclaimer_date' | t }}",
          pause_disclaimer_indefinitely:
            "{{ 'awtomatic.customer_portal.subscription.pause_disclaimer_indefinitely' | t }}",
          pause_success: "{{ 'awtomatic.customer_portal.subscription.pause_success' | t }}",
          reactivate_btn: "{{ 'awtomatic.customer_portal.subscription.reactivate_btn' | t }}",
          reactivate: "{{ 'awtomatic.customer_portal.subscription.reactivate' | t }}",
          reactivate_success: "{{ 'awtomatic.customer_portal.subscription.reactivate_success' | t }}",
          reactivate_error: "{{ 'awtomatic.customer_portal.subscription.reactivate_error' | t }}",
          frequency_days: "{{ 'awtomatic.customer_portal.subscription.frequency_days' | t }}",
          frequency_weeks: "{{ 'awtomatic.customer_portal.subscription.frequency_weeks' | t }}",
          frequency_months: "{{ 'awtomatic.customer_portal.subscription.frequency_months' | t }}",
          order_now_modal_body: "{{ 'awtomatic.customer_portal.subscription.order_now_modal_body' | t }}",
          amount: "{{ 'awtomatic.customer_portal.subscription.amount' | t }}",
          frequency_update_success: "{{ 'awtomatic.customer_portal.subscription.frequency_update_success' | t }}",
          frequency_update_error: "{{ 'awtomatic.customer_portal.subscription.frequency_update_error' | t }}",
          customer_update_success: "{{ 'awtomatic.customer_portal.subscription.customer_update_success' | t }}",
          customer_update_error: "{{ 'awtomatic.customer_portal.subscription.customer_update_error' | t }}",
          customer_update_info: "{{ 'awtomatic.customer_portal.subscription.customer_update_info' | t }}",
          verified_phone: "{{ 'awtomatic.customer_portal.subscription.verified_phone' | t }}",
          not_verified_phone: "{{ 'awtomatic.customer_portal.subscription.not_verified_phone' | t }}",
          verify_phone: "{{ 'awtomatic.customer_portal.subscription.verify_phone' | t }}",
          enter_code: "{{ 'awtomatic.customer_portal.subscription.enter_code' | t }}",
          verify_code_error: "{{ 'awtomatic.customer_portal.subscription.verify_code_error' | t }}",
          verify_code_retry_error: "{{ 'awtomatic.customer_portal.subscription.verify_code_retry_error' | t }}",
          verify_code_success: "{{ 'awtomatic.customer_portal.subscription.verify_code_success' | t }}",
          code_received: "{{ 'awtomatic.customer_portal.subscription.code_received' | t }}",
          code_enter: "{{ 'awtomatic.customer_portal.subscription.code_enter' | t }}",
          code_send: "{{ 'awtomatic.customer_portal.subscription.code_send' | t }}",
          qty: "{{ 'awtomatic.customer_portal.subscription.qty' | t }}",
          manage: "{{ 'awtomatic.customer_portal.subscription.manage' | t }}",
          your_next_order: "{{ 'awtomatic.customer_portal.subscription.your_next_order' | t }}",
          swap_product: "{{ 'awtomatic.customer_portal.subscription.swap_product' | t }}",
          swap_product_cta: "{{ 'awtomatic.customer_portal.subscription.swap_product_cta' | t }}",
          or: "{{ 'awtomatic.customer_portal.subscription.or' | t }}",
          update_variant: "{{ 'awtomatic.customer_portal.subscription.update_variant' | t }}",
          update_bundle: "{{ 'awtomatic.customer_portal.subscription.update_bundle' | t }}",
          save: "{{ 'awtomatic.customer_portal.subscription.save' | t }}",
          failed_payment_message: "{{ 'awtomatic.customer_portal.subscription.failed_payment_message' | t }}",
          failed_payment_cta: "{{ 'awtomatic.customer_portal.subscription.failed_payment_cta' | t }}",
          order_now_success: "{{ 'awtomatic.customer_portal.subscription.order_now_success' | t }}",
          order_now_error: "{{ 'awtomatic.customer_portal.subscription.order_now_error' | t }}",
          update_current_payment_method:
            "{{ 'awtomatic.customer_portal.subscription.update_current_payment_method' | t }}",
          add_new_card: "{{ 'awtomatic.customer_portal.subscription.add_new_card' | t }}",
          delayed: "{{ 'awtomatic.customer_portal.subscription.delayed' | t }}",
          delayedNotification: "{{ 'awtomatic.customer_portal.subscription.delayedNotification' | t }}",
          update_success: "{{ 'awtomatic.customer_portal.subscription.update_success' | t }}",
          update_line_success: "{{ 'awtomatic.customer_portal.subscription.update_line_success' | t }}",
          update_error: "{{ 'awtomatic.customer_portal.subscription.update_error' | t }}",
        },
        new_payment_method: {
          title: "{{ 'awtomatic.customer_portal.new_payment_method.title' | t }}",
          payment_info: "{{ 'awtomatic.customer_portal.new_payment_method.payment_info' | t }}",
          card_number: "{{ 'awtomatic.customer_portal.new_payment_method.card_number' | t }}",
          exp_date: "{{ 'awtomatic.customer_portal.new_payment_method.exp_date' | t }}",
          security_code: "{{ 'awtomatic.customer_portal.new_payment_method.security_code' | t }}",
          first_name: "{{ 'awtomatic.customer_portal.new_payment_method.first_name' | t }}",
          last_name: "{{ 'awtomatic.customer_portal.new_payment_method.last_name' | t }}",
          billing_address: "{{ 'awtomatic.customer_portal.new_payment_method.billing_address' | t }}",
          address1: "{{ 'awtomatic.customer_portal.new_payment_method.address1' | t }}",
          address2: "{{ 'awtomatic.customer_portal.new_payment_method.address2' | t }}",
          city: "{{ 'awtomatic.customer_portal.new_payment_method.city' | t }}",
          country: "{{ 'awtomatic.customer_portal.new_payment_method.country' | t }}",
          state: "{{ 'awtomatic.customer_portal.new_payment_method.state' | t }}",
          zip: "{{ 'awtomatic.customer_portal.new_payment_method.zip' | t }}",
          phone: "{{ 'awtomatic.customer_portal.new_payment_method.phone' | t }}",
        },
        tables: {
          ID: "{{ 'awtomatic.customer_portal.tables.ID' | t }}",
          status: "{{ 'awtomatic.customer_portal.tables.status' | t }}",
          item: "{{ 'awtomatic.customer_portal.tables.item' | t }}",
          created: "{{ 'awtomatic.customer_portal.tables.created' | t }}",
          next_order: "{{ 'awtomatic.customer_portal.tables.next_order' | t }}",
          skip: "{{ 'awtomatic.customer_portal.tables.skip' | t }}",
          product: "{{ 'awtomatic.customer_portal.tables.product' | t }}",
          quantity: "{{ 'awtomatic.customer_portal.tables.quantity' | t }}",
          price: "{{ 'awtomatic.customer_portal.tables.price' | t }}",
          total: "{{ 'awtomatic.customer_portal.tables.total' | t }}",
          shipping: "{{ 'awtomatic.customer_portal.tables.shipping' | t }}",
          originated: "{{ 'awtomatic.customer_portal.tables.originated' | t }}",
          frequency: "{{ 'awtomatic.customer_portal.tables.frequency' | t }}",
          phone: "{{ 'awtomatic.customer_portal.tables.phone' | t }}",
          name: "{{ 'awtomatic.customer_portal.tables.name' | t }}",
          lastName: "{{ 'awtomatic.customer_portal.tables.lastName' | t }}",
          date: "{{ 'awtomatic.customer_portal.tables.date' | t }}",
          sold_out: "{{ 'awtomatic.customer_portal.tables.sold_out' | t }}",
          partial_inventory: "{{ 'awtomatic.customer_portal.tables.partial_inventory' | t }}",
          qty_sold_out: "{{ 'awtomatic.customer_portal.tables.qty_sold_out' | t }}",
          order_number: "{{ 'awtomatic.customer_portal.tables.order_number' | t }}",
          subtotal: "{{ 'awtomatic.customer_portal.tables.subtotal' | t }}",
          loading_fulfillments: "{{ 'awtomatic.customer_portal.tables.loading_fulfillments' | t }}",
          last_fulfillment: "{{ 'awtomatic.customer_portal.tables.last_fulfillment' | t }}",
          next_fulfillment: "{{ 'awtomatic.customer_portal.tables.next_fulfillment' | t }}",
          merchant_discount: "{{ 'awtomatic.customer_portal.tables.merchant_discount' | t }}",
          one_time_discount_label: "{{ 'awtomatic.customer_portal.tables.one_time_discount_label' | t }}",
          finite_discount_label: "{{ 'awtomatic.customer_portal.tables.finite_discount_label' | t }}",
          edit: "{{ 'awtomatic.customer_portal.tables.edit' | t }}",
          box_discount: {
            percentage: "{{ 'awtomatic.customer_portal.tables.box_discount.percentage' | t }}",
            amount: "{{ 'awtomatic.customer_portal.tables.box_discount.amount' | t }}",
          },
        },
        login: {
          start_intro: "{{ 'awtomatic.customer_portal.login.start_intro' | t }}",
          welcome: "{{ 'awtomatic.customer_portal.login.welcome' | t }}",
          send_link: "{{ 'awtomatic.customer_portal.login.send_link' | t }}",
          email_address: "{{ 'awtomatic.customer_portal.login.email_address' | t }}",
          continue: "{{ 'awtomatic.customer_portal.login.continue' | t }}",
          welcome_back: "{{ 'awtomatic.customer_portal.login.welcome_back' | t }}",
          choose_login: "{{ 'awtomatic.customer_portal.login.choose_login' | t }}",
          send_secure_link: "{{ 'awtomatic.customer_portal.login.send_secure_link' | t }}",
          login_password: "{{ 'awtomatic.customer_portal.login.login_password' | t }}",
          check_email: "{{ 'awtomatic.customer_portal.login.check_email' | t }}",
          secure_email_sent: "{{ 'awtomatic.customer_portal.login.secure_email_sent' | t }}",
          not_received: "{{ 'awtomatic.customer_portal.login.not_received' | t }}",
          new_link: "{{ 'awtomatic.customer_portal.login.new_link' | t }}",
          different_email: "{{ 'awtomatic.customer_portal.login.different_email' | t }}",
          no_subscriptions: "{{ 'awtomatic.customer_portal.login.no_subscriptions' | t }}",
          invalid_email: "{{ 'awtomatic.customer_portal.login.invalid_email' | t }}",
          no_subscriptions_message: "{{ 'awtomatic.customer_portal.login.no_subscriptions_message' | t }}",
        },
        product_swap: {
          title_select: "{{ 'awtomatic.customer_portal.product_swap.title_select' | t }}",
          title_confirm: "{{ 'awtomatic.customer_portal.product_swap.title_confirm' | t }}",
          sold_out: "{{ 'awtomatic.customer_portal.product_swap.sold_out' | t }}",
          choose: "{{ 'awtomatic.customer_portal.product_swap.choose' | t }}",
          back: "{{ 'awtomatic.customer_portal.product_swap.back' | t }}",
          confirm: "{{ 'awtomatic.customer_portal.product_swap.confirm' | t }}",
          save: "{{ 'awtomatic.customer_portal.product_swap.save' | t }}",
          update: "{{ 'awtomatic.customer_portal.product_swap.update' | t }}",
          original_item: "{{ 'awtomatic.customer_portal.product_swap.original_item' | t }}",
          replacement_item: "{{ 'awtomatic.customer_portal.product_swap.replacement_item' | t }}",
          update_success: "{{ 'awtomatic.customer_portal.product_swap.update_success' | t }}",
          update_error: "{{ 'awtomatic.customer_portal.product_swap.update_error' | t }}",
        },
        bundles: {
          build_box: "{{ 'awtomatic.customer_portal.bundles.build_box' | t }}",
          of: "{{ 'awtomatic.customer_portal.bundles.of' | t }}",
          back: "{{ 'awtomatic.customer_portal.bundles.back' | t }}",
          continue: "{{ 'awtomatic.customer_portal.bundles.continue' | t }}",
          add_cart: "{{ 'awtomatic.customer_portal.bundles.add_cart' | t }}",
          select_plan: "{{ 'awtomatic.customer_portal.bundles.select_plan' | t }}",
          build_your_box: "{{ 'awtomatic.customer_portal.bundles.build_your_box' | t }}",
          addons: "{{ 'awtomatic.customer_portal.bundles.addons' | t }}",
          checkout: "{{ 'awtomatic.customer_portal.bundles.checkout' | t }}",
          edit_my_box: "{{ 'awtomatic.customer_portal.bundles.edit_my_box' | t }}",
          box_size: "{{ 'awtomatic.customer_portal.bundles.box_size' | t }}",
          add_cart_error: "{{ 'awtomatic.customer_portal.bundles.add_cart_error' | t }}",
          update_price: "{{ 'awtomatic.customer_portal.bundles.update_price' | t }}",
          update_success: "{{ 'awtomatic.customer_portal.bundles.update_success' | t }}",
          update_error: "{{ 'awtomatic.customer_portal.bundles.update_error' | t }}",
          delivery_options: "{{ 'awtomatic.customer_portal.bundles.delivery_options' | t }}",
          one_time_delivery: "{{ 'awtomatic.customer_portal.bundles.one_time_delivery' | t }}",
          additional_products: "{{ 'awtomatic.customer_portal.bundles.additional_products' | t }}",
        },
        addons: {
          title: "{{ 'awtomatic.customer_portal.addons.title' | t }}",
          selection_unavailable: "{{ 'awtomatic.customer_portal.addons.selection_unavailable' | t }}",
          success_added: "{{ 'awtomatic.customer_portal.addons.success_added' | t }}",
          subtitle: "{{ 'awtomatic.customer_portal.addons.subtitle' | t }}",
          subscribe: "{{ 'awtomatic.customer_portal.addons.subscribe' | t }}",
          select: "{{ 'awtomatic.customer_portal.addons.select' | t }}",
          see_products: "{{ 'awtomatic.customer_portal.addons.see_products' | t }}",
          modal_title: "{{ 'awtomatic.customer_portal.addons.modal_title' | t }}",
          save: "{{ 'awtomatic.customer_portal.addons.save' | t }}",
          type: "{{ 'awtomatic.customer_portal.addons.type' | t }}",
        },
        sms: {
          title: "{{ 'awtomatic.customer_portal.sms.title' | t }}",
          enable: "{{ 'awtomatic.customer_portal.sms.enable' | t }}",
          edit_phone: "{{ 'awtomatic.customer_portal.sms.edit_phone' | t }}",
          enable_phone: "{{ 'awtomatic.customer_portal.sms.enable_phone' | t }}",
          phone_placeholder: "{{ 'awtomatic.customer_portal.sms.phone_placeholder' | t }}",
          enable_phone_text: "{{ 'awtomatic.customer_portal.sms.enable_phone_text' | t }}",
          phone: "{{ 'awtomatic.customer_portal.sms.phone' | t }}",
          invalid_phone: "{{ 'awtomatic.customer_portal.sms.invalid_phone' | t }}",
          invalid_country: "{{ 'awtomatic.customer_portal.sms.invalid_country' | t }}",
          duplicated_phone_error: "{{ 'awtomatic.customer_portal.sms.duplicated_phone_error' | t }}",
        },
        cancelModal: {
          header: "{{ 'awtomatic.customer_portal.cancelModal.header' | t }}",
          neverMind: "{{ 'awtomatic.customer_portal.cancelModal.neverMind' | t }}",
          back: "{{ 'awtomatic.customer_portal.cancelModal.back' | t }}",
          continue: "{{ 'awtomatic.customer_portal.cancelModal.continue' | t }}",
          continueWithCancellation: "{{ 'awtomatic.customer_portal.cancelModal.continueWithCancellation' | t }}",
          continueCancel: "{{ 'awtomatic.customer_portal.cancelModal.continueCancel' | t }}",
          confirmCancellation: "{{ 'awtomatic.customer_portal.cancelModal.confirmCancellation' | t }}",
          error: "{{ 'awtomatic.customer_portal.cancelModal.error' | t }}",
          tryAgain: "{{ 'awtomatic.customer_portal.cancelModal.tryAgain' | t }}",
          TOO_MUCH: "{{ 'awtomatic.customer_portal.cancelModal.TOO_MUCH' | t }}",
          MOVING: "{{ 'awtomatic.customer_portal.cancelModal.MOVING' | t }}",
          COMPETITOR: "{{ 'awtomatic.customer_portal.cancelModal.COMPETITOR' | t }}",
          BUDGET: "{{ 'awtomatic.customer_portal.cancelModal.BUDGET' | t }}",
          NOT_ENJOY: "{{ 'awtomatic.customer_portal.cancelModal.NOT_ENJOY' | t }}",
          NO_NEED: "{{ 'awtomatic.customer_portal.cancelModal.NO_NEED' | t }}",
          TRAVELLING: "{{ 'awtomatic.customer_portal.cancelModal.TRAVELLING' | t }}",
          OTHER: "{{ 'awtomatic.customer_portal.cancelModal.OTHER' | t }}",
          frequency: {
            modalTitle: "{{ 'awtomatic.customer_portal.cancelModal.frequency.modalTitle' | t }}",
            title: "{{ 'awtomatic.customer_portal.cancelModal.frequency.title' | t }}",
            subtitle: "{{ 'awtomatic.customer_portal.cancelModal.frequency.subtitle' | t }}",
            updateFrequency: "{{ 'awtomatic.customer_portal.cancelModal.frequency.updateFrequency' | t }}",
            formModalTitle: "{{ 'awtomatic.customer_portal.cancelModal.frequency.formModalTitle' | t }}",
            formTitle: "{{ 'awtomatic.customer_portal.cancelModal.frequency.formTitle' | t }}",
            formDelivery: "{{ 'awtomatic.customer_portal.cancelModal.frequency.formDelivery' | t }}",
            setFrequency: "{{ 'awtomatic.customer_portal.cancelModal.frequency.setFrequency' | t }}",
          },
          address: {
            modalPromptTitle: "{{ 'awtomatic.customer_portal.cancelModal.address.modalPromptTitle' | t }}",
            promptTitle: "{{ 'awtomatic.customer_portal.cancelModal.address.promptTitle' | t }}",
            promptSubtitle: "{{ 'awtomatic.customer_portal.cancelModal.address.promptSubtitle' | t }}",
            promptUpdateAddress: "{{ 'awtomatic.customer_portal.cancelModal.address.promptUpdateAddress' | t }}",
            modalFormTitle: "{{ 'awtomatic.customer_portal.cancelModal.address.modalFormTitle' | t }}",
            noShippingOptions: "{{ 'awtomatic.customer_portal.cancelModal.address.noShippingOptions' | t }}",
            confirmModalTitle: "{{ 'awtomatic.customer_portal.cancelModal.address.confirmModalTitle' | t }}",
            confirmTitle: "{{ 'awtomatic.customer_portal.cancelModal.address.confirmTitle' | t }}",
            confirmAcceptShipping: "{{ 'awtomatic.customer_portal.cancelModal.address.confirmAcceptShipping' | t }}",
            newAddress: "{{ 'awtomatic.customer_portal.cancelModal.address.newAddress' | t }}",
            oldAddress: "{{ 'awtomatic.customer_portal.cancelModal.address.oldAddress' | t }}",
          },
          pause: {
            header: "{{ 'awtomatic.customer_portal.cancelModal.pause.header' | t }}",
            title: "{{ 'awtomatic.customer_portal.cancelModal.pause.title' | t }}",
            description: "{{ 'awtomatic.customer_portal.cancelModal.pause.description' | t }}",
            cta: "{{ 'awtomatic.customer_portal.cancelModal.pause.cta' | t }}",
            success: "{{ 'awtomatic.customer_portal.cancelModal.pause.success' | t }}",
          },
          skip: {
            header: "{{ 'awtomatic.customer_portal.cancelModal.skip.header' | t }}",
            title: "{{ 'awtomatic.customer_portal.cancelModal.skip.title' | t }}",
            description: "{{ 'awtomatic.customer_portal.cancelModal.skip.description' | t }}",
            titleHideSkip: "{{ 'awtomatic.customer_portal.cancelModal.skip.titleHideSkip' | t }}",
            descriptionHideSkip: "{{ 'awtomatic.customer_portal.cancelModal.skip.descriptionHideSkip' | t }}",
            titleHideEditNextOrderDate:
              "{{ 'awtomatic.customer_portal.cancelModal.skip.titleHideEditNextOrderDate' | t }}",
            descriptionHideEditNextOrderDate:
              "{{ 'awtomatic.customer_portal.cancelModal.skip.descriptionHideEditNextOrderDate' | t }}",
            ctaReschedule: "{{ 'awtomatic.customer_portal.cancelModal.skip.ctaReschedule' | t }}",
            ctaSkip: "{{ 'awtomatic.customer_portal.cancelModal.skip.ctaSkip' | t }}",
            success: "{{ 'awtomatic.customer_portal.cancelModal.skip.success' | t }}",
          },
          reschedule: {
            header: "{{ 'awtomatic.customer_portal.cancelModal.reschedule.header' | t }}",
            confirmReschedule: "{{ 'awtomatic.customer_portal.cancelModal.reschedule.confirmReschedule' | t }}",
            dateFormat: "{{ 'awtomatic.customer_portal.cancelModal.reschedule.dateFormat' | t }}",
            inputLabel: "{{ 'awtomatic.customer_portal.cancelModal.reschedule.inputLabel' | t }}",
            description: "{{ 'awtomatic.customer_portal.cancelModal.reschedule.description' | t }}",
          },
          confirm: {
            modalTitle: "{{ 'awtomatic.customer_portal.cancelModal.confirm.modalTitle' | t }}",
            title: "{{ 'awtomatic.customer_portal.cancelModal.confirm.title' | t }}",
            button: "{{ 'awtomatic.customer_portal.cancelModal.confirm.button' | t }}",
            success: "{{ 'awtomatic.customer_portal.cancelModal.confirm.success' | t }}",
          },
          prepaidMultiOrder: {
            modalTitle: "{{ 'awtomatic.customer_portal.cancelModal.prepaidMultiOrder.modalTitle' | t }}",
            description: "{{ 'awtomatic.customer_portal.cancelModal.prepaidMultiOrder.description' | t }}",
            button: "{{ 'awtomatic.customer_portal.cancelModal.prepaidMultiOrder.button' | t }}",
            success: "{{ 'awtomatic.customer_portal.cancelModal.prepaidMultiOrder.success' | t }}",
          },
        },
        drawers: {
          updateAddress: {
            title: "{{ 'awtomatic.customer_portal.drawers.updateAddress.title' | t }}",
          },
        },
        babFilters: {
          filters: "{{ 'awtomatic.babFilters.filters' | t }}",
          reset: "{{ 'awtomatic.babFilters.reset' | t }}",
        },
        billingHistory: {
          title: "{{ 'awtomatic.customer_portal.billingHistory.title' | t }}",
          order: "{{ 'awtomatic.customer_portal.billingHistory.order' | t }}",
          date: "{{ 'awtomatic.customer_portal.billingHistory.date' | t }}",
          status: "{{ 'awtomatic.customer_portal.billingHistory.status' | t }}",
          submitted: "{{ 'awtomatic.customer_portal.billingHistory.submitted' | t }}",
          challenged: "{{ 'awtomatic.customer_portal.billingHistory.challenged' | t }}",
          failed: "{{ 'awtomatic.customer_portal.billingHistory.failed' | t }}",
          success: "{{ 'awtomatic.customer_portal.billingHistory.success' | t }}",
          loadMore: "{{ 'awtomatic.customer_portal.billingHistory.loadMore' | t }}",
          bankChallenge: "{{ 'awtomatic.customer_portal.billingHistory.bankChallenge' | t }}",
        },
        pauseModal: {
          delaySubscriptionTitle: "{{ 'awtomatic.customer_portal.pauseModal.delaySubscriptionTitle' | t }}",
          delaySubscriptionSubtitle: "{{ 'awtomatic.customer_portal.pauseModal.delaySubscriptionSubtitle' | t }}",
          delaySubscriptionBody: "{{ 'awtomatic.customer_portal.pauseModal.delaySubscriptionBody' | t }}",
          delaySubscriptionCta: "{{ 'awtomatic.customer_portal.pauseModal.delaySubscriptionCta' | t }}",
          pauseModalTitle: "{{ 'awtomatic.customer_portal.pauseModal.pauseModalTitle' | t }}",
          pauseModalLabel: "{{ 'awtomatic.customer_portal.pauseModal.pauseModalLabel' | t }}",
          pauseModalBody: "{{ 'awtomatic.customer_portal.pauseModal.pauseModalBody' | t }}",
          continueToPause: "{{ 'awtomatic.customer_portal.pauseModal.continueToPause' | t }}",
          neverMind: "{{ 'awtomatic.customer_portal.pauseModal.neverMind' | t }}",
          back: "{{ 'awtomatic.customer_portal.pauseModal.back' | t }}",
          save: "{{ 'awtomatic.customer_portal.pauseModal.save' | t }}",
          confirmPause: "{{ 'awtomatic.customer_portal.pauseModal.confirmPause' | t }}",
          delaySubscription: "{{ 'awtomatic.customer_portal.pauseModal.delaySubscription' | t }}",
          ONE_MONTH: "{{ 'awtomatic.customer_portal.pauseModal.ONE_MONTH' | t }}",
          TWO_MONTH: "{{ 'awtomatic.customer_portal.pauseModal.TWO_MONTH' | t }}",
          THREE_MONTH: "{{ 'awtomatic.customer_portal.pauseModal.THREE_MONTH' | t }}",
          CUSTOM: "{{ 'awtomatic.customer_portal.pauseModal.CUSTOM' | t }}",
        },
        discountCodeCard: {
          title: "{{ 'awtomatic.customer_portal.discountCodeCard.title' | t }}",
          addBtn: "{{ 'awtomatic.customer_portal.discountCodeCard.addBtn' | t }}",
          cancelBtn: "{{ 'awtomatic.customer_portal.discountCodeCard.cancelBtn' | t }}",
          inputPlaceholder: "{{ 'awtomatic.customer_portal.discountCodeCard.inputPlaceholder' | t }}",
          applyBtn: "{{ 'awtomatic.customer_portal.discountCodeCard.applyBtn' | t }}",
          successMsg: "{{ 'awtomatic.customer_portal.discountCodeCard.successMsg' | t }}",
          removeSuccessMsg: "{{ 'awtomatic.customer_portal.discountCodeCard.removeSuccessMsg' | t }}",
          loadingMsg: "{{ 'awtomatic.customer_portal.discountCodeCard.loadingMsg' | t }}",
          errorMsg: "{{ 'awtomatic.customer_portal.discountCodeCard.errorMsg' | t }}",
          boxDiscount: "{{ 'awtomatic.customer_portal.discountCodeCard.boxDiscount' | t }}",
        },
        frequency: {
          once_a: "{{ 'awtomatic.customer_portal.frequency.once_a' | t }}",
          every: "{{ 'awtomatic.customer_portal.frequency.every' | t }}",
          week: "{{ 'awtomatic.customer_portal.frequency.week' | t }}",
          month: "{{ 'awtomatic.customer_portal.frequency.month' | t }}",
          year: "{{ 'awtomatic.customer_portal.frequency.year' | t }}",
          weeks: "{{ 'awtomatic.customer_portal.frequency.weeks' | t }}",
          months: "{{ 'awtomatic.customer_portal.frequency.months' | t }}",
          years: "{{ 'awtomatic.customer_portal.frequency.years' | t }}",
        },
      },
    },
  };

  window.bundleapp = window.bundleapp || {};
  window.bundleapp.settings = {
    ...window.bundleapp.settings,
    translations: TRANSLATIONS,
  };
</script>
