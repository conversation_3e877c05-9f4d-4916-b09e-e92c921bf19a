<div class="tw:h-full tw:relative {{ block.settings.custom_classes }}">
  {% if block.settings.mobile_image %}
    <div class="tw:block tw:md:hidden tw:h-full tw:w-full {{ block.settings.mobile_image_wrapper_css }}">
      {{
        block.settings.mobile_image
        | image_url: width: 750
        | image_tag: class: 'tw:block tw:w-full tw:h-full tw:object-cover tw:object-center'
      }}
    </div>
  {% endif %}

  {% if block.settings.desktop_image %}
    <div class="tw:hidden tw:md:block tw:h-full tw:w-full {{ block.settings.desktop_image_wrapper_css }}">
      {{
        block.settings.desktop_image
        | image_url: width: block.settings.desktop_image.width
        | image_tag: class: 'tw:block tw:w-full tw:h-full tw:object-cover tw:object-center'
      }}
    </div>
  {% endif %}
  {% if block.settings.add_content %}
    <div class="{{ block.settings.add_content_classes }}">
      {{ block.settings.add_content }}
    </div>
  {% endif %}
</div>

{% schema %}
{
  "name": "Image",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Mobile",
      "info": "Devices with screens < 750px"
    },
    {
      "type": "image_picker",
      "id": "mobile_image",
      "label": "Image"
    },
    {
      "type": "header",
      "content": "Desktop",
      "info": "Devices with screens >= 749px"
    },
    {
      "type": "image_picker",
      "id": "desktop_image",
      "label": "Image"
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "liquid",
      "id": "add_content",
      "label": "Add content on top of image"
    },
    {
      "type": "text",
      "id": "add_content_classes",
      "label": "Additional content wrapper classes",
      "default": "tw:absolute tw:inset-0 tw:flex tw:justify-center tw:items-center"
    },
    {
      "type": "text",
      "id": "mobile_image_wrapper_css",
      "label": "Mobile Image wrapper custom classes"
    },
    {
      "type": "text",
      "id": "desktop_image_wrapper_css",
      "label": "Desktop image wrapper classes"
    },
    {
      "type": "text",
      "id": "custom_classes",
      "label": "Block custom classes"
    }
  ],
  "presets": [{ "name": "Image" }]
}
{% endschema %}
