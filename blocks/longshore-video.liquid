{% if block.settings.mobile_video %}
  <div class="tw:flex tw:md:hidden tw:h-full tw:w-full {{ block.settings.mobile_video_wrapper_css }}">
    {% liquid
      if block.settings.mobile_autoplay
        assign mobile_muted = true
      else
        assign mobile_muted = false
      endif
      if block.settings.mobile_controls
        assign mobile_loop = false
      else
        assign mobile_loop = true
      endif
    %}
    {{
      block.settings.mobile_video
      | video_tag:
        image_size: '1500x',
        loop: mobile_loop,
        controls: block.settings.mobile_controls,
        muted: mobile_muted,
        autoplay: block.settings.mobile_autoplay,
        class: 'tw:h-full tw:w-full tw:object-cover tw:object-center'
    }}
  </div>
{% endif %}

{% if block.settings.desktop_video %}
  <div class="tw:hidden tw:md:flex tw:h-full tw:w-full {{ block.settings.desktop_video_wrapper_css }}">
    {% liquid
      if block.settings.desktop_autoplay
        assign desktop_muted = true
      else
        assign desktop_muted = false
      endif
      if block.settings.desktop_controls
        assign desktop_loop = false
      else
        assign desktop_loop = true
      endif
    %}
    {{
      block.settings.desktop_video
      | video_tag:
        image_size: '1500x',
        loop: desktop_loop,
        controls: block.settings.desktop_controls,
        muted: desktop_muted,
        autoplay: block.settings.desktop_autoplay,
        class: 'tw:h-full tw:w-full tw:object-cover tw:object-center'
    }}
  </div>
{% endif %}

{% schema %}
  {
    "name": "Video",
    "class": "tw:h-full tw:w-full",
    "settings": [
      {
        "type": "header",
        "content": "Mobile",
        "info": "Devices with screens < 750px"
      },
      {
        "type": "video",
        "id": "mobile_video",
        "label": "Video"
      },
      {
        "type": "checkbox",
        "id": "mobile_controls",
        "label": "Show video controls?",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "mobile_autoplay",
        "label": "Autoplay?",
        "default": true
      },
      {
        "type": "header",
        "content": "Desktop",
        "info": "Devices with screens >= 750px"
      },
      {
        "type": "video",
        "id": "desktop_video",
        "label": "Desktop video"
      },
      {
        "type": "checkbox",
        "id": "desktop_controls",
        "label": "Show video controls?",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "desktop_autoplay",
        "label": "Autoplay?",
        "default": true
      },
      {
        "type": "header",
        "content": "Advanced settings",
        "info": "t:sections.all.advanced_options.label"
      },
      {
        "type": "text",
        "id": "mobile_video_wrapper_css",
        "label": "Mobile video wrapper custom classes"
      },
      {
        "type": "text",
        "id": "desktop_video_wrapper_css",
        "label": "Desktop video wrapper custom classes"
      },
      {
        "type": "text",
        "id": "custom_classes",
        "label": "Block custom classes"
      }
    ],
    "presets": [
      {"name": "Video"}
    ]
  }
{% endschema %}
