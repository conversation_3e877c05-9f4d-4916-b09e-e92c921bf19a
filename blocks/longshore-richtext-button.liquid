<div class="tw-flex tw-flex-col tw-text-center tw-justify-center tw-items-center tw-h-full tw-w-full tw-p-4">
  {{ block.settings.richtext }}
  {%- if block.settings.button_label != blank -%}
    <a
      {% if block.settings.button_link == blank %}
        role="link" aria-disabled="true"
      {% else %}
        href="{{ block.settings.button_link }}"
      {% endif %}
      class="{{ block.settings.button_style }}"
    >
      {{- block.settings.button_label -}}
    </a>
  {%- endif -%}
</div>

{% schema %}
{
  "name": "Text + button",
  "class": "tw-h-full",
  "settings": [
    {
      "type": "richtext",
      "id": "richtext",
      "label": "Text"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "t:sections.rich-text.blocks.buttons.settings.button_label_1.default",
      "label": "t:sections.rich-text.blocks.buttons.settings.button_label_1.label",
      "info": "t:sections.rich-text.blocks.buttons.settings.button_label_1.info"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.rich-text.blocks.buttons.settings.button_link_1.label"
    },
    {
      "type": "select",
      "id": "button_style",
      "options": [
        {
          "value": "button button--primary",
          "label": "Solid"
        },
        {
          "value": "button button--secondary",
          "label": "Outline"
        },
        {
          "value": "link underlined-link",
          "label": "Link"
        },
        {
          "value": "button button-1",
          "label": "Custom 1"
        },
        {
          "value": "button button-2",
          "label": "Custom 2"
        },
        {
          "value": "button button-3",
          "label": "Custom 3"
        },
        {
          "value": "button button-4",
          "label": "Custom 4"
        },
        {
          "value": "button button-5",
          "label": "Custom 5"
        }
      ],
      "default": "button button--primary",
      "label": "First button style"
    },
    {
      "type": "text",
      "id": "custom_classes",
      "label": "Block custom classes"
    }
  ]
}
{% endschema %}
