<div class="{{ block.settings.custom_css }}">
  {{ block.settings.liquid }}
</div>

{% schema %}
{
  "name": "Liquid",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "liquid",
      "id": "liquid",
      "label": "Liquid"
    },
    {
      "type": "header",
      "content": "Advanced settings (optional)"
    },
    {
      "type": "text",
      "id": "custom_css",
      "label": "Block custom CSS classes",
      "info": "Use this field to set padding, alignment etc. Classes already present are: tw:h-full"
    }
  ],
  "presets": [{ "name": "Liquid" }]
}
{% endschema %}
