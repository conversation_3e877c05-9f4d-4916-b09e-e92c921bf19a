{% liquid
  assign media_aspect_ratio = section.settings.image_ratio | default: settings.card_image_ratio
  assign image_shape = section.settings.image_shape | default: settings.card_image_shape
  assign quick_add_behavior = section.settings.quick_add_behavior | default: settings.card_quick_add_behavior
  assign card_heading_font_size = section.settings.card_heading_font_size | default: settings.card_heading_font_size  
  assign show_secondary_image = settings.card_show_secondary_image
  if section.settings.show_secondary_image == "true"
    assign show_secondary_image = true
  elsif section.settings.show_secondary_image == "false"
    assign show_secondary_image = false
  endif
  assign show_vendor = settings.card_show_vendor
  if section.settings.show_vendor == "true"
    assign show_vendor = true
  elsif section.settings.show_vendor == "false"
    assign show_vendor = false
  endif
  assign show_rating = settings.card_show_rating
  if section.settings.show_rating == "true"
    assign show_rating = true
  elsif section.settings.show_rating == "false"
    assign show_rating = false
  endif
  assign show_alternative_title = settings.card_show_alternative_title
  if section.settings.show_alternative_title == "true"
    assign show_alternative_title = true
  elsif section.settings.show_alternative_title == "false"
    assign show_alternative_title = false
  endif
  assign show_card_product_custom_field = settings.card_show_card_product_custom_field
  if section.settings.show_card_product_custom_field == "true"
    assign show_card_product_custom_field = true
  elsif section.settings.show_card_product_custom_field == "false"
    assign show_card_product_custom_field = false
  endif
%}

{% if image_shape == 'blob' %}
  {{ 'mask-blobs.css' | asset_url | stylesheet_tag }}
{%- endif -%}

{%- unless block.settings.quick_add == 'none' -%}
  {{ 'quick-add.css' | asset_url | stylesheet_tag }}
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
{%- endunless -%}

{%- if block.settings.quick_add == 'standard' -%}
  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{%- if block.settings.quick_add == 'bulk' -%}
  <script src="{{ 'quick-add-bulk.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'quantity-popover.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'price-per-item.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'quick-order-list.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{% render 'card-product',
    card_product: block.settings.product,
    section_id: section.id,
    lazy_load: true,
    quick_add: block.settings.quick_add,
    quick_add_button_style: block.settings.quick_add_button_style,
    media_aspect_ratio: media_aspect_ratio,
    image_shape: image_shape,
    show_secondary_image: show_secondary_image,
    show_vendor: show_vendor,
    show_rating: show_rating,
    quick_add_behavior: quick_add_behavior,
    card_heading_font_size: card_heading_font_size,
    show_alternative_title: show_alternative_title,
    show_card_product_custom_field: show_card_product_custom_field
  %}
{% schema %}
{
  "name": "Product card",
  "settings": [
    {
      "type": "product",
      "id": "product",
      "label": "Product"
    },
    {
      "type": "select",
      "id": "quick_add",
      "default": "none",
      "label": "t:sections.main-collection-product-grid.settings.quick_add.label",
      "info": "t:sections.main-collection-product-grid.settings.quick_add.info",
      "options": [
        {
          "value": "none",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_1"
        },
        {
          "value": "standard",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_2"
        },
        {
          "value": "bulk",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_3"
        }
      ]
    },
    {
      "type": "select",
      "id": "quick_add_button_style",
      "options": [
        {
          "value": "button button--primary",
          "label": "Solid"
        },
        {
          "value": "button button--secondary",
          "label": "Outline"
        },
        {
          "value": "link underlined-link",
          "label": "Link"
        },
        {
          "value": "button button-1",
          "label": "Custom 1"
        },
        {
          "value": "button button-2",
          "label": "Custom 2"
        },
        {
          "value": "button button-3",
          "label": "Custom 3"
        },
        {
          "value": "button button-4",
          "label": "Custom 4"
        },
        {
          "value": "button button-5",
          "label": "Custom 5"
        }
      ],
      "default": "button button--primary",
      "label": "Quick add button style"
    },
    {
      "type": "header",
      "content": "Optional: Product card overrides"
    },
    {
      "type": "paragraph",
      "content": "Product cards are configured in theme settings. To override values for this block only, use the inputs below."
    },
    {
      "type": "text",
      "id": "image_ratio",
      "label": "t:sections.featured-collection.settings.image_ratio.label",
      "info": "Options: square, portrait, adapt"
    },
    {
      "type": "text",
      "id": "image_shape",
      "label": "Image shape",
      "info": "Options: default, arch, blob, chevronleft, chevronright, diamond, parallelogram, round"
    },
    {
      "type": "text",
      "id": "show_secondary_image",
      "label": "t:sections.featured-collection.settings.show_secondary_image.label",
      "info": "Options: true, false"
    },
    {
      "type": "text",
      "id": "card_heading_font_size",
      "label": "Card heading font size",
      "info": "Options: h1, h2, h3, h4, h5, h6 etc."
    },
    {
      "type": "text",
      "id": "show_vendor",
      "label": "t:sections.featured-collection.settings.show_vendor.label",
      "info": "Options: true, false"
    },
    {
      "type": "text",
      "id": "show_rating",
      "label": "t:sections.featured-collection.settings.show_rating.label",
      "info": "Options: true, false"
    },
    {
      "type": "text",
      "id": "quick_add_behavior",
      "label": "Quick add behavior"
    },
    {
      "type": "text",
      "id": "show_alternative_title",
      "label": "Show alternative title",
      "info": "Options: true, false"
    },
    {
      "type": "text",
      "id": "show_card_product_custom_field",
      "label": "Show custom field",
      "info": "Options: true, false"
    }
  ],
  "presets": [{ "name": "Product card" }]
}
{% endschema %}
