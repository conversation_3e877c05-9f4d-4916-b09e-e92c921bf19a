<div class="tw:mt-4 {{ block.settings.custom_css }}">
  {% if block.settings.button_1_text %}
    <a href="{{ block.settings.button_1_url }}" class="{{ block.settings.button_1_style }} {{ block.settings.button_1_css }}" {{ block.settings.button_1_advanced }}>{{ block.settings.button_1_text }}</a>
  {% endif %}
  {% if block.settings.button_2_text %}
    <a href="{{ block.settings.button_2_url }}" class="{{ block.settings.button_2_style }} {{ block.settings.button_2_css }}" {{ block.settings.button_2_advanced }}>{{ block.settings.button_2_text }}</a>
  {% endif %}
</div>

{% schema %}
{
  "name": "Buttons",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Button 1"
    },
    {
      "type": "text",
      "id": "button_1_text",
      "label": "Button text"
    },
    {
      "type": "url",
      "id": "button_1_url",
      "label": "Button url"
    },
    {
      "type": "select",
      "id": "button_1_style",
      "options": [
        {
          "value": "button button--primary",
          "label": "Solid"
        },
        {
          "value": "button button--secondary",
          "label": "Outline"
        },
        {
          "value": "link underlined-link",
          "label": "Link"
        },
        {
          "value": "button button-1",
          "label": "Custom 1"
        },
        {
          "value": "button button-2",
          "label": "Custom 2"
        },
        {
          "value": "button button-3",
          "label": "Custom 3"
        },
        {
          "value": "button button-4",
          "label": "Custom 4"
        },
        {
          "value": "button button-5",
          "label": "Custom 5"
        }
      ],
      "default": "button button--primary",
      "label": "Button style"
    },
    {
      "type": "text",
      "id": "button_1_css",
      "label": "Button additional CSS classes"
    },
    {
      "type": "text",
      "id": "button_1_attributes",
      "label": "Button additional <a> tag attributes"
    },
    {
      "type": "header",
      "content": "Button 2"
    },
    {
      "type": "text",
      "id": "button_2_text",
      "label": "Button text"
    },
    {
      "type": "url",
      "id": "button_2_url",
      "label": "Button url"
    },
    {
      "type": "select",
      "id": "button_2_style",
      "options": [
        {
          "value": "button button--primary",
          "label": "Solid"
        },
        {
          "value": "button button--secondary",
          "label": "Outline"
        },
        {
          "value": "link underlined-link",
          "label": "Link"
        },
        {
          "value": "button button-1",
          "label": "Custom 1"
        },
        {
          "value": "button button-2",
          "label": "Custom 2"
        },
        {
          "value": "button button-3",
          "label": "Custom 3"
        },
        {
          "value": "button button-4",
          "label": "Custom 4"
        },
        {
          "value": "button button-5",
          "label": "Custom 5"
        }
      ],
      "default": "button button--primary",
      "label": "Button style"
    },
    {
      "type": "text",
      "id": "button_2_css",
      "label": "Button additional CSS classes"
    },
    {
      "type": "text",
      "id": "button_2_attributes",
      "label": "Button additional <a> tag attributes"
    },
    {
      "type": "header",
      "content": "Advanced settings (optional)"
    },
    {
      "type": "text",
      "id": "custom_css",
      "label": "CSS classes for wrapper <div>"
    }
  ],
  "presets": [{ "name": "Buttons" }]
}
{% endschema %}