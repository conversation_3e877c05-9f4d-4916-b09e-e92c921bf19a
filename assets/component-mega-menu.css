.mega-menu {
  position: static;
}

.mega-menu__content {
  background-color: rgb(var(--color-background));
  border-left: 0;
  border-radius: 0;
  border-right: 0;
  left: 0;
  overflow-y: auto;
  padding-bottom: 3rem;
  padding-top: 3rem;
  position: absolute;
  right: 0;
  top: 100%;
}

.shopify-section-header-sticky .mega-menu__content {
  max-height: calc(100vh - var(--header-bottom-position-desktop, 20rem) - 4rem);
}

.header-wrapper--border-bottom .mega-menu__content {
  border-top: 0;
}

/* bs-add
  -for feature/transparent-header 
*/
.mega-menu[open] .mega-menu__content {
  animation: var(--duration-short) animateMenuOpen var(--duration-default) ease;
  animation-fill-mode: both;
}

/* bs-mod
  -media query for feature/transparent-header 
*/
@media (prefers-reduced-motion) {
  .js .mega-menu__content {
    opacity: 0;
    transform: translateY(-1.5rem);
  }

  .mega-menu[open] .mega-menu__content {
    opacity: 1;
    transform: translateY(0);
  }
}

/* bs-mod
  - removed grid layout, using grid classes instead
*/
.mega-menu__list {
  list-style: none;
}

/* bs-mod
  - removed details from product cards in mega menu
*/
.mega-menu__list .product-card-wrapper .card-pricing,
.mega-menu__list .product-card-wrapper .card-information {
  display: none;
}

/* bs-mod
  - remove transparency for fonts
  - mega menu block text set to match other links
*/
.mega-menu__list .full-unstyled-link,
.mega-menu__link {
  color: rgba(var(--color-foreground), 1);
  display: block;
  line-height: calc(1 + 0.3 / var(--font-body-scale));
  padding-bottom: 0.6rem;
  padding-top: 0.6rem;
  text-decoration: none;
  transition: text-decoration var(--duration-short) ease;
  word-wrap: break-word;
}

/* bs-mod
  - added header font treatments to sub-menu headings and block cards
*/
.mega-menu__link--level-2,
.mega-menu__list .card__heading {
  font-family: var(--font-heading-family);
  font-style: var(--font-heading-style);
  font-weight: var(--font-heading-weight);
  font-size: inherit;
  letter-spacing: inherit;
}

/* bs-fix
  - remove hover styles for empty sub-menu headers links
*/
.mega-menu__link.mega-menu__link--level-2[href="#"]:hover {
  text-decoration: none;
  cursor: default;
}

.header--top-center .mega-menu__list {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  column-gap: 0;
}

.header--top-center .mega-menu__list > li {
  width: 16%;
  padding-right: 2.4rem;
}

.mega-menu__link:hover,
.mega-menu__link--active {
  color: rgb(var(--color-foreground));
  text-decoration: underline;
}

.mega-menu__link--active:hover {
  text-decoration-thickness: 0.2rem;
}

.mega-menu .mega-menu__list--condensed {
  display: block;
}

.mega-menu__list--condensed .mega-menu__link {
  font-weight: normal;
}
