.footer {
  border-top: 0.1rem solid rgba(var(--color-foreground), 0.08);
}

.footer:not(.color-scheme-1) {
  border-top: none;
}

.footer__content-top {
  padding-bottom: 5rem;
  display: block;
}

@media screen and (max-width: 749px) {
  /* bs-del 
    - .footer .grid removed as footer works off of grid and spanning feature
  */
  
  .footer-block.grid__item {
    padding: 0;
    /* bs-mod 
      - margin is now set on row (top or bottom)
    */

    /* bs-del 
      - .footer-block.grid__item:first-child removed as footer works off of grid and spanning feature
    */
  }

  .footer__content-top {
    padding-bottom: 3rem;
    /* bs-mod
      - adjusted side padding
    */
    padding-left: calc(1rem / var(--font-body-scale));
    padding-right: calc(1rem / var(--font-body-scale));
  }

  /* bs-mod 
    - gaps set now on row
    - alignment for mobile social set here now
  */

  .footer__content-top .grid {
    row-gap: 2rem;
  }

  .footer-block .footer__list-social.content--mobile-left,
  .footer-block .footer__localization.content--mobile-left {
    justify-content: flex-start;
  }

  .footer-block .footer__list-social.content--mobile-center,
  .footer-block .footer__localization.content--mobile-center,
  .content--mobile-center .footer-block__details-content .list-menu__item {
    justify-content: center;
  }

  .footer-block .footer__list-social.content--mobile-right,
  .footer-block .footer__localization.content--mobile-right,
  .content--mobile-right .footer-block__details-content .list-menu__item {
    justify-content: flex-end;
  }
}

@media screen and (min-width: 750px) {
  .footer__content-top .grid {
    row-gap: 6rem;
    margin-bottom: 0;
  }

  /* bs-mod 
    - alignment for desktop social set here now
  */

  .footer-block .footer__list-social.content--desktop-left,
  .footer-block .footer__localization.content--desktop-left {
    justify-content: flex-start;
  }

  .footer-block .footer__list-social.content--desktop-center,
  .footer-block .footer__localization.content--desktop-center {
    justify-content: center;
  }

  .footer-block .footer__list-social.content--desktop-right,
  .footer-block .footer__localization.content--desktop-right {
    justify-content: flex-end;
  }
}

.footer__content-bottom {
  border-top: solid 0.1rem rgba(var(--color-foreground), 0.08);
  padding-top: 3rem;
}

.footer__content-bottom:only-child {
  border-top: 0;
}

.footer__content-bottom-wrapper {
  display: flex;
  width: 100%;
}

@media screen and (max-width: 749px) {
  .footer__content-bottom {
    flex-wrap: wrap;
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
    row-gap: 1.5rem;
  }

  .footer__content-bottom-wrapper {
    flex-wrap: wrap;
    row-gap: 1.5rem;
    justify-content: center;
  }

  .footer__content-bottom.scroll-trigger.animate--slide-in {
    animation: none;
    opacity: 1;
    transform: inherit;
  }
}

.footer__localization:empty + .footer__column--info {
  align-items: center;
}

/* bs-mod 
  - set height here so items can be pushed to bottom as needed
*/
.footer__column--info {
  align-items: center;
  height: 100%;
}

@media screen and (max-width: 749px) {
  .footer__localization:empty + .footer__column {
    padding-top: 1.5rem;
  }
}

.footer__column {
  width: 100%;
  align-items: flex-end;
}

.footer__column--info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* bs-del
    - removed side padding
  */
}

@media screen and (min-width: 750px) {
  .footer__column--info {
    /* bs-del
      - removed side padding
    */
    align-items: flex-end;
  }
}

.footer-block:only-child:last-child {
  text-align: center;
  max-width: 76rem;
  margin: 0 auto;
}

@media screen and (min-width: 750px) {
  .footer-block {
    display: block;
    margin-top: 0;
  }
}

.footer-block:empty {
  display: none;
}

/* bs-del 
  - .footer-block--newsletter removed newsletter formatting as not needed with new footer grid span setup
  - .footer-block--newsletter:only-child removed newsletter formatting as not needed with new footer grid span setup
  - .footer-block--newsletter > * removed newsletter formatting as not needed with new footer grid span setup
*/
@media screen and (max-width: 749px) {
  .footer-block.footer-block--menu:only-child {
    text-align: left;
  }

  /* bs-add
    - remove gap for accordions
    - borders between
  */
  .footer-block:has(+ .footer-block--link_list) .accordion {
    margin-bottom: -1rem;
    margin-top: -1rem;
    border-top-color: rgba(var(--color-foreground), 0.08);
    border-bottom-color: transparent;
  }
  .footer-block:not(:has(+ .footer-block--link_list)) .accordion {
    margin-top: -1rem;
  }
}

/* bs-del 
  - .footer-block--newsletter removed newsletter formatting as not needed with new footer grid span setup
*/

.footer-block__heading {
  margin-bottom: 2rem;
  margin-top: 0;
  font-size: calc(var(--font-heading-scale) * 1.6rem);
}

@media screen and (min-width: 990px) {
  .footer-block__heading {
    font-size: calc(var(--font-heading-scale) * 1.8rem);
  }
}

/* bs-del 
  - .footer__list-social:empty, .footer-block--newsletter:empty removed newsletter formatting as not needed with new footer grid span setup
  - .footer__list-social.list-social:only-child removed newsletter formatting as not needed with new footer grid span setup
  - .footer-block__newsletter removed newsletter formatting as not needed with new footer grid span setup
*/
.newsletter-form__field-wrapper {
  max-width: 36rem;
}

.footer__follow-on-shop {
  display: flex;
  /* bs-del
    - 'text-align: center;' removed newsletter formatting as not needed with new footer grid span setup
  */
}

@media screen and (min-width: 750px) {
  /* bs-mod
    - newsletter alignment now done here to match new grid span setup
  */
  .content--desktop-left .footer-block__newsletter .newsletter-form {
    margin: 0;
  }

  .content--desktop-right .footer-block__newsletter .newsletter-form {
    margin-left: auto;
    margin-right: 0;
  }

  .content--desktop-center .footer-block__newsletter .newsletter-form {
    margin-left: auto;
    margin-right: auto;
  }

  .footer__follow-on-shop {
    margin-bottom: 0.4rem;
  }
  /* Follow on shop is the first button but it has siblings*/
  .footer__follow-on-shop:first-child:not(:last-child) {
    justify-content: flex-start;
    margin-right: auto;
    text-align: left;
  }

  /*
    All three components are present, email, Follow on Shop, and social icons.
    Moves the FoS button next to the social icons so they appear grouped together
  */
  .footer__follow-on-shop:not(:first-child):not(:last-child) {
    justify-content: flex-end;
    text-align: right;
  }
}

/* bs-del 
  - .footer-block__newsletter + .footer__list-social removed newsletter formatting as not needed with new footer grid span setup
*/ 

@media screen and (max-width: 749px) {
  /* bs-mod
    - newsletter alignment now done here to match new grid span setup
  */
  .content--mobile-left .footer-block__newsletter .newsletter-form {
    margin: 0;
  }

  .content--mobile-right .footer-block__newsletter .newsletter-form {
    margin-left: auto;
    margin-right: 0;
  }

  .content--mobile-center .footer-block__newsletter .newsletter-form {
    margin-left: auto;
    margin-right: auto;
  }
}

.footer__localization {
  display: flex;
  flex-direction: row;
  /* bs-del
    - alignment handled via utlitiy classes
  */
  align-content: center;
  flex-wrap: wrap;
  padding: 1rem 1rem 0;
}

.footer__localization:empty {
  display: none;
}

.footer__localization .h2 {
  margin: 1rem 1rem 0.5rem;
  color: rgba(var(--color-foreground), 0.75);
}

@media screen and (min-width: 750px) {
  .footer__localization {
    padding: 0.4rem 0;
    /* bs-del
      - alignment handled via utlitiy classes
    */
  }

  .footer__localization .h2 {
    margin: 1rem 0 0;
  }
}

@media screen and (min-width: 750px) {
  .footer__payment {
    margin-top: 1.5rem;
  }
}

@media screen and (max-width: 749px) {
  .footer__payment .list-payment.content--mobile-left {
    justify-content: flex-start;
  }

  .footer__payment .list-payment.content--mobile-right {
    justify-content: center;
  }

  .footer__payment .list-payment.content--mobile-right {
    justify-content: flex-end;
  }
}

@media screen and (min-width: 750px) {
  .footer__payment .list-payment.content--desktop-left {
    justify-content: flex-start;
  }

  .footer__payment .list-payment.content--desktop-right {
    justify-content: center;
  }

  .footer__payment .list-payment.content--desktop-right {
    justify-content: flex-end;
  }
}

@media screen and (min-width: 750px) {
  .footer__payment {
    margin-top: 1.5rem;
  }
}

.footer__content-bottom-wrapper--center {
  justify-content: center;
}

.footer__payment + .footer__copyright {
  /* 
    bs-del
    - removed as controlled by footer alignment 
    - only add margin if payment icons are present
  */
  margin-top: 1.5rem;
}

@keyframes appear-down {
  0% {
    opacity: 0;
    margin-top: -1rem;
  }
  100% {
    opacity: 1;
    margin-top: 0;
  }
}

.footer-block__details-content {
  margin-bottom: 4rem;
}

@media screen and (min-width: 750px) {
  .footer-block__details-content {
    margin-bottom: 0;
  }

  .footer-block__details-content > p,
  .footer-block__details-content > li {
    padding: 0;
  }

  .footer-block:only-child li {
    display: inline;
  }

  .footer-block__details-content > li:not(:last-child) {
    margin-right: 1.5rem;
  }
}

.footer-block__details-content .list-menu__item--link,
.copyright__content a {
  /* bs-mod
    - remove transparency for links
  */
  color: rgba(var(--color-foreground), 1);
}

.footer-block__details-content .list-menu__item--active {
  transition: text-decoration-thickness var(--duration-short) ease;
  color: rgb(var(--color-foreground));
}

@media screen and (min-width: 750px) {
  .footer-block__details-content .list-menu__item--link:hover,
  .copyright__content a:hover {
    color: rgb(var(--color-foreground));
    text-decoration: underline;
    text-underline-offset: 0.3rem;
  }

  .footer-block__details-content .list-menu__item--active:hover {
    text-decoration-thickness: 0.2rem;
  }
}

@media screen and (max-width: 989px) {
  .footer-block__details-content .list-menu__item--link {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

@media screen and (min-width: 750px) {
  .footer-block__details-content .list-menu__item--link {
    display: inline-block;
    font-size: 1.4rem;
  }

  .footer-block__details-content > :first-child .list-menu__item--link {
    padding-top: 0;
  }
}

.footer-block-image {
  display: flex;
}

.footer-block-image.left {
  justify-content: flex-start;
}

.footer-block-image.center {
  justify-content: center;
}

.footer-block-image.right {
  justify-content: flex-end;
}

@media screen and (max-width: 749px) {
  .footer-block-image,
  .footer-block-image.left,
  .footer-block-image.center,
  .footer-block-image.right {
    justify-content: center;
  }
}

.footer-block__image-wrapper {
  margin-bottom: 2rem;
  overflow: hidden !important;
}

.footer-block__image-wrapper img {
  display: block;
  height: auto;
  max-width: 100%;
}

.footer-block__brand-info {
  text-align: left;
}

.footer-block:only-child .footer-block__brand-info {
  text-align: center;
}

.footer-block:only-child > .footer-block__brand-info > .footer-block__image-wrapper {
  margin-left: auto;
  margin-right: auto;
}

.footer-block-image > img,
.footer-block__brand-info > img {
  height: auto;
}

/* 
  bs-del
  - removed .footer-block:only-child as controlled by footer alignment 
*/

.footer-block__details-content .placeholder-svg {
  max-width: 20rem;
}

.copyright__content {
  font-size: 1.1rem;
}

.copyright__content a {
  color: currentColor;
  text-decoration: none;
}

ul.policies {
  /* 
    bs-mod
    - now show on it's own line
    - removed uneeded left padding
    - added margin top for propper spacing
  */
  display: block;
  margin-top: 1rem;
  padding-left: 0;
}

/* 
  bs-mod
  - fix for alignment issues 
*/
.footer-block .policies li {
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

/* 
  bs-mod
  - now show on it's own line, so do not need first bullet
*/
.policies li:not(:first-of-type)::before {
  content: '\00B7';
  padding: 0 0.8rem;
}

.policies li a {
  /* bs-mod
    - removed padding for links
  */
  display: block;
}

/* bs-mod
  - removed padding for links
*/

@keyframes animateLocalization {
  0% {
    opacity: 0;
    transform: translateY(0);
  }

  100% {
    opacity: 1;
    transform: translateY(-1rem);
  }
}

/* check for flexbox gap in older Safari versions */
@supports not (inset: 10px) {
  @media screen and (max-width: 749px) {
    .footer .grid {
      margin-left: 0;
    }
  }

  @media screen and (min-width: 750px) {
    .footer__content-top .grid {
      margin-left: -3rem;
    }

    .footer__content-top .grid__item {
      padding-left: 3rem;
    }
  }
}